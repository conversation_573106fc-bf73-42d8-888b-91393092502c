from pydantic import BaseModel
from typing import List
class Role(BaseModel):
    code: str
    name: str
    description: str = ""

class Level(BaseModel):
    id: int
    name: str
    description: str = ""
    tag: str = ""
    color: str = "#3B82F6"

class Staff(BaseModel):
    job_no: str
    name: str
    role_code: str
    level: str = ""
    phone: str
    status: str
    remark: str = ""

# 角色数据已迁移到数据库，请通过API访问
roles: List[Role] = []

# 级别数据已迁移到数据库，请通过API访问
levels: List[Level] = []

# 员工数据已迁移到数据库，请通过API访问
staffs: List[Staff] = []
