"""
数据库查询工具示例
展示如何在内部工具中访问数据库
"""

import sqlite3
import json
import logging

# 初始化函数（可选）
def init(env):
    """
    工具初始化函数
    env: 包含 inputs, db_connection, logger 的字典
    """
    logger = env.get("logger")
    logger.info("数据库查询工具初始化")
    
    # 可以在这里进行一些初始化操作
    # 例如：创建临时表、设置数据库连接参数等

# 执行函数（必需）
def execute(inputs):
    """
    工具执行函数
    inputs: 包含输入参数的字典
    """
    # 获取输入参数
    query = inputs.get("query", "")
    table_name = inputs.get("table_name", "")
    
    # 验证必需参数
    if not query:
        return {
            "success": False,
            "error": "缺少必需参数: query"
        }
    
    try:
        # 连接数据库
        conn = sqlite3.connect("tmp/accounting.db")
        cursor = conn.cursor()
        
        # 执行查询
        if table_name:
            # 如果指定了表名，先检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                conn.close()
                return {
                    "success": False,
                    "error": f"表 '{table_name}' 不存在"
                }
        
        # 执行查询
        cursor.execute(query)
        
        # 获取结果
        if query.strip().upper().startswith(("SELECT", "PRAGMA")):
            # 查询语句，获取所有结果
            columns = [description[0] for description in cursor.description]
            rows = cursor.fetchall()
            
            # 将结果转换为字典列表
            result = []
            for row in rows:
                result.append(dict(zip(columns, row)))
            
            conn.close()
            return {
                "success": True,
                "data": {
                    "query": query,
                    "columns": columns,
                    "rows": result,
                    "row_count": len(result)
                }
            }
        else:
            # 非查询语句，返回影响的行数
            row_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            return {
                "success": True,
                "data": {
                    "query": query,
                    "row_count": row_count
                }
            }
            
    except sqlite3.Error as e:
        if 'conn' in locals():
            conn.close()
        return {
            "success": False,
            "error": f"数据库错误: {str(e)}"
        }
    except Exception as e:
        if 'conn' in locals():
            conn.close()
        return {
            "success": False,
            "error": f"执行错误: {str(e)}"
        }

# 工具元数据（用于前端显示）
TOOL_META = {
    "name": "数据库查询工具",
    "description": "执行SQL查询并返回结果",
    "inputs": [
        {
            "name": "query",
            "type": "string",
            "description": "要执行的SQL查询语句",
            "required": True
        },
        {
            "name": "table_name",
            "type": "string",
            "description": "要查询的表名（可选，用于验证）",
            "required": False
        }
    ],
    "outputs": [
        {
            "name": "result",
            "type": "object",
            "description": "查询结果，包含数据或影响的行数"
        }
    ]
}