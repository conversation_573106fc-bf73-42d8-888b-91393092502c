"""
表格结构配置文件
定义允许发送给AI的表格结构与描述
"""

# 表格配置
TABLE_SCHEMAS = {
    "roles": {
        "description": "系统角色表，存储不同角色的基本信息",
        "fields": {
            "code": {"type": "String", "description": "角色代码，主键", "example": "admin, manager, staff"},
            "name": {"type": "String", "description": "角色名称", "example": "管理员, 经理, 员工"},
            "description": {"type": "String", "description": "角色描述", "example": "系统管理员角色, 部门经理角色"}
        },
        "relationships": {
            "staffs": "一个角色可以拥有多个员工，通过staff表中的role_code关联"
        }
    },
    "levels": {
        "description": "员工级别表，存储不同级别的基本信息",
        "fields": {
            "id": {"type": "Integer", "description": "级别ID，主键", "example": "1, 2, 3"},
            "name": {"type": "String", "description": "级别名称", "example": "初级, 中级, 高级"},
            "description": {"type": "String", "description": "级别描述", "example": "初级员工级别, 中级员工级别"},
            "tag": {"type": "String", "description": "标签", "example": "junior, middle, senior"},
            "color": {"type": "String", "description": "颜色标识", "example": "#3B82F6, #10B981, #F59E0B"}
        }
    },
    "staffs": {
        "description": "员工信息表，存储员工的基本信息和工作状态",
        "fields": {
            "id": {"type": "Integer", "description": "员工ID，主键，自增", "example": "1, 2, 3"},
            "job_no": {"type": "String", "description": "工号，唯一", "example": "EMP001, EMP002"},
            "name": {"type": "String", "description": "员工姓名", "example": "张三, 李四"},
            "role_code": {"type": "String", "description": "角色代码，外键关联roles表", "example": "admin, manager"},
            "level": {"type": "String", "description": "员工级别", "example": "初级, 中级"},
            "phone": {"type": "String", "description": "联系电话", "example": "***********"},
            "status": {"type": "String", "description": "员工状态", "example": "在职, 离职"},
            "remark": {"type": "String", "description": "备注信息", "example": "优秀员工"}
        },
        "relationships": {
            "roles": "员工属于某个角色，通过role_code关联roles表的code字段"
        }
    },
    "subject_accounts": {
        "description": "会计科目表，存储会计科目的层级结构和基本信息",
        "fields": {
            "code": {"type": "String", "description": "科目编码，主键", "example": "1001, 1002, 1601"},
            "name": {"type": "String", "description": "科目名称", "example": "现金, 银行存款, 固定资产"},
            "level": {"type": "Integer", "description": "科目级次", "example": "1, 2, 3"},
            "parent_code": {"type": "String", "description": "父级科目编码", "example": "1001, 1601"},
            "category": {"type": "String", "description": "科目类别", "example": "资产, 负债, 所有者权益"},
            "direction": {"type": "String", "description": "科目方向", "example": "借, 贷"},
            "aux": {"type": "String", "description": "辅助核算，逗号分隔", "example": "客户, 供应商"},
            "is_leaf": {"type": "Boolean", "description": "是否末级科目", "example": "true, false"},
            "status": {"type": "String", "description": "科目状态", "example": "启用, 停用"},
            "remark": {"type": "String", "description": "备注", "example": "现金科目"},
            "quantity": {"type": "Boolean", "description": "是否数量核算", "example": "true, false"}
        }
    },
    "companies": {
        "description": "公司信息表，存储公司的基本信息和设置",
        "fields": {
            "id": {"type": "Integer", "description": "公司ID，主键，自增", "example": "1, 2"},
            "name": {"type": "String", "description": "公司名称", "example": "XX科技有限公司"},
            "business_scope": {"type": "Text", "description": "经营范围", "example": "软件开发, 技术咨询"},
            "industry": {"type": "String", "description": "所属行业", "example": "IT, 制造业"},
            "company_size": {"type": "String", "description": "公司规模", "example": "小型, 中型, 大型"},
            "tax_id": {"type": "String", "description": "税号", "example": "91110000123456789X"},
            "accounting_standards": {"type": "String", "description": "会计准则", "example": "企业会计准则"},
            "parent_company_id": {"type": "Integer", "description": "母公司ID", "example": "1"},
            "company_type": {"type": "String", "description": "公司类型", "example": "parent, subsidiary"},
            "established_date": {"type": "Date", "description": "成立日期", "example": "2020-01-01"},
            "registered_capital": {"type": "DECIMAL", "description": "注册资本", "example": "1000000.00"},
            "status": {"type": "String", "description": "公司状态", "example": "active, inactive"},
            "created_at": {"type": "DateTime", "description": "创建时间", "example": "2020-01-01 10:00:00"},
            "updated_at": {"type": "DateTime", "description": "更新时间", "example": "2020-01-01 10:00:00"}
        }
    },
    "vouchers": {
        "description": "会计凭证表，存储会计凭证的基本信息",
        "fields": {
            "id": {"type": "Integer", "description": "凭证ID，主键，自增", "example": "1, 2"},
            "voucher_no": {"type": "String", "description": "凭证号", "example": "记001, 记002"},
            "date": {"type": "Date", "description": "凭证日期", "example": "2023-01-01"},
            "total_debit": {"type": "DECIMAL", "description": "借方合计", "example": "1000.00"},
            "total_credit": {"type": "DECIMAL", "description": "贷方合计", "example": "1000.00"}
        },
        "relationships": {
            "voucher_entries": "一个凭证包含多个分录，通过voucher_entries表关联"
        }
    },
    "voucher_entries": {
        "description": "会计凭证分录表，存储会计凭证的详细分录信息",
        "fields": {
            "id": {"type": "Integer", "description": "分录ID，主键，自增", "example": "1, 2"},
            "voucher_id": {"type": "Integer", "description": "凭证ID，外键关联vouchers表", "example": "1, 2"},
            "summary": {"type": "String", "description": "摘要", "example": "购买办公用品"},
            "account_code": {"type": "String", "description": "科目编码", "example": "6601, 1002"},
            "account_name": {"type": "String", "description": "科目名称", "example": "管理费用, 银行存款"},
            "debit": {"type": "DECIMAL", "description": "借方金额", "example": "1000.00"},
            "credit": {"type": "DECIMAL", "description": "贷方金额", "example": "1000.00"}
        },
        "relationships": {
            "vouchers": "分录属于某个凭证，通过voucher_id关联vouchers表的id字段"
        }
    },
    "assets": {
        "description": "资产表，存储公司的固定资产信息",
        "fields": {
            "id": {"type": "Integer", "description": "资产ID，主键，自增", "example": "1, 2"},
            "name": {"type": "String", "description": "资产名称", "example": "电脑, 办公桌"},
            "type": {"type": "String", "description": "资产类别", "example": "电子设备, 办公家具"},
            "value": {"type": "DECIMAL", "description": "资产价值", "example": "5000.00"},
            "status": {"type": "String", "description": "资产状态", "example": "在用, 报废"},
            "remark": {"type": "String", "description": "备注", "example": "采购日期2023-01-01"}
        }
    }
}

def get_table_schema(table_name):
    """获取指定表格的配置信息"""
    return TABLE_SCHEMAS.get(table_name)

def get_all_table_schemas():
    """获取所有表格的配置信息"""
    return TABLE_SCHEMAS

def get_allowed_table_names():
    """获取允许发送给AI的表格名称列表"""
    return list(TABLE_SCHEMAS.keys())