from sqlalchemy import Column, String, Integer, <PERSON><PERSON>an, ForeignKey
from sqlalchemy.orm import relationship
from core.db import Base

class Role(Base):
    __tablename__ = "roles"
    code = Column(String, primary_key=True, comment="角色代码")
    name = Column(String, nullable=False, comment="角色名称")
    description = Column(String, default="", comment="角色描述")

class Level(Base):
    __tablename__ = "levels"
    id = Column(Integer, primary_key=True, comment="级别ID")
    name = Column(String, nullable=False, comment="级别名称")
    description = Column(String, default="", comment="级别描述")
    tag = Column(String, default="", comment="标签")
    color = Column(String, default="#3B82F6", comment="颜色")

class Staff(Base):
    __tablename__ = "staffs"
    id = Column(Integer, primary_key=True, autoincrement=True)
    job_no = Column(String, nullable=False, unique=True, comment="工号")
    name = Column(String, nullable=False, comment="员工姓名")
    role_code = Column(String, ForeignKey("roles.code"), nullable=False, comment="角色代码")
    level = Column(String, default="", comment="级别")
    phone = Column(String, nullable=False, comment="电话")
    status = Column(String, default="在职", comment="状态")
    remark = Column(String, default="", comment="备注")
    
    # 建立关系
    role = relationship("Role", backref="staffs")