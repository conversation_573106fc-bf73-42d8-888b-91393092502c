"""
LLM重试机制 - 处理LLM请求的重试逻辑，包括指数退避和错误处理
"""

import logging
import asyncio
import json
import time
from typing import Callable, Any, Dict, Optional, Union
from enum import Enum

logger = logging.getLogger(__name__)


class RetryErrorType(Enum):
    """重试错误类型"""
    RATE_LIMIT = "rate_limit"  # 速率限制
    TIMEOUT = "timeout"  # 超时
    SERVER_ERROR = "server_error"  # 服务器错误
    NETWORK_ERROR = "network_error"  # 网络错误
    UNKNOWN_ERROR = "unknown_error"  # 未知错误


class LLMRetryConfig:
    """LLM重试配置"""
    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        retryable_error_codes: list = None,
        retryable_status_codes: list = None
    ):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        
        # 默认可重试的错误码
        if retryable_error_codes is None:
            retryable_error_codes = [
                "tpm_rate_limit_exceeded",
                "rpm_rate_limit_exceeded",
                "rate_limit_exceeded",
                "timeout",
                "server_error"
            ]
        self.retryable_error_codes = retryable_error_codes
        
        # 默认可重试的HTTP状态码
        if retryable_status_codes is None:
            retryable_status_codes = [429, 500, 502, 503, 504]
        self.retryable_status_codes = retryable_status_codes


class LLMRetryHandler:
    """LLM重试处理器"""
    
    def __init__(self, config: LLMRetryConfig = None):
        self.config = config or LLMRetryConfig()
    
    def _should_retry(self, error: Exception, attempt: int) -> tuple[bool, RetryErrorType, float]:
        """
        判断是否应该重试
        
        Args:
            error: 异常对象
            attempt: 当前尝试次数
            
        Returns:
            (should_retry, error_type, delay)
        """
        if attempt >= self.config.max_retries:
            return False, RetryErrorType.UNKNOWN_ERROR, 0
        
        error_str = str(error)
        delay = self._calculate_delay(attempt)
        
        # 检查速率限制错误
        if "429" in error_str:
            # 尝试解析JSON错误响应
            try:
                if "{" in error_str and "}" in error_str:
                    json_start = error_str.find("{")
                    json_end = error_str.rfind("}") + 1
                    json_str = error_str[json_start:json_end]
                    error_data = json.loads(json_str)
                    
                    error_code = error_data.get("error", {}).get("code", "")
                    if error_code in self.config.retryable_error_codes:
                        logger.warning(f"检测到速率限制错误 ({error_code})，第 {attempt + 1} 次重试，等待 {delay:.2f} 秒")
                        return True, RetryErrorType.RATE_LIMIT, delay
            except (json.JSONDecodeError, KeyError, AttributeError):
                pass
            
            # 如果无法解析JSON，但包含429状态码，也进行重试
            logger.warning(f"检测到429状态码，第 {attempt + 1} 次重试，等待 {delay:.2f} 秒")
            return True, RetryErrorType.RATE_LIMIT, delay
        
        # 检查其他可重试的状态码
        for status_code in self.config.retryable_status_codes:
            if str(status_code) in error_str:
                logger.warning(f"检测到可重试状态码 {status_code}，第 {attempt + 1} 次重试，等待 {delay:.2f} 秒")
                return True, RetryErrorType.SERVER_ERROR, delay
        
        # 检查超时错误
        if any(keyword in error_str.lower() for keyword in ["timeout", "timed out", "time out"]):
            logger.warning(f"检测到超时错误，第 {attempt + 1} 次重试，等待 {delay:.2f} 秒")
            return True, RetryErrorType.TIMEOUT, delay
        
        # 检查网络错误
        if any(keyword in error_str.lower() for keyword in ["connection", "network", "connect"]):
            logger.warning(f"检测到网络错误，第 {attempt + 1} 次重试，等待 {delay:.2f} 秒")
            return True, RetryErrorType.NETWORK_ERROR, delay
        
        logger.error(f"不可重试的错误: {error_str}")
        return False, RetryErrorType.UNKNOWN_ERROR, 0
    
    def _calculate_delay(self, attempt: int) -> float:
        """
        计算退避延迟时间
        
        Args:
            attempt: 当前尝试次数（从0开始）
            
        Returns:
            延迟时间（秒）
        """
        # 指数退避算法：delay = base_delay * (exponential_base ^ attempt)
        delay = self.config.base_delay * (self.config.exponential_base ** attempt)
        
        # 添加随机抖动（±25%），避免多个客户端同时重试
        import random
        jitter = delay * 0.25 * (random.random() * 2 - 1)
        delay += jitter
        
        # 确保不超过最大延迟
        return min(delay, self.config.max_delay)
    
    async def execute_with_retry(
        self,
        func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """
        执行函数并自动重试
        
        Args:
            func: 要执行的函数
            *args: 函数位置参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            最后一次尝试的异常
        """
        last_error = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                result = await func(*args, **kwargs)
                if attempt > 0:
                    logger.info(f"LLM请求在第 {attempt + 1} 次尝试后成功")
                return result
                
            except Exception as e:
                last_error = e
                # Special handling for UTF-8 decoding errors
                if isinstance(e, UnicodeDecodeError) and 'utf-8' in str(e):
                    logger.error(f"检测到UTF-8解码错误: {str(e)}")
                    # UTF-8 decoding errors are typically not retryable as they indicate data corruption
                    should_retry, error_type, delay = False, RetryErrorType.UNKNOWN_ERROR, 0
                else:
                    should_retry, error_type, delay = self._should_retry(e, attempt)
                
                if not should_retry:
                    break
                
                if attempt < self.config.max_retries:
                    logger.info(f"等待 {delay:.2f} 秒后进行第 {attempt + 2} 次重试...")
                    await asyncio.sleep(delay)
        
        # 所有重试都失败了
        logger.error(f"LLM请求在 {self.config.max_retries + 1} 次尝试后仍然失败: {last_error}")
        raise last_error


# 全局重试处理器实例
_default_retry_handler = None


def get_retry_handler(config: LLMRetryConfig = None) -> LLMRetryHandler:
    """获取全局重试处理器实例"""
    global _default_retry_handler
    if _default_retry_handler is None:
        _default_retry_handler = LLMRetryHandler(config)
    return _default_retry_handler


def set_retry_handler(handler: LLMRetryHandler):
    """设置全局重试处理器"""
    global _default_retry_handler
    _default_retry_handler = handler