"""
RAG管理器 - 管理单据审核相关的规章制度数据
"""

import logging
import os
import uuid
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import json

from core.logging_config import log_rag_operation
from core.config import config

logger = logging.getLogger(__name__)

class RAGManager:
    """RAG管理器"""
    
    def __init__(self, collection_name: str = "audit_regulations"):
        """初始化RAG管理器"""
        logger.info(f"[RAG] 初始化RAG管理器，集合名称: {collection_name}")
        self.collection_name = collection_name
        self.client = None
        self.collection = None
        self.embedding_model = None
        self.embedding_dimension = 384  # 默认维度
        self._initialize_client()
        self._initialize_embedding_model()
    
    def _initialize_client(self):
        """初始化ChromaDB客户端"""
        logger.info("[RAG] 初始化ChromaDB客户端")
        try:
            import chromadb
            # 创建持久化客户端
            persist_directory = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "chroma_db")
            logger.debug(f"[RAG] ChromaDB持久化目录: {persist_directory}")
            os.makedirs(persist_directory, exist_ok=True)
            
            self.client = chromadb.PersistentClient(path=persist_directory)
            logger.debug("[RAG] ChromaDB客户端创建成功")
            
            # 获取或创建集合
            try:
                self.collection = self.client.get_collection(name=self.collection_name)
                logger.info(f"[RAG] 已获取现有集合: {self.collection_name}")
            except Exception:
                self.collection = self.client.create_collection(name=self.collection_name)
                logger.info(f"[RAG] 已创建新集合: {self.collection_name}")
                
        except ImportError:
            logger.error("[RAG] 未安装chromadb，请运行: pip install chromadb")
            raise ImportError("chromadb未安装")
        except Exception as e:
            logger.error(f"[RAG] 初始化ChromaDB客户端失败: {str(e)}", exc_info=True)
            raise
    
    def _initialize_embedding_model(self):
        """初始化嵌入模型"""
        logger.info("[RAG] 初始化嵌入模型")
        
        # 优先尝试使用OpenAI嵌入模型
        if config.is_openai_enabled():
            try:
                from langchain_openai import OpenAIEmbeddings
                self.embedding_model = OpenAIEmbeddings(
                    openai_api_key=config.OPENAI_API_KEY,
                    openai_api_base=config.OPENAI_BASE_URL
                )
                self.embedding_dimension = 1536  # OpenAI text-embedding-ada-002 的维度
                logger.info("[RAG] 成功初始化OpenAI嵌入模型")
                return
            except Exception as e:
                logger.warning(f"[RAG] OpenAI嵌入模型初始化失败: {str(e)}")
        
        # 回退到sentence-transformers
        try:
            from sentence_transformers import SentenceTransformer
            self.embedding_model = SentenceTransformer(config.SENTENCE_TRANSFORMER_MODEL, local_files_only=config.LOCAL_FILES_ONLY)
            self.embedding_dimension = 384  # 这个模型的维度
            logger.info(f"[RAG] 成功初始化SentenceTransformer嵌入模型: {config.SENTENCE_TRANSFORMER_MODEL}")
            return
        except Exception as e:
            logger.warning(f"[RAG] SentenceTransformer嵌入模型初始化失败: {str(e)}")
        
        # 如果都失败了，使用空embedding作为最后手段
        logger.warning("[RAG] 所有嵌入模型初始化失败，将使用空embedding")
        self.embedding_model = None
    
    def _generate_embedding(self, text: str) -> List[float]:
        """生成文本的嵌入向量"""
        # 检查文本是否为空或仅包含空白字符
        if not text or not text.strip():
            logger.warning("[RAG] 文本内容为空，返回空embedding")
            return [0.0] * self.embedding_dimension
        
        if not self.embedding_model:
            # 如果没有嵌入模型，返回空embedding
            logger.warning("[RAG] 使用空embedding")
            return [0.0] * self.embedding_dimension
        
        try:
            if hasattr(self.embedding_model, 'embed_query'):
                # LangChain风格的嵌入模型
                embedding = self.embedding_model.embed_query(text)
            else:
                # SentenceTransformer风格的嵌入模型
                embedding = self.embedding_model.encode(text).tolist()
            
            # 验证embedding不为空
            if not embedding or len(embedding) == 0:
                logger.error("[RAG] 生成的embedding为空，使用默认空embedding")
                return [0.0] * self.embedding_dimension
            
            logger.debug(f"[RAG] 成功生成embedding，维度: {len(embedding)}")
            return embedding
        except Exception as e:
            logger.error(f"[RAG] 生成embedding失败: {str(e)}", exc_info=True)
            # 失败时返回空embedding
            return [0.0] * self.embedding_dimension
    
    def add_documents(self, documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """添加文档到ChromaDB"""
        logger.info(f"[RAG] 开始添加 {len(documents)} 条文档到ChromaDB")
        log_rag_operation("添加文档", self.collection_name, results=documents)
        
        try:
            if not self.collection:
                logger.error("[RAG] ChromaDB集合未初始化")
                return {"success": False, "error": "ChromaDB集合未初始化"}
            
            ids = []
            embeddings = []
            metadatas = []
            documents_list = []
            skipped_docs = 0
            
            for i, doc in enumerate(documents):
                logger.debug(f"[RAG] 处理第 {i+1}/{len(documents)} 条文档")
                
                # 添加文档内容
                content = doc.get("content", "")
                
                # 跳过空内容文档
                if not content or not content.strip():
                    logger.warning(f"[RAG] 跳过空内容文档: {doc.get('title', '无标题')}")
                    skipped_docs += 1
                    continue
                
                # 生成唯一ID
                doc_id = str(uuid.uuid4())
                ids.append(doc_id)
                logger.debug(f"[RAG] 生成文档ID: {doc_id}")
                
                documents_list.append(content)
                logger.debug(f"[RAG] 文档内容长度: {len(content)} 字符")
                
                # 添加元数据
                metadata = {
                    "title": doc.get("title", ""),
                    "category": doc.get("category", ""),
                    "source": doc.get("source", ""),
                    "rule_type": doc.get("rule_type", ""),
                    "rule_description": doc.get("rule_description", ""),
                    "rule_content": doc.get("rule_content", ""),
                    "workflows": json.dumps(doc.get("workflows", []), ensure_ascii=False),
                    "created_at": doc.get("created_at", datetime.now().isoformat()),
                    "updated_at": datetime.now().isoformat()
                }
                metadatas.append(metadata)
                logger.debug(f"[RAG] 文档元数据: {metadata}")
                
                # 使用真实的embedding模型生成嵌入向量
                embedding = self._generate_embedding(content)
                
                # 验证embedding不为空
                if not embedding or len(embedding) == 0:
                    logger.warning(f"[RAG] 跳过空embedding文档: {doc.get('title', '无标题')}")
                    skipped_docs += 1
                    # 移除已添加的ID、元数据和文档内容
                    ids.pop()
                    metadatas.pop()
                    documents_list.pop()
                    continue
                
                embeddings.append(embedding)
            
            # 检查是否有有效文档需要添加
            if not ids:
                logger.warning("[RAG] 没有有效文档可添加到ChromaDB")
                return {
                    "success": True,
                    "message": f"没有有效文档可添加，跳过了 {skipped_docs} 条空文档",
                    "ids": [],
                    "skipped_count": skipped_docs
                }
            
            # 添加到集合
            logger.info("[RAG] 开始将文档添加到ChromaDB集合")
            self.collection.add(
                ids=ids,
                embeddings=embeddings,
                metadatas=metadatas,
                documents=documents_list
            )
            logger.info(f"[RAG] 成功添加 {len(ids)} 条文档到ChromaDB，跳过了 {skipped_docs} 条空文档")
            
            return {
                "success": True,
                "message": f"成功添加 {len(ids)} 条规章制度数据，跳过了 {skipped_docs} 条空文档",
                "ids": ids,
                "skipped_count": skipped_docs
            }
            
        except Exception as e:
            logger.error(f"[RAG] 添加文档到ChromaDB失败: {str(e)}", exc_info=True)
            log_rag_operation("添加文档", self.collection_name, error=str(e))
            return {"success": False, "error": str(e)}
    
    def search_documents(self, query: str, n_results: int = 5) -> Dict[str, Any]:
        """搜索相关文档"""
        logger.info(f"[RAG] 搜索相关文档，查询: {query}, 返回结果数: {n_results}")
        log_rag_operation("搜索文档", self.collection_name, query=query)
        
        try:
            if not self.collection:
                logger.error("[RAG] ChromaDB集合未初始化")
                return {"success": False, "error": "ChromaDB集合未初始化"}
            
            # 使用真实的embedding模型生成查询嵌入向量
            query_embedding = self._generate_embedding(query)
            
            # 搜索文档
            logger.debug("[RAG] 开始搜索文档")
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where={"content": {"$ne": ""}}  # 确保内容不为空
            )
            logger.debug("[RAG] 文档搜索完成")
            
            # 格式化结果
            documents = []
            for i in range(len(results["ids"][0])):
                doc = {
                    "id": results["ids"][0][i],
                    "content": results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "distance": results["distances"][0][i] if "distances" in results else None
                }
                documents.append(doc)
                logger.debug(f"[RAG] 搜索结果 {i+1}: ID={doc['id']}, 距离={doc['distance']}")
            
            logger.info(f"[RAG] 搜索完成，找到 {len(documents)} 条相关文档")
            log_rag_operation("搜索文档", self.collection_name, query=query, results=documents)
            
            return {
                "success": True,
                "data": documents,
                "count": len(documents)
            }
            
        except Exception as e:
            logger.error(f"[RAG] 搜索ChromaDB文档失败: {str(e)}", exc_info=True)
            log_rag_operation("搜索文档", self.collection_name, query=query, error=str(e))
            return {"success": False, "error": str(e)}
    
    def get_document(self, doc_id: str) -> Dict[str, Any]:
        """获取特定文档"""
        logger.info(f"[RAG] 获取特定文档，ID: {doc_id}")
        
        try:
            if not self.collection:
                logger.error("[RAG] ChromaDB集合未初始化")
                return {"success": False, "error": "ChromaDB集合未初始化"}
            
            # 获取文档
            logger.debug(f"[RAG] 开始获取文档，ID: {doc_id}")
            result = self.collection.get(ids=[doc_id])
            logger.debug("[RAG] 文档获取完成")
            
            if not result["ids"]:
                logger.warning(f"[RAG] 文档不存在，ID: {doc_id}")
                return {"success": False, "error": "文档不存在"}
            
            doc = {
                "id": result["ids"][0],
                "content": result["documents"][0],
                "metadata": result["metadatas"][0]
            }
            
            logger.info(f"[RAG] 成功获取文档，ID: {doc_id}")
            
            return {
                "success": True,
                "data": doc
            }
            
        except Exception as e:
            logger.error(f"[RAG] 获取ChromaDB文档失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def update_document(self, doc_id: str, document: Dict[str, Any]) -> Dict[str, Any]:
        """更新文档"""
        logger.info(f"[RAG] 更新文档，ID: {doc_id}")
        
        try:
            if not self.collection:
                logger.error("[RAG] ChromaDB集合未初始化")
                return {"success": False, "error": "ChromaDB集合未初始化"}
            
            # 检查文档是否存在
            logger.debug(f"[RAG] 检查文档是否存在，ID: {doc_id}")
            existing = self.collection.get(ids=[doc_id])
            if not existing["ids"]:
                logger.warning(f"[RAG] 文档不存在，ID: {doc_id}")
                return {"success": False, "error": "文档不存在"}
            
            # 更新文档
            content = document.get("content", "")
            metadata = {
                "title": document.get("title", ""),
                "category": document.get("category", ""),
                "source": document.get("source", ""),
                "workflows": json.dumps(document.get("workflows", []), ensure_ascii=False),
                "created_at": existing["metadatas"][0].get("created_at", datetime.now().isoformat()),
                "updated_at": datetime.now().isoformat()
            }
            
            # 使用真实的embedding模型生成嵌入向量
            embedding = self._generate_embedding(content)
            
            logger.debug(f"[RAG] 开始更新文档，ID: {doc_id}")
            self.collection.update(
                ids=[doc_id],
                embeddings=[embedding],
                metadatas=[metadata],
                documents=[content]
            )
            logger.info(f"[RAG] 成功更新文档，ID: {doc_id}")
            
            return {
                "success": True,
                "message": f"成功更新文档: {doc_id}"
            }
            
        except Exception as e:
            logger.error(f"[RAG] 更新ChromaDB文档失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def delete_document(self, doc_id: str) -> Dict[str, Any]:
        """删除文档"""
        logger.info(f"[RAG] 删除文档，ID: {doc_id}")
        
        try:
            if not self.collection:
                logger.error("[RAG] ChromaDB集合未初始化")
                return {"success": False, "error": "ChromaDB集合未初始化"}
            
            # 删除文档
            logger.debug(f"[RAG] 开始删除文档，ID: {doc_id}")
            self.collection.delete(ids=[doc_id])
            logger.info(f"[RAG] 成功删除文档，ID: {doc_id}")
            
            return {
                "success": True,
                "message": f"成功删除文档: {doc_id}"
            }
            
        except Exception as e:
            logger.error(f"[RAG] 删除ChromaDB文档失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def list_documents(self, limit: int = 100, offset: int = 0) -> Dict[str, Any]:
        """列出所有文档"""
        logger.info(f"[RAG] 列出所有文档，限制: {limit}, 偏移: {offset}")
        
        try:
            if not self.collection:
                logger.error("[RAG] ChromaDB集合未初始化")
                return {"success": False, "error": "ChromaDB集合未初始化"}
            
            # 获取所有文档
            logger.debug("[RAG] 开始获取所有文档")
            result = self.collection.get(
                limit=limit,
                offset=offset
            )
            logger.debug("[RAG] 文档获取完成")
            
            # 格式化结果
            documents = []
            for i in range(len(result["ids"])):
                doc = {
                    "id": result["ids"][i],
                    "content": result["documents"][i],
                    "metadata": result["metadatas"][i]
                }
                documents.append(doc)
            
            logger.info(f"[RAG] 成功列出 {len(documents)} 条文档")
            
            return {
                "success": True,
                "data": documents,
                "count": len(documents)
            }
            
        except Exception as e:
            logger.error(f"[RAG] 列出ChromaDB文档失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        logger.info("[RAG] 获取集合统计信息")
        
        try:
            if not self.collection:
                logger.error("[RAG] ChromaDB集合未初始化")
                return {"success": False, "error": "ChromaDB集合未初始化"}
            
            # 获取集合统计信息
            logger.debug("[RAG] 开始获取集合统计信息")
            count = self.collection.count()
            logger.debug("[RAG] 集合统计信息获取完成")
            
            return {
                "success": True,
                "data": {
                    "collection_name": self.collection_name,
                    "document_count": count
                }
            }
            
        except Exception as e:
            logger.error(f"[RAG] 获取ChromaDB集合统计信息失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def add_regulation_categories(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """添加分类规则到ChromaDB"""
        logger.info("[RAG] 开始添加分类规则到ChromaDB")
        log_rag_operation("添加分类规则", self.collection_name, results=parsed_data)
        
        try:
            if not self.collection:
                logger.error("[RAG] ChromaDB集合未初始化")
                return {"success": False, "error": "ChromaDB集合未初始化"}
            
            # 检查解析后的数据格式
            if "regulation_categories" not in parsed_data:
                logger.error("[RAG] 解析数据格式不正确，缺少regulation_categories字段")
                return {"success": False, "error": "解析数据格式不正确，缺少regulation_categories字段"}
            
            regulation_categories = parsed_data["regulation_categories"]
            if not isinstance(regulation_categories, list):
                logger.error("[RAG] regulation_categories字段不是列表类型")
                return {"success": False, "error": "regulation_categories字段不是列表类型"}
            
            logger.info(f"[RAG] 解析到 {len(regulation_categories)} 条分类规则")
            
            ids = []
            embeddings = []
            metadatas = []
            documents_list = []
            skipped_categories = 0
            
            # 为每个分类的每条规则创建单独的文档
            for i, category in enumerate(regulation_categories):
                logger.debug(f"[RAG] 处理第 {i+1}/{len(regulation_categories)} 条分类规则")
                
                # 构建分类内容
                category_type = category.get("type", "")
                description = category.get("description", "")
                rules = category.get("rules", [])
                
                # 为每条规则创建单独的文档
                for j, rule in enumerate(rules):
                    # 处理新旧格式的规则
                    if isinstance(rule, str):
                        # 旧格式：字符串
                        rule_content = rule.strip()
                        rule_workflows = []
                    elif isinstance(rule, dict):
                        # 新格式：对象
                        rule_content = rule.get("rule_content", "").strip()
                        rule_workflows = rule.get("workflows", [])
                    else:
                        continue
                    
                    if rule_content:  # 确保规则不为空
                        content = f"# {category_type}\n\n{description}\n\n## 规则\n\n{rule_content}"
                        
                        # 生成唯一ID
                        rule_id = str(uuid.uuid4())
                        ids.append(rule_id)
                        logger.debug(f"[RAG] 生成规则ID: {rule_id}")
                        
                        documents_list.append(content)
                        logger.debug(f"[RAG] 规则内容长度: {len(content)} 字符")
                        
                        # 添加元数据
                        metadata = {
                            "title": category_type,
                            "category": category_type,
                            "source": "规章制度解析",
                            "type": "regulation_rule",
                            "rule_type": category_type,
                            "rule_description": description,
                            "rule_content": rule_content,
                            "rule_index": j,
                            "workflows": json.dumps(rule_workflows, ensure_ascii=False),
                            "created_at": datetime.now().isoformat(),
                            "updated_at": datetime.now().isoformat()
                        }
                        metadatas.append(metadata)
                        logger.debug(f"[RAG] 规则元数据: {metadata}")
                        
                        # 使用真实的embedding模型生成嵌入向量
                        embedding = self._generate_embedding(content)
                        
                        # 验证embedding不为空
                        if not embedding or len(embedding) == 0:
                            logger.warning(f"[RAG] 跳过空embedding规则: {category_type} - 规则 {j+1}")
                            skipped_categories += 1
                            # 移除已添加的ID、元数据和文档内容
                            ids.pop()
                            metadatas.pop()
                            documents_list.pop()
                            continue
                        
                        embeddings.append(embedding)
                
                # 记录处理的规则数量
                logger.debug(f"[RAG] 分类 {category_type} 处理完成，共处理 {len(rules)} 条规则")
            
            # 检查是否有有效分类需要添加
            if not ids:
                logger.warning("[RAG] 没有有效分类规则可添加到ChromaDB")
                return {
                    "success": True,
                    "message": f"没有有效分类规则可添加，跳过了 {skipped_categories} 条空分类",
                    "ids": [],
                    "skipped_count": skipped_categories
                }
            
            # 添加到集合
            logger.info("[RAG] 开始将分类规则添加到ChromaDB集合")
            self.collection.add(
                ids=ids,
                embeddings=embeddings,
                metadatas=metadatas,
                documents=documents_list
            )
            logger.info(f"[RAG] 成功添加 {len(ids)} 条分类规则到ChromaDB，跳过了 {skipped_categories} 条空分类")
            
            return {
                "success": True,
                "message": f"成功添加 {len(ids)} 条分类规则数据，跳过了 {skipped_categories} 条空分类",
                "ids": ids,
                "skipped_count": skipped_categories
            }
            
        except Exception as e:
            logger.error(f"[RAG] 添加分类规则到ChromaDB失败: {str(e)}", exc_info=True)
            log_rag_operation("添加分类规则", self.collection_name, error=str(e))
            return {"success": False, "error": str(e)}
    
    def search_by_category(self, category: str, n_results: int = 5) -> Dict[str, Any]:
        """按分类搜索相关文档"""
        logger.info(f"[RAG] 按分类搜索相关文档，分类: {category}, 返回结果数: {n_results}")
        log_rag_operation("按分类搜索", self.collection_name, query=category)
        
        try:
            if not self.collection:
                logger.error("[RAG] ChromaDB集合未初始化")
                return {"success": False, "error": "ChromaDB集合未初始化"}
            
            # 使用真实的embedding模型生成查询嵌入向量
            query_embedding = self._generate_embedding(category)
            
            # 按分类搜索文档
            logger.debug(f"[RAG] 开始按分类搜索文档，分类: {category}")
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where={"category": category}  # 按分类筛选
            )
            logger.debug("[RAG] 分类搜索完成")
            
            # 格式化结果
            documents = []
            for i in range(len(results["ids"][0])):
                doc = {
                    "id": results["ids"][0][i],
                    "content": results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "distance": results["distances"][0][i] if "distances" in results else None
                }
                documents.append(doc)
                logger.debug(f"[RAG] 搜索结果 {i+1}: ID={doc['id']}, 距离={doc['distance']}")
            
            logger.info(f"[RAG] 分类搜索完成，找到 {len(documents)} 条相关文档")
            log_rag_operation("按分类搜索", self.collection_name, query=category, results=documents)
            
            return {
                "success": True,
                "data": documents,
                "count": len(documents)
            }
            
        except Exception as e:
            logger.error(f"[RAG] 按分类搜索ChromaDB文档失败: {str(e)}", exc_info=True)
            log_rag_operation("按分类搜索", self.collection_name, query=category, error=str(e))
            return {"success": False, "error": str(e)}
    
    def get_categories(self) -> Dict[str, Any]:
        """获取所有分类"""
        logger.info("[RAG] 获取所有分类")
        
        try:
            if not self.collection:
                logger.error("[RAG] ChromaDB集合未初始化")
                return {"success": False, "error": "ChromaDB集合未初始化"}
            
            # 获取所有文档
            logger.debug("[RAG] 开始获取所有文档以提取分类")
            result = self.collection.get()
            logger.debug("[RAG] 文档获取完成")
            
            # 提取所有分类
            categories = set()
            for i, metadata in enumerate(result["metadatas"]):
                category = metadata.get("category", "")
                if category:
                    categories.add(category)
                    logger.debug(f"[RAG] 提取分类 {i+1}: {category}")
            
            categories_list = list(categories)
            logger.info(f"[RAG] 成功获取 {len(categories_list)} 个分类")
            
            return {
                "success": True,
                "data": categories_list,
                "count": len(categories_list)
            }
            
        except Exception as e:
            logger.error(f"[RAG] 获取ChromaDB分类失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def add_chunked_regulation_document(self, parsed_data: Dict[str, Any], original_filename: str = "") -> Dict[str, Any]:
        """添加分块解析的规章制度文档到ChromaDB"""
        logger.info("[RAG] 开始添加分块解析的规章制度文档到ChromaDB")
        log_rag_operation("添加分块文档", self.collection_name, results=parsed_data)
        
        try:
            if not self.collection:
                logger.error("[RAG] ChromaDB集合未初始化")
                return {"success": False, "error": "ChromaDB集合未初始化"}
            
            # 检查解析后的数据格式
            if "regulation_categories" not in parsed_data:
                logger.error("[RAG] 解析数据格式不正确，缺少regulation_categories字段")
                return {"success": False, "error": "解析数据格式不正确，缺少regulation_categories字段"}
            
            regulation_categories = parsed_data["regulation_categories"]
            if not isinstance(regulation_categories, list):
                logger.error("[RAG] regulation_categories字段不是列表类型")
                return {"success": False, "error": "regulation_categories字段不是列表类型"}
            
            # 获取合并摘要信息（如果存在）
            merge_summary = parsed_data.get("merge_summary", {})
            chunk_count = merge_summary.get("total_chunks", 1)
            
            logger.info(f"[RAG] 解析到 {len(regulation_categories)} 条分类规则，来自 {chunk_count} 个文档块")
            
            ids = []
            embeddings = []
            metadatas = []
            documents_list = []
            skipped_categories = 0
            
            # 为每个分类的每条规则创建单独的文档
            for i, category in enumerate(regulation_categories):
                logger.debug(f"[RAG] 处理第 {i+1}/{len(regulation_categories)} 条分类规则")
                
                # 构建分类内容
                category_type = category.get("type", "")
                description = category.get("description", "")
                rules = category.get("rules", [])
                
                # 为每条规则创建单独的文档
                for j, rule in enumerate(rules):
                    # 处理新旧格式的规则
                    if isinstance(rule, str):
                        # 旧格式：字符串
                        rule_content = rule.strip()
                        rule_workflows = []
                    elif isinstance(rule, dict):
                        # 新格式：对象
                        rule_content = rule.get("rule_content", "").strip()
                        rule_workflows = rule.get("workflows", [])
                    else:
                        continue
                    
                    if rule_content:  # 确保规则不为空
                        content = f"# {category_type}\n\n{description}\n\n## 规则\n\n{rule_content}"
                        
                        # 生成唯一ID
                        rule_id = str(uuid.uuid4())
                        ids.append(rule_id)
                        logger.debug(f"[RAG] 生成规则ID: {rule_id}")
                        
                        documents_list.append(content)
                        logger.debug(f"[RAG] 规则内容长度: {len(content)} 字符")
                        
                        # 添加元数据
                        metadata = {
                            "title": category_type,
                            "category": category_type,
                            "source": original_filename or "规章制度文档解析",
                            "type": "regulation_rule",
                            "rule_type": category_type,
                            "rule_description": description,
                            "rule_content": rule_content,
                            "rule_index": j,
                            "chunk_count": chunk_count,
                            "workflows": json.dumps(rule_workflows, ensure_ascii=False),
                            "created_at": datetime.now().isoformat(),
                            "updated_at": datetime.now().isoformat()
                        }
                        metadatas.append(metadata)
                        logger.debug(f"[RAG] 规则元数据: {metadata}")
                        
                        # 使用真实的embedding模型生成嵌入向量
                        embedding = self._generate_embedding(content)
                        
                        # 验证embedding不为空
                        if not embedding or len(embedding) == 0:
                            logger.warning(f"[RAG] 跳过空embedding规则: {category_type} - 规则 {j+1}")
                            skipped_categories += 1
                            # 移除已添加的ID、元数据和文档内容
                            ids.pop()
                            metadatas.pop()
                            documents_list.pop()
                            continue
                        
                        embeddings.append(embedding)
                
                # 记录处理的规则数量
                logger.debug(f"[RAG] 分类 {category_type} 处理完成，共处理 {len(rules)} 条规则")
            
            # 检查是否有有效分类需要添加
            if not ids:
                logger.warning("[RAG] 没有有效分类规则可添加到ChromaDB")
                return {
                    "success": True,
                    "message": f"没有有效分类规则可添加，跳过了 {skipped_categories} 条空分类",
                    "ids": [],
                    "skipped_count": skipped_categories,
                    "chunk_info": {
                        "total_chunks": chunk_count,
                        "successful_chunks": merge_summary.get("successful_chunks", chunk_count),
                        "merged_categories": 0
                    }
                }
            
            # 添加到集合
            logger.info("[RAG] 开始将分类规则添加到ChromaDB集合")
            self.collection.add(
                ids=ids,
                embeddings=embeddings,
                metadatas=metadatas,
                documents=documents_list
            )
            logger.info(f"[RAG] 成功添加 {len(ids)} 条分类规则到ChromaDB，跳过了 {skipped_categories} 条空分类")
            
            return {
                "success": True,
                "message": f"成功添加 {len(ids)} 条分类规则数据（来自 {chunk_count} 个文档块），跳过了 {skipped_categories} 条空分类",
                "ids": ids,
                "skipped_count": skipped_categories,
                "chunk_info": {
                    "total_chunks": chunk_count,
                    "successful_chunks": merge_summary.get("successful_chunks", chunk_count),
                    "merged_categories": len(ids)
                }
            }
            
        except Exception as e:
            logger.error(f"[RAG] 添加分块规章制度文档到ChromaDB失败: {str(e)}", exc_info=True)
            log_rag_operation("添加分块文档", self.collection_name, error=str(e))
            return {"success": False, "error": str(e)}


# 全局RAG管理器实例
_rag_manager = None

def get_rag_manager(collection_name: str = "audit_regulations") -> RAGManager:
    """获取全局RAG管理器实例"""
    global _rag_manager
    if _rag_manager is None:
        logger.info(f"[RAG] 创建全局RAG管理器实例，集合名称: {collection_name}")
        _rag_manager = RAGManager(collection_name)
    return _rag_manager