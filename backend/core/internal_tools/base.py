"""
内置工具系统 - 基础工具类
"""

from typing import Dict, Any, List

class BuiltinTool:
    """内置工具基类"""
    
    def __init__(self, tool_id: str, name: str, description: str, 
                 inputs: List[Dict], outputs: List[Dict], category: str = "general"):
        self.tool_id = tool_id
        self.name = name
        self.description = description
        self.inputs = inputs
        self.outputs = outputs
        self.category = category
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具，子类需要实现此方法"""
        raise NotImplementedError("子类必须实现execute方法")
    
    def validate_inputs(self, inputs: Dict[str, Any]) -> bool:
        """验证输入参数"""
        for input_def in self.inputs:
            param_name = input_def.get("name")
            required = input_def.get("required", False)
            
            if required and param_name not in inputs:
                raise ValueError(f"缺少必需参数: {param_name}")
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.tool_id,
            "name": self.name,
            "description": self.description,
            "inputs": self.inputs,
            "outputs": self.outputs,
            "category": self.category,
            "type": "builtin"
        }