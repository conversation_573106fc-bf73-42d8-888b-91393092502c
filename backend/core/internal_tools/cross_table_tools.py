"""
跨表关联工具
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from core.db import get_db_connection
from .base import BuiltinTool

logger = logging.getLogger(__name__)


class CrossTableQueryTool(BuiltinTool):
    """跨表关联查询工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="cross_table_query",
            name="跨表关联查询",
            description="跨多个表进行关联查询",
            inputs=[
                {"name": "main_table", "type": "string", "description": "主表(companies/vouchers/assets/staffs)", "required": True},
                {"name": "join_tables", "type": "array", "description": "要关联的表列表", "required": False},
                {"name": "conditions", "type": "array", "description": "查询条件列表", "required": False},
                {"name": "fields", "type": "array", "description": "要返回的字段列表", "required": False},
                {"name": "limit", "type": "number", "description": "返回记录数限制", "required": False}
            ],
            outputs=[
                {"name": "results", "type": "array", "description": "查询结果"},
                {"name": "count", "type": "number", "description": "记录数量"},
                {"name": "query_info", "type": "object", "description": "查询信息"}
            ],
            category="cross_table"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行跨表关联查询"""
        try:
            self.validate_inputs(inputs)
            
            main_table = inputs.get("main_table")
            join_tables = inputs.get("join_tables", [])
            conditions = inputs.get("conditions", [])
            fields = inputs.get("fields", [])
            limit = inputs.get("limit", 100)
            
            # 验证主表
            valid_tables = ["companies", "vouchers", "assets", "staffs", "subject_accounts", "voucher_entries"]
            if main_table not in valid_tables:
                return {
                    "success": False,
                    "error": f"无效的主表: {main_table}"
                }
            
            # 构建查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建SELECT部分
            if fields:
                select_fields = ", ".join(fields)
            else:
                select_fields = f"{main_table}.*"
            
            # 构建FROM和JOIN部分
            from_clause = main_table
            join_clauses = []
            
            # 预定义的关联关系
            join_relations = {
                ("vouchers", "voucher_entries"): "vouchers.id = voucher_entries.voucher_id",
                ("voucher_entries", "vouchers"): "voucher_entries.voucher_id = vouchers.id",
                ("voucher_entries", "subject_accounts"): "voucher_entries.account_code = subject_accounts.code",
                ("subject_accounts", "voucher_entries"): "subject_accounts.code = voucher_entries.account_code",
                ("staffs", "roles"): "staffs.role_code = roles.code",
                ("roles", "staffs"): "roles.code = staffs.role_code"
            }
            
            for join_table in join_tables:
                if join_table in valid_tables:
                    relation_key = (main_table, join_table)
                    reverse_key = (join_table, main_table)
                    
                    if relation_key in join_relations:
                        join_clauses.append(f"LEFT JOIN {join_table} ON {join_relations[relation_key]}")
                    elif reverse_key in join_relations:
                        join_clauses.append(f"LEFT JOIN {join_table} ON {join_relations[reverse_key]}")
                    else:
                        # 如果没有预定义关系，简单关联
                        join_clauses.append(f"LEFT JOIN {join_table} ON 1=1")
            
            # 构建WHERE部分
            where_clauses = []
            params = []
            
            for condition in conditions:
                if isinstance(condition, dict) and "field" in condition and "operator" in condition and "value" in condition:
                    field = condition["field"]
                    operator = condition["operator"]
                    value = condition["value"]
                    
                    if operator in ["=", ">", "<", ">=", "<=", "!="]:
                        where_clauses.append(f"{field} {operator} ?")
                        params.append(value)
                    elif operator.upper() == "LIKE":
                        where_clauses.append(f"{field} LIKE ?")
                        params.append(f"%{value}%")
                    elif operator.upper() == "IN":
                        if isinstance(value, list):
                            placeholders = ", ".join(["?"] * len(value))
                            where_clauses.append(f"{field} IN ({placeholders})")
                            params.extend(value)
            
            where_clause = "WHERE " + " AND ".join(where_clauses) if where_clauses else ""
            
            # 构建完整查询
            query = f"""
                SELECT {select_fields}
                FROM {from_clause}
                {" ".join(join_clauses)}
                {where_clause}
                LIMIT {limit}
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 获取列名
            column_names = [description[0] for description in cursor.description]
            
            # 转换结果
            results = []
            for row in rows:
                result = {}
                for i, value in enumerate(row):
                    result[column_names[i]] = value
                results.append(result)
            
            query_info = {
                "main_table": main_table,
                "join_tables": join_tables,
                "conditions_count": len(conditions),
                "fields_count": len(fields) if fields else len(column_names)
            }
            
            return {
                "success": True,
                "data": {
                    "results": results,
                    "count": len(results),
                    "query_info": query_info
                }
            }
            
        except Exception as e:
            logger.error(f"跨表关联查询失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class BusinessSummaryTool(BuiltinTool):
    """业务汇总统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="business_summary",
            name="业务汇总统计",
            description="对整个系统的业务数据进行汇总统计",
            inputs=[
                {"name": "date_from", "type": "string", "description": "开始日期(YYYY-MM-DD)", "required": False},
                {"name": "date_to", "type": "string", "description": "结束日期(YYYY-MM-DD)", "required": False},
                {"name": "include_companies", "type": "boolean", "description": "是否包含公司统计", "required": False},
                {"name": "include_vouchers", "type": "boolean", "description": "是否包含凭证统计", "required": False},
                {"name": "include_assets", "type": "boolean", "description": "是否包含资产统计", "required": False},
                {"name": "include_staffs", "type": "boolean", "description": "是否包含员工统计", "required": False}
            ],
            outputs=[
                {"name": "summary", "type": "object", "description": "业务汇总数据"},
                {"name": "company_stats", "type": "object", "description": "公司统计"},
                {"name": "voucher_stats", "type": "object", "description": "凭证统计"},
                {"name": "asset_stats", "type": "object", "description": "资产统计"},
                {"name": "staff_stats", "type": "object", "description": "员工统计"}
            ],
            category="cross_table"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行业务汇总统计"""
        try:
            self.validate_inputs(inputs)
            
            date_from = inputs.get("date_from")
            date_to = inputs.get("date_to")
            include_companies = inputs.get("include_companies", True)
            include_vouchers = inputs.get("include_vouchers", True)
            include_assets = inputs.get("include_assets", True)
            include_staffs = inputs.get("include_staffs", True)
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            summary = {}
            company_stats = {}
            voucher_stats = {}
            asset_stats = {}
            staff_stats = {}
            
            # 公司统计
            if include_companies:
                company_query = """
                    SELECT 
                        COUNT(*) as total_companies,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_companies,
                        COUNT(CASE WHEN company_type = 'parent' THEN 1 END) as parent_companies,
                        COUNT(CASE WHEN company_type = 'subsidiary' THEN 1 END) as subsidiary_companies,
                        SUM(registered_capital) as total_capital
                    FROM companies
                """
                cursor.execute(company_query)
                company_row = cursor.fetchone()
                
                company_stats = {
                    "total_companies": company_row[0] or 0,
                    "active_companies": company_row[1] or 0,
                    "parent_companies": company_row[2] or 0,
                    "subsidiary_companies": company_row[3] or 0,
                    "total_capital": float(company_row[4]) if company_row[4] else 0
                }
            
            # 凭证统计
            if include_vouchers:
                voucher_conditions = []
                voucher_params = []
                
                if date_from:
                    voucher_conditions.append("date >= ?")
                    voucher_params.append(date_from)
                
                if date_to:
                    voucher_conditions.append("date <= ?")
                    voucher_params.append(date_to)
                
                voucher_where = "WHERE " + " AND ".join(voucher_conditions) if voucher_conditions else ""
                
                voucher_query = f"""
                    SELECT 
                        COUNT(*) as total_vouchers,
                        SUM(total_debit) as total_debit,
                        SUM(total_credit) as total_credit,
                        COUNT(CASE WHEN date >= date('now', '-30 days') THEN 1 END) as recent_vouchers
                    FROM vouchers
                    {voucher_where}
                """
                cursor.execute(voucher_query, voucher_params)
                voucher_row = cursor.fetchone()
                
                # 分录统计
                entry_conditions = []
                entry_params = []
                
                if date_from:
                    entry_conditions.append("v.date >= ?")
                    entry_params.append(date_from)
                
                if date_to:
                    entry_conditions.append("v.date <= ?")
                    entry_params.append(date_to)
                
                entry_where = "WHERE " + " AND ".join(entry_conditions) if entry_conditions else ""
                
                entry_query = f"""
                    SELECT COUNT(*) as total_entries
                    FROM voucher_entries ve
                    JOIN vouchers v ON ve.voucher_id = v.id
                    {entry_where}
                """
                cursor.execute(entry_query, entry_params)
                entry_row = cursor.fetchone()
                
                voucher_stats = {
                    "total_vouchers": voucher_row[0] or 0,
                    "total_debit": float(voucher_row[1]) if voucher_row[1] else 0,
                    "total_credit": float(voucher_row[2]) if voucher_row[2] else 0,
                    "recent_vouchers": voucher_row[3] or 0,
                    "total_entries": entry_row[0] or 0
                }
            
            # 资产统计
            if include_assets:
                asset_query = """
                    SELECT 
                        COUNT(*) as total_assets,
                        SUM(value) as total_value,
                        COUNT(CASE WHEN status = '在用' THEN 1 END) as active_assets,
                        COUNT(CASE WHEN status = '报废' THEN 1 END) as scrapped_assets
                    FROM assets
                """
                cursor.execute(asset_query)
                asset_row = cursor.fetchone()
                
                asset_stats = {
                    "total_assets": asset_row[0] or 0,
                    "total_value": float(asset_row[1]) if asset_row[1] else 0,
                    "active_assets": asset_row[2] or 0,
                    "scrapped_assets": asset_row[3] or 0
                }
            
            # 员工统计
            if include_staffs:
                staff_query = """
                    SELECT 
                        COUNT(*) as total_staffs,
                        COUNT(CASE WHEN status = '在职' THEN 1 END) as active_staffs,
                        COUNT(DISTINCT role_code) as total_roles
                    FROM staffs
                """
                cursor.execute(staff_query)
                staff_row = cursor.fetchone()
                
                staff_stats = {
                    "total_staffs": staff_row[0] or 0,
                    "active_staffs": staff_row[1] or 0,
                    "total_roles": staff_row[2] or 0
                }
            
            # 汇总数据
            summary = {
                "timestamp": datetime.now().isoformat(),
                "date_range": {
                    "from": date_from,
                    "to": date_to
                },
                "includes": {
                    "companies": include_companies,
                    "vouchers": include_vouchers,
                    "assets": include_assets,
                    "staffs": include_staffs
                }
            }
            
            return {
                "success": True,
                "data": {
                    "summary": summary,
                    "company_stats": company_stats if include_companies else None,
                    "voucher_stats": voucher_stats if include_vouchers else None,
                    "asset_stats": asset_stats if include_assets else None,
                    "staff_stats": staff_stats if include_staffs else None
                }
            }
            
        except Exception as e:
            logger.error(f"业务汇总统计失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }