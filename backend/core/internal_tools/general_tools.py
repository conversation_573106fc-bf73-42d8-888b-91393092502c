"""
通用工具
"""

import logging
import json
from typing import Dict, Any, List, Optional, Union
from core.db import get_db_connection
from .base import BuiltinTool

logger = logging.getLogger(__name__)


class CalculateStatisticsTool(BuiltinTool):
    """计算统计数据工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="calculate_statistics",
            name="计算统计数据",
            description="对数值数组进行统计计算",
            inputs=[
                {"name": "numbers", "type": "array", "description": "数值数组", "required": True},
                {"name": "operations", "type": "array", "description": "要执行的操作列表", "required": False}
            ],
            outputs=[
                {"name": "sum", "type": "number", "description": "总和"},
                {"name": "average", "type": "number", "description": "平均值"},
                {"name": "min", "type": "number", "description": "最小值"},
                {"name": "max", "type": "number", "description": "最大值"},
                {"name": "count", "type": "number", "description": "数量"}
            ],
            category="calculation"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行统计计算"""
        try:
            self.validate_inputs(inputs)
            
            numbers = inputs.get("numbers", [])
            operations = inputs.get("operations", ["sum", "average", "min", "max", "count"])
            
            if not numbers:
                return {
                    "success": False,
                    "error": "数值数组不能为空"
                }
            
            # 验证所有元素都是数字
            valid_numbers = []
            for num in numbers:
                if isinstance(num, (int, float)):
                    valid_numbers.append(num)
                else:
                    try:
                        valid_numbers.append(float(num))
                    except (ValueError, TypeError):
                        return {
                            "success": False,
                            "error": f"无效的数值: {num}"
                        }
            
            if not valid_numbers:
                return {
                    "success": False,
                    "error": "没有有效的数值"
                }
            
            # 计算统计结果
            result = {}
            
            if "sum" in operations:
                result["sum"] = sum(valid_numbers)
            
            if "average" in operations:
                result["average"] = sum(valid_numbers) / len(valid_numbers)
            
            if "min" in operations:
                result["min"] = min(valid_numbers)
            
            if "max" in operations:
                result["max"] = max(valid_numbers)
            
            if "count" in operations:
                result["count"] = len(valid_numbers)
            
            # 如果没有指定操作，默认计算所有
            if not operations:
                result = {
                    "sum": sum(valid_numbers),
                    "average": sum(valid_numbers) / len(valid_numbers),
                    "min": min(valid_numbers),
                    "max": max(valid_numbers),
                    "count": len(valid_numbers)
                }
            
            return {
                "success": True,
                "data": result
            }
            
        except Exception as e:
            logger.error(f"统计计算失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class FilterDataTool(BuiltinTool):
    """数据过滤工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="filter_data",
            name="数据过滤",
            description="根据条件过滤数据",
            inputs=[
                {"name": "data", "type": "array", "description": "要过滤的数据数组", "required": True},
                {"name": "filters", "type": "array", "description": "过滤条件列表", "required": True}
            ],
            outputs=[
                {"name": "filtered_data", "type": "array", "description": "过滤后的数据"},
                {"name": "count", "type": "number", "description": "过滤后的数据数量"}
            ],
            category="data_processing"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行数据过滤"""
        try:
            self.validate_inputs(inputs)
            
            data = inputs.get("data", [])
            filters = inputs.get("filters", [])
            
            if not data:
                return {
                    "success": False,
                    "error": "数据数组不能为空"
                }
            
            if not filters:
                return {
                    "success": False,
                    "error": "过滤条件不能为空"
                }
            
            # 应用过滤条件
            filtered_data = data.copy()
            
            for filter_condition in filters:
                if not isinstance(filter_condition, dict):
                    continue
                
                field = filter_condition.get("field")
                operator = filter_condition.get("operator", "=")
                value = filter_condition.get("value")
                
                if field is None:
                    continue
                
                temp_data = []
                
                for item in filtered_data:
                    if not isinstance(item, dict):
                        temp_data.append(item)
                        continue
                    
                    if field not in item:
                        continue
                    
                    item_value = item[field]
                    match = False
                    
                    try:
                        if operator == "=":
                            match = item_value == value
                        elif operator == "!=":
                            match = item_value != value
                        elif operator == ">":
                            match = float(item_value) > float(value)
                        elif operator == "<":
                            match = float(item_value) < float(value)
                        elif operator == ">=":
                            match = float(item_value) >= float(value)
                        elif operator == "<=":
                            match = float(item_value) <= float(value)
                        elif operator.upper() == "LIKE":
                            match = str(value).lower() in str(item_value).lower()
                        elif operator.upper() == "IN":
                            match = item_value in value if isinstance(value, (list, tuple)) else False
                    except (ValueError, TypeError):
                        # 如果转换失败，则不匹配
                        pass
                    
                    if match:
                        temp_data.append(item)
                
                filtered_data = temp_data
            
            return {
                "success": True,
                "data": {
                    "filtered_data": filtered_data,
                    "count": len(filtered_data)
                }
            }
            
        except Exception as e:
            logger.error(f"数据过滤失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }