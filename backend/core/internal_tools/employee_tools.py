"""
员工相关工具
"""

import logging
from typing import Dict, Any, List, Optional
from core.db import get_db_connection
from .base import BuiltinTool

logger = logging.getLogger(__name__)


class QueryEmployeeInfoTool(BuiltinTool):
    """查询员工信息工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_employee_info",
            name="查询员工信息",
            description="根据员工ID或姓名查询员工详细信息",
            inputs=[
                {"name": "employee_id", "type": "string", "description": "员工ID", "required": False},
                {"name": "employee_name", "type": "string", "description": "员工姓名", "required": False},
                {"name": "department", "type": "string", "description": "部门名称", "required": False}
            ],
            outputs=[
                {"name": "employee_info", "type": "object", "description": "员工信息对象"},
                {"name": "count", "type": "number", "description": "查询到的员工数量"}
            ],
            category="employee"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询员工信息"""
        try:
            self.validate_inputs(inputs)
            
            employee_id = inputs.get("employee_id")
            employee_name = inputs.get("employee_name")
            department = inputs.get("department")
            
            if not any([employee_id, employee_name, department]):
                return {
                    "success": False,
                    "error": "至少需要提供员工ID、姓名或部门中的一个参数"
                }
            
            # 构建查询条件
            conditions = []
            params = []
            
            if employee_id:
                conditions.append("id = ?")
                params.append(employee_id)
            
            if employee_name:
                conditions.append("name LIKE ?")
                params.append(f"%{employee_name}%")
            
            if department:
                conditions.append("department = ?")
                params.append(department)
            
            where_clause = " AND ".join(conditions)
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT id, name, department, position, salary, hire_date, status
                FROM role_staff 
                WHERE {where_clause}
                ORDER BY name
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            employees = []
            for row in rows:
                employees.append({
                    "id": row[0],
                    "name": row[1],
                    "department": row[2],
                    "position": row[3],
                    "salary": row[4],
                    "hire_date": row[5],
                    "status": row[6]
                })
            
            return {
                "success": True,
                "data": {
                    "employee_info": employees,
                    "count": len(employees)
                }
            }
            
        except Exception as e:
            logger.error(f"查询员工信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class CountEmployeesTool(BuiltinTool):
    """统计员工数量工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="count_employees",
            name="统计员工数量",
            description="统计指定条件下的员工数量",
            inputs=[
                {"name": "department", "type": "string", "description": "部门名称", "required": False},
                {"name": "position", "type": "string", "description": "职位", "required": False},
                {"name": "status", "type": "string", "description": "员工状态", "required": False}
            ],
            outputs=[
                {"name": "total_count", "type": "number", "description": "员工总数"},
                {"name": "department_breakdown", "type": "object", "description": "按部门分组统计"}
            ],
            category="employee"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行员工数量统计"""
        try:
            self.validate_inputs(inputs)
            
            department = inputs.get("department")
            position = inputs.get("position")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if department:
                conditions.append("department = ?")
                params.append(department)
            
            if position:
                conditions.append("position = ?")
                params.append(position)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 总数统计
            total_query = f"SELECT COUNT(*) FROM role_staff {where_clause}"
            cursor.execute(total_query, params)
            total_count = cursor.fetchone()[0]
            
            # 按部门分组统计
            dept_query = f"""
                SELECT department, COUNT(*) as count
                FROM role_staff 
                {where_clause}
                GROUP BY department
                ORDER BY count DESC
            """
            cursor.execute(dept_query, params)
            dept_rows = cursor.fetchall()
            
            department_breakdown = {}
            for row in dept_rows:
                department_breakdown[row[0]] = row[1]
            
            return {
                "success": True,
                "data": {
                    "total_count": total_count,
                    "department_breakdown": department_breakdown
                }
            }
            
        except Exception as e:
            logger.error(f"统计员工数量失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class QueryStaffsTool(BuiltinTool):
    """查询员工信息工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_staffs",
            name="查询员工信息",
            description="根据条件查询员工详细信息",
            inputs=[
                {"name": "staff_id", "type": "number", "description": "员工ID", "required": False},
                {"name": "job_no", "type": "string", "description": "工号", "required": False},
                {"name": "name", "type": "string", "description": "员工姓名", "required": False},
                {"name": "role_code", "type": "string", "description": "角色代码", "required": False},
                {"name": "level", "type": "string", "description": "员工级别", "required": False},
                {"name": "phone", "type": "string", "description": "联系电话", "required": False},
                {"name": "status", "type": "string", "description": "员工状态", "required": False}
            ],
            outputs=[
                {"name": "staffs", "type": "array", "description": "员工信息列表"},
                {"name": "count", "type": "number", "description": "查询到的员工数量"}
            ],
            category="employee"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询员工信息"""
        try:
            self.validate_inputs(inputs)
            
            staff_id = inputs.get("staff_id")
            job_no = inputs.get("job_no")
            name = inputs.get("name")
            role_code = inputs.get("role_code")
            level = inputs.get("level")
            phone = inputs.get("phone")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if staff_id:
                conditions.append("s.id = ?")
                params.append(staff_id)
            
            if job_no:
                conditions.append("s.job_no = ?")
                params.append(job_no)
            
            if name:
                conditions.append("s.name LIKE ?")
                params.append(f"%{name}%")
            
            if role_code:
                conditions.append("s.role_code = ?")
                params.append(role_code)
            
            if level:
                conditions.append("s.level = ?")
                params.append(level)
            
            if phone:
                conditions.append("s.phone = ?")
                params.append(phone)
            
            if status:
                conditions.append("s.status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT s.id, s.job_no, s.name, s.role_code, r.name as role_name, 
                       s.level, s.phone, s.status, s.remark
                FROM staffs s
                LEFT JOIN roles r ON s.role_code = r.code
                {where_clause}
                ORDER BY s.name
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            staffs = []
            for row in rows:
                staffs.append({
                    "id": row[0],
                    "job_no": row[1],
                    "name": row[2],
                    "role_code": row[3],
                    "role_name": row[4],
                    "level": row[5],
                    "phone": row[6],
                    "status": row[7],
                    "remark": row[8]
                })
            
            return {
                "success": True,
                "data": {
                    "staffs": staffs,
                    "count": len(staffs)
                }
            }
            
        except Exception as e:
            logger.error(f"查询员工信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class QueryRolesTool(BuiltinTool):
    """查询角色信息工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_roles",
            name="查询角色信息",
            description="根据条件查询角色信息",
            inputs=[
                {"name": "role_code", "type": "string", "description": "角色代码", "required": False},
                {"name": "role_name", "type": "string", "description": "角色名称", "required": False},
                {"name": "include_staff_count", "type": "boolean", "description": "是否包含员工数量", "required": False}
            ],
            outputs=[
                {"name": "roles", "type": "array", "description": "角色信息列表"},
                {"name": "count", "type": "number", "description": "查询到的角色数量"}
            ],
            category="employee"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询角色信息"""
        try:
            self.validate_inputs(inputs)
            
            role_code = inputs.get("role_code")
            role_name = inputs.get("role_name")
            include_staff_count = inputs.get("include_staff_count", False)
            
            # 构建查询条件
            conditions = []
            params = []
            
            if role_code:
                conditions.append("r.code = ?")
                params.append(role_code)
            
            if role_name:
                conditions.append("r.name LIKE ?")
                params.append(f"%{role_name}%")
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            if include_staff_count:
                query = f"""
                    SELECT r.code, r.name, r.description, COUNT(s.id) as staff_count
                    FROM roles r
                    LEFT JOIN staffs s ON r.code = s.role_code
                    {where_clause}
                    GROUP BY r.code, r.name, r.description
                    ORDER BY r.name
                """
            else:
                query = f"""
                    SELECT r.code, r.name, r.description
                    FROM roles r
                    {where_clause}
                    ORDER BY r.name
                """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            roles = []
            for row in rows:
                if include_staff_count:
                    roles.append({
                        "code": row[0],
                        "name": row[1],
                        "description": row[2],
                        "staff_count": row[3]
                    })
                else:
                    roles.append({
                        "code": row[0],
                        "name": row[1],
                        "description": row[2]
                    })
            
            return {
                "success": True,
                "data": {
                    "roles": roles,
                    "count": len(roles)
                }
            }
            
        except Exception as e:
            logger.error(f"查询角色信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class StaffRoleStatisticsTool(BuiltinTool):
    """员工角色统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="staff_role_statistics",
            name="员工角色统计分析",
            description="对员工和角色进行统计分析",
            inputs=[
                {"name": "group_by", "type": "string", "description": "分组字段(role/level/status)", "required": False},
                {"name": "role_code", "type": "string", "description": "筛选角色代码", "required": False},
                {"name": "level", "type": "string", "description": "筛选员工级别", "required": False},
                {"name": "status", "type": "string", "description": "筛选员工状态", "required": False}
            ],
            outputs=[
                {"name": "total_staffs", "type": "number", "description": "员工总数"},
                {"name": "total_roles", "type": "number", "description": "角色总数"},
                {"name": "group_statistics", "type": "object", "description": "分组统计结果"},
                {"name": "role_distribution", "type": "object", "description": "角色分布"},
                {"name": "level_distribution", "type": "object", "description": "级别分布"}
            ],
            category="employee"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行员工角色统计分析"""
        try:
            self.validate_inputs(inputs)
            
            group_by = inputs.get("group_by", "role")
            role_code = inputs.get("role_code")
            level = inputs.get("level")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if role_code:
                conditions.append("s.role_code = ?")
                params.append(role_code)
            
            if level:
                conditions.append("s.level = ?")
                params.append(level)
            
            if status:
                conditions.append("s.status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 基本统计
            staff_query = f"SELECT COUNT(*) FROM staffs s {where_clause}"
            cursor.execute(staff_query, params)
            total_staffs = cursor.fetchone()[0]
            
            role_query = "SELECT COUNT(*) FROM roles"
            cursor.execute(role_query)
            total_roles = cursor.fetchone()[0]
            
            # 分组统计
            valid_group_fields = ["role", "level", "status"]
            if group_by not in valid_group_fields:
                group_by = "role"
            
            if group_by == "role":
                group_query = f"""
                    SELECT r.name, COUNT(s.id) as count
                    FROM staffs s
                    LEFT JOIN roles r ON s.role_code = r.code
                    {where_clause}
                    GROUP BY r.name
                    ORDER BY count DESC
                """
            elif group_by == "level":
                group_query = f"""
                    SELECT s.level, COUNT(s.id) as count
                    FROM staffs s
                    {where_clause}
                    GROUP BY s.level
                    ORDER BY count DESC
                """
            else:  # status
                group_query = f"""
                    SELECT s.status, COUNT(s.id) as count
                    FROM staffs s
                    {where_clause}
                    GROUP BY s.status
                    ORDER BY count DESC
                """
            
            cursor.execute(group_query, params)
            group_rows = cursor.fetchall()
            
            group_statistics = {}
            for row in group_rows:
                key = row[0] or "未分类"
                group_statistics[key] = row[1]
            
            # 角色分布
            role_dist_query = f"""
                SELECT r.name, COUNT(s.id) as count
                FROM roles r
                LEFT JOIN staffs s ON r.code = s.role_code
                GROUP BY r.name
                ORDER BY count DESC
            """
            cursor.execute(role_dist_query)
            role_dist_rows = cursor.fetchall()
            
            role_distribution = {}
            for row in role_dist_rows:
                role_distribution[row[0] or "未分类"] = row[1]
            
            # 级别分布
            level_dist_query = f"""
                SELECT s.level, COUNT(s.id) as count
                FROM staffs s
                GROUP BY s.level
                ORDER BY count DESC
            """
            cursor.execute(level_dist_query)
            level_dist_rows = cursor.fetchall()
            
            level_distribution = {}
            for row in level_dist_rows:
                level_distribution[row[0] or "未分类"] = row[1]
            
            return {
                "success": True,
                "data": {
                    "total_staffs": total_staffs,
                    "total_roles": total_roles,
                    "group_statistics": group_statistics,
                    "role_distribution": role_distribution,
                    "level_distribution": level_distribution
                }
            }
            
        except Exception as e:
            logger.error(f"员工角色统计分析失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }