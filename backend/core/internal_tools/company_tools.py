"""
公司相关工具
"""

import logging
from typing import Dict, Any, List, Optional
from core.db import get_db_connection
from .base import BuiltinTool

logger = logging.getLogger(__name__)


class QueryCompaniesTool(BuiltinTool):
    """查询公司信息工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_companies",
            name="查询公司信息",
            description="根据条件查询公司信息",
            inputs=[
                {"name": "company_id", "type": "number", "description": "公司ID", "required": False},
                {"name": "company_name", "type": "string", "description": "公司名称", "required": False},
                {"name": "industry", "type": "string", "description": "行业", "required": False},
                {"name": "company_size", "type": "string", "description": "公司规模", "required": False},
                {"name": "status", "type": "string", "description": "公司状态", "required": False},
                {"name": "company_type", "type": "string", "description": "公司类型(parent/subsidiary)", "required": False}
            ],
            outputs=[
                {"name": "companies", "type": "array", "description": "公司信息列表"},
                {"name": "count", "type": "number", "description": "查询到的公司数量"}
            ],
            category="company"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询公司信息"""
        try:
            self.validate_inputs(inputs)
            
            company_id = inputs.get("company_id")
            company_name = inputs.get("company_name")
            industry = inputs.get("industry")
            company_size = inputs.get("company_size")
            status = inputs.get("status")
            company_type = inputs.get("company_type")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if company_id:
                conditions.append("id = ?")
                params.append(company_id)
            
            if company_name:
                conditions.append("name LIKE ?")
                params.append(f"%{company_name}%")
            
            if industry:
                conditions.append("industry = ?")
                params.append(industry)
            
            if company_size:
                conditions.append("company_size = ?")
                params.append(company_size)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            if company_type:
                conditions.append("company_type = ?")
                params.append(company_type)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT id, name, business_scope, industry, company_size, tax_id,
                       accounting_standards, parent_company_id, company_type,
                       established_date, registered_capital, status, created_at, updated_at
                FROM companies 
                {where_clause}
                ORDER BY name
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            companies = []
            for row in rows:
                companies.append({
                    "id": row[0],
                    "name": row[1],
                    "business_scope": row[2],
                    "industry": row[3],
                    "company_size": row[4],
                    "tax_id": row[5],
                    "accounting_standards": row[6],
                    "parent_company_id": row[7],
                    "company_type": row[8],
                    "established_date": row[9],
                    "registered_capital": float(row[10]) if row[10] else None,
                    "status": row[11],
                    "created_at": row[12],
                    "updated_at": row[13]
                })
            
            return {
                "success": True,
                "data": {
                    "companies": companies,
                    "count": len(companies)
                }
            }
            
        except Exception as e:
            logger.error(f"查询公司信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class CompanyStatisticsTool(BuiltinTool):
    """公司统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="company_statistics",
            name="公司统计分析",
            description="对公司信息进行统计分析",
            inputs=[
                {"name": "group_by", "type": "string", "description": "分组字段(industry/company_size/status/company_type)", "required": False},
                {"name": "industry", "type": "string", "description": "筛选行业", "required": False},
                {"name": "company_size", "type": "string", "description": "筛选公司规模", "required": False},
                {"name": "status", "type": "string", "description": "筛选公司状态", "required": False}
            ],
            outputs=[
                {"name": "total_count", "type": "number", "description": "公司总数"},
                {"name": "group_statistics", "type": "object", "description": "分组统计结果"},
                {"name": "capital_statistics", "type": "object", "description": "注册资本统计"}
            ],
            category="company"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行公司统计分析"""
        try:
            self.validate_inputs(inputs)
            
            group_by = inputs.get("group_by", "industry")
            industry = inputs.get("industry")
            company_size = inputs.get("company_size")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if industry:
                conditions.append("industry = ?")
                params.append(industry)
            
            if company_size:
                conditions.append("company_size = ?")
                params.append(company_size)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 总数统计
            total_query = f"SELECT COUNT(*) FROM companies {where_clause}"
            cursor.execute(total_query, params)
            total_count = cursor.fetchone()[0]
            
            # 分组统计
            valid_group_fields = ["industry", "company_size", "status", "company_type"]
            if group_by not in valid_group_fields:
                group_by = "industry"
            
            group_query = f"""
                SELECT {group_by}, COUNT(*) as count
                FROM companies 
                {where_clause}
                GROUP BY {group_by}
                ORDER BY count DESC
            """
            cursor.execute(group_query, params)
            group_rows = cursor.fetchall()
            
            group_statistics = {}
            for row in group_rows:
                group_statistics[row[0] or "未分类"] = row[1]
            
            # 注册资本统计
            capital_query = f"""
                SELECT 
                    SUM(registered_capital) as total_capital,
                    AVG(registered_capital) as avg_capital,
                    MIN(registered_capital) as min_capital,
                    MAX(registered_capital) as max_capital
                FROM companies 
                {where_clause}
                WHERE registered_capital IS NOT NULL
            """
            cursor.execute(capital_query, params)
            capital_row = cursor.fetchone()
            
            capital_statistics = {
                "total_capital": float(capital_row[0]) if capital_row[0] else 0,
                "avg_capital": float(capital_row[1]) if capital_row[1] else 0,
                "min_capital": float(capital_row[2]) if capital_row[2] else 0,
                "max_capital": float(capital_row[3]) if capital_row[3] else 0
            }
            
            return {
                "success": True,
                "data": {
                    "total_count": total_count,
                    "group_statistics": group_statistics,
                    "capital_statistics": capital_statistics
                }
            }
            
        except Exception as e:
            logger.error(f"公司统计分析失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }