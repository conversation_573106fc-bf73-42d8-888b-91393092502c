"""
财务相关工具
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from core.db import get_db_connection
from .base import BuiltinTool

logger = logging.getLogger(__name__)


class QueryDepartmentExpensesTool(BuiltinTool):
    """查询部门年度报销总额工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_department_expenses",
            name="查询部门年度报销总额",
            description="根据部门和年份查询报销总额",
            inputs=[
                {"name": "department", "type": "string", "description": "部门名称", "required": True},
                {"name": "year", "type": "number", "description": "年份", "required": False},
                {"name": "expense_type", "type": "string", "description": "费用类型", "required": False}
            ],
            outputs=[
                {"name": "total_amount", "type": "number", "description": "总报销金额"},
                {"name": "expense_breakdown", "type": "object", "description": "按费用类型分组"},
                {"name": "monthly_breakdown", "type": "object", "description": "按月份分组"}
            ],
            category="finance"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行部门报销查询"""
        try:
            self.validate_inputs(inputs)
            
            department = inputs.get("department")
            year = inputs.get("year", datetime.now().year)
            expense_type = inputs.get("expense_type")
            
            # 构建查询条件
            conditions = ["v.department = ?"]
            params = [department]
            
            # 添加年份条件
            conditions.append("strftime('%Y', v.voucher_date) = ?")
            params.append(str(year))
            
            if expense_type:
                conditions.append("v.expense_type = ?")
                params.append(expense_type)
            
            where_clause = " AND ".join(conditions)
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 总金额查询
            total_query = f"""
                SELECT SUM(vd.credit_amount) as total
                FROM voucher v
                JOIN voucher_detail vd ON v.id = vd.voucher_id
                WHERE {where_clause}
            """
            cursor.execute(total_query, params)
            total_result = cursor.fetchone()
            total_amount = total_result[0] if total_result[0] else 0
            
            # 按费用类型分组
            type_query = f"""
                SELECT v.expense_type, SUM(vd.credit_amount) as amount
                FROM voucher v
                JOIN voucher_detail vd ON v.id = vd.voucher_id
                WHERE {where_clause}
                GROUP BY v.expense_type
                ORDER BY amount DESC
            """
            cursor.execute(type_query, params)
            type_rows = cursor.fetchall()
            
            expense_breakdown = {}
            for row in type_rows:
                expense_breakdown[row[0] or "未分类"] = float(row[1])
            
            # 按月份分组
            month_query = f"""
                SELECT strftime('%m', v.voucher_date) as month, 
                       SUM(vd.credit_amount) as amount
                FROM voucher v
                JOIN voucher_detail vd ON v.id = vd.voucher_id
                WHERE {where_clause}
                GROUP BY strftime('%m', v.voucher_date)
                ORDER BY month
            """
            cursor.execute(month_query, params)
            month_rows = cursor.fetchall()
            
            monthly_breakdown = {}
            for row in month_rows:
                month_name = f"{year}-{row[0].zfill(2)}"
                monthly_breakdown[month_name] = float(row[1])
            
            return {
                "success": True,
                "data": {
                    "total_amount": float(total_amount),
                    "expense_breakdown": expense_breakdown,
                    "monthly_breakdown": monthly_breakdown
                }
            }
            
        except Exception as e:
            logger.error(f"查询部门报销失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class SearchVouchersTool(BuiltinTool):
    """搜索凭证工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="search_vouchers",
            name="搜索凭证",
            description="根据多种条件搜索会计凭证",
            inputs=[
                {"name": "keyword", "type": "string", "description": "关键词搜索", "required": False},
                {"name": "date_from", "type": "string", "description": "开始日期(YYYY-MM-DD)", "required": False},
                {"name": "date_to", "type": "string", "description": "结束日期(YYYY-MM-DD)", "required": False},
                {"name": "amount_min", "type": "number", "description": "最小金额", "required": False},
                {"name": "amount_max", "type": "number", "description": "最大金额", "required": False},
                {"name": "department", "type": "string", "description": "部门", "required": False},
                {"name": "limit", "type": "number", "description": "返回数量限制", "required": False}
            ],
            outputs=[
                {"name": "vouchers", "type": "array", "description": "凭证列表"},
                {"name": "total_count", "type": "number", "description": "总数量"},
                {"name": "total_amount", "type": "number", "description": "总金额"}
            ],
            category="finance"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行凭证搜索"""
        try:
            self.validate_inputs(inputs)
            
            keyword = inputs.get("keyword")
            date_from = inputs.get("date_from")
            date_to = inputs.get("date_to")
            amount_min = inputs.get("amount_min")
            amount_max = inputs.get("amount_max")
            department = inputs.get("department")
            limit = inputs.get("limit", 100)
            
            # 构建查询条件
            conditions = []
            params = []
            
            if keyword:
                conditions.append("(v.description LIKE ? OR v.summary LIKE ?)")
                params.extend([f"%{keyword}%", f"%{keyword}%"])
            
            if date_from:
                conditions.append("v.voucher_date >= ?")
                params.append(date_from)
            
            if date_to:
                conditions.append("v.voucher_date <= ?")
                params.append(date_to)
            
            if department:
                conditions.append("v.department = ?")
                params.append(department)
            
            # 金额条件需要通过子查询处理
            amount_conditions = []
            if amount_min is not None:
                amount_conditions.append("total_amount >= ?")
                params.append(amount_min)
            
            if amount_max is not None:
                amount_conditions.append("total_amount <= ?")
                params.append(amount_max)
            
            where_clause = ""
            if conditions:
                where_clause = "WHERE " + " AND ".join(conditions)
            
            having_clause = ""
            if amount_conditions:
                having_clause = "HAVING " + " AND ".join(amount_conditions)
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 主查询
            query = f"""
                SELECT v.id, v.voucher_number, v.voucher_date, v.description, 
                       v.department, v.created_by, v.created_at,
                       SUM(vd.debit_amount) as total_amount
                FROM voucher v
                LEFT JOIN voucher_detail vd ON v.id = vd.voucher_id
                {where_clause}
                GROUP BY v.id, v.voucher_number, v.voucher_date, v.description, 
                         v.department, v.created_by, v.created_at
                {having_clause}
                ORDER BY v.voucher_date DESC, v.created_at DESC
                LIMIT ?
            """
            
            params.append(limit)
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            vouchers = []
            total_amount = 0
            for row in rows:
                voucher = {
                    "id": row[0],
                    "voucher_number": row[1],
                    "voucher_date": row[2],
                    "description": row[3],
                    "department": row[4],
                    "created_by": row[5],
                    "created_at": row[6],
                    "total_amount": float(row[7]) if row[7] else 0
                }
                vouchers.append(voucher)
                total_amount += voucher["total_amount"]
            
            return {
                "success": True,
                "data": {
                    "vouchers": vouchers,
                    "total_count": len(vouchers),
                    "total_amount": total_amount
                }
            }
            
        except Exception as e:
            logger.error(f"搜索凭证失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class QueryVoucherEntriesTool(BuiltinTool):
    """查询凭证分录工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_voucher_entries",
            name="查询凭证分录",
            description="根据条件查询凭证分录信息",
            inputs=[
                {"name": "voucher_id", "type": "number", "description": "凭证ID", "required": False},
                {"name": "voucher_no", "type": "string", "description": "凭证号", "required": False},
                {"name": "account_code", "type": "string", "description": "科目编码", "required": False},
                {"name": "account_name", "type": "string", "description": "科目名称", "required": False},
                {"name": "date_from", "type": "string", "description": "开始日期(YYYY-MM-DD)", "required": False},
                {"name": "date_to", "type": "string", "description": "结束日期(YYYY-MM-DD)", "required": False},
                {"name": "summary", "type": "string", "description": "摘要关键词", "required": False},
                {"name": "min_amount", "type": "number", "description": "最小金额", "required": False},
                {"name": "max_amount", "type": "number", "description": "最大金额", "required": False}
            ],
            outputs=[
                {"name": "voucher_entries", "type": "array", "description": "凭证分录列表"},
                {"name": "total_count", "type": "number", "description": "分录总数"},
                {"name": "total_debit", "type": "number", "description": "借方合计"},
                {"name": "total_credit", "type": "number", "description": "贷方合计"}
            ],
            category="finance"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询凭证分录"""
        try:
            self.validate_inputs(inputs)
            
            voucher_id = inputs.get("voucher_id")
            voucher_no = inputs.get("voucher_no")
            account_code = inputs.get("account_code")
            account_name = inputs.get("account_name")
            date_from = inputs.get("date_from")
            date_to = inputs.get("date_to")
            summary = inputs.get("summary")
            min_amount = inputs.get("min_amount")
            max_amount = inputs.get("max_amount")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if voucher_id:
                conditions.append("ve.voucher_id = ?")
                params.append(voucher_id)
            
            if voucher_no:
                conditions.append("v.voucher_no LIKE ?")
                params.append(f"%{voucher_no}%")
            
            if account_code:
                conditions.append("ve.account_code = ?")
                params.append(account_code)
            
            if account_name:
                conditions.append("ve.account_name LIKE ?")
                params.append(f"%{account_name}%")
            
            if date_from:
                conditions.append("v.date >= ?")
                params.append(date_from)
            
            if date_to:
                conditions.append("v.date <= ?")
                params.append(date_to)
            
            if summary:
                conditions.append("ve.summary LIKE ?")
                params.append(f"%{summary}%")
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 金额条件
            amount_conditions = []
            if min_amount is not None:
                amount_conditions.append("(ve.debit >= ? OR ve.credit >= ?)")
                params.extend([min_amount, min_amount])
            
            if max_amount is not None:
                amount_conditions.append("(ve.debit <= ? OR ve.credit <= ?)")
                params.extend([max_amount, max_amount])
            
            having_clause = ""
            if amount_conditions:
                having_clause = "HAVING " + " AND ".join(amount_conditions)
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT ve.id, ve.voucher_id, v.voucher_no, v.date, ve.summary,
                       ve.account_code, ve.account_name, ve.debit, ve.credit
                FROM voucher_entries ve
                JOIN vouchers v ON ve.voucher_id = v.id
                {where_clause}
                {having_clause}
                ORDER BY v.date DESC, v.voucher_no DESC, ve.id
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            voucher_entries = []
            total_debit = 0
            total_credit = 0
            
            for row in rows:
                entry = {
                    "id": row[0],
                    "voucher_id": row[1],
                    "voucher_no": row[2],
                    "date": row[3],
                    "summary": row[4],
                    "account_code": row[5],
                    "account_name": row[6],
                    "debit": float(row[7]) if row[7] else 0,
                    "credit": float(row[8]) if row[8] else 0
                }
                voucher_entries.append(entry)
                total_debit += entry["debit"]
                total_credit += entry["credit"]
            
            return {
                "success": True,
                "data": {
                    "voucher_entries": voucher_entries,
                    "total_count": len(voucher_entries),
                    "total_debit": total_debit,
                    "total_credit": total_credit
                }
            }
            
        except Exception as e:
            logger.error(f"查询凭证分录失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class VoucherStatisticsTool(BuiltinTool):
    """凭证统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="voucher_statistics",
            name="凭证统计分析",
            description="对凭证进行统计分析",
            inputs=[
                {"name": "date_from", "type": "string", "description": "开始日期(YYYY-MM-DD)", "required": False},
                {"name": "date_to", "type": "string", "description": "结束日期(YYYY-MM-DD)", "required": False},
                {"name": "group_by", "type": "string", "description": "分组字段(month/account_category)", "required": False},
                {"name": "account_category", "type": "string", "description": "筛选科目类别", "required": False}
            ],
            outputs=[
                {"name": "voucher_count", "type": "number", "description": "凭证总数"},
                {"name": "entry_count", "type": "number", "description": "分录总数"},
                {"name": "total_debit", "type": "number", "description": "借方合计"},
                {"name": "total_credit", "type": "number", "description": "贷方合计"},
                {"name": "group_statistics", "type": "object", "description": "分组统计结果"},
                {"name": "top_accounts", "type": "array", "description": "最常用科目"}
            ],
            category="finance"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行凭证统计分析"""
        try:
            self.validate_inputs(inputs)
            
            date_from = inputs.get("date_from")
            date_to = inputs.get("date_to")
            group_by = inputs.get("group_by", "month")
            account_category = inputs.get("account_category")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if date_from:
                conditions.append("v.date >= ?")
                params.append(date_from)
            
            if date_to:
                conditions.append("v.date <= ?")
                params.append(date_to)
            
            if account_category:
                conditions.append("sa.category = ?")
                params.append(account_category)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 基本统计
            basic_query = f"""
                SELECT 
                    COUNT(DISTINCT v.id) as voucher_count,
                    COUNT(ve.id) as entry_count,
                    SUM(ve.debit) as total_debit,
                    SUM(ve.credit) as total_credit
                FROM vouchers v
                JOIN voucher_entries ve ON v.id = ve.voucher_id
                LEFT JOIN subject_accounts sa ON ve.account_code = sa.code
                {where_clause}
            """
            cursor.execute(basic_query, params)
            basic_row = cursor.fetchone()
            
            voucher_count = basic_row[0] if basic_row[0] else 0
            entry_count = basic_row[1] if basic_row[1] else 0
            total_debit = float(basic_row[2]) if basic_row[2] else 0
            total_credit = float(basic_row[3]) if basic_row[3] else 0
            
            # 分组统计
            group_statistics = {}
            if group_by == "month":
                month_query = f"""
                    SELECT strftime('%Y-%m', v.date) as month, 
                           COUNT(DISTINCT v.id) as voucher_count,
                           SUM(ve.debit) as debit_amount,
                           SUM(ve.credit) as credit_amount
                    FROM vouchers v
                    JOIN voucher_entries ve ON v.id = ve.voucher_id
                    {where_clause}
                    GROUP BY strftime('%Y-%m', v.date)
                    ORDER BY month
                """
                cursor.execute(month_query, params)
                month_rows = cursor.fetchall()
                
                for row in month_rows:
                    group_statistics[row[0]] = {
                        "voucher_count": row[1],
                        "debit_amount": float(row[2]) if row[2] else 0,
                        "credit_amount": float(row[3]) if row[3] else 0
                    }
            
            elif group_by == "account_category":
                category_query = f"""
                    SELECT sa.category, 
                           COUNT(ve.id) as entry_count,
                           SUM(ve.debit) as debit_amount,
                           SUM(ve.credit) as credit_amount
                    FROM voucher_entries ve
                    JOIN vouchers v ON ve.voucher_id = v.id
                    LEFT JOIN subject_accounts sa ON ve.account_code = sa.code
                    {where_clause}
                    GROUP BY sa.category
                    ORDER BY entry_count DESC
                """
                cursor.execute(category_query, params)
                category_rows = cursor.fetchall()
                
                for row in category_rows:
                    category = row[0] or "未分类"
                    group_statistics[category] = {
                        "entry_count": row[1],
                        "debit_amount": float(row[2]) if row[2] else 0,
                        "credit_amount": float(row[3]) if row[3] else 0
                    }
            
            # 最常用科目
            top_accounts_query = f"""
                SELECT ve.account_code, ve.account_name, sa.category,
                       COUNT(ve.id) as usage_count,
                       SUM(ve.debit) as total_debit,
                       SUM(ve.credit) as total_credit
                FROM voucher_entries ve
                JOIN vouchers v ON ve.voucher_id = v.id
                LEFT JOIN subject_accounts sa ON ve.account_code = sa.code
                {where_clause}
                GROUP BY ve.account_code, ve.account_name, sa.category
                ORDER BY usage_count DESC
                LIMIT 10
            """
            cursor.execute(top_accounts_query, params)
            top_account_rows = cursor.fetchall()
            
            top_accounts = []
            for row in top_account_rows:
                top_accounts.append({
                    "account_code": row[0],
                    "account_name": row[1],
                    "category": row[2] or "未分类",
                    "usage_count": row[3],
                    "total_debit": float(row[4]) if row[4] else 0,
                    "total_credit": float(row[5]) if row[5] else 0
                })
            
            return {
                "success": True,
                "data": {
                    "voucher_count": voucher_count,
                    "entry_count": entry_count,
                    "total_debit": total_debit,
                    "total_credit": total_credit,
                    "group_statistics": group_statistics,
                    "top_accounts": top_accounts
                }
            }
            
        except Exception as e:
            logger.error(f"凭证统计分析失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }