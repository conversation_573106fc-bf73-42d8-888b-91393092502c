"""
内部工具管理器 - 管理智能体使用的内部工具
"""

import logging
import os
import uuid
import json
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import importlib.util
import sys
import tempfile

from core.logging_config import log_rag_operation
from core.config import config
from core.db import get_db_connection

logger = logging.getLogger(__name__)

class InternalToolsManager:
    """内部工具管理器"""
    
    def __init__(self, storage_path: Optional[str] = None):
        """初始化内部工具管理器"""
        logger.info("[InternalTools] 初始化内部工具管理器")
        
        # 设置存储路径
        if not storage_path:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            storage_path = os.path.join(base_dir, "internal_tools")
        
        self.storage_path = storage_path
        self.tools_file = os.path.join(storage_path, "tools.json")
        self.tools_dir = os.path.join(storage_path, "tool_scripts")
        
        # 创建必要的目录
        os.makedirs(self.storage_path, exist_ok=True)
        os.makedirs(self.tools_dir, exist_ok=True)
        
        # 初始化工具数据
        self.tools = self._load_tools()
        
        logger.info(f"[InternalTools] 内部工具管理器初始化完成，存储路径: {self.storage_path}")
    
    def _load_tools(self) -> Dict[str, Any]:
        """加载工具数据"""
        logger.debug("[InternalTools] 加载工具数据")
        
        try:
            if os.path.exists(self.tools_file):
                with open(self.tools_file, 'r', encoding='utf-8') as f:
                    tools_data = json.load(f)
                logger.info(f"[InternalTools] 成功加载 {len(tools_data)} 个工具")
                return tools_data
            else:
                logger.info("[InternalTools] 工具数据文件不存在，创建空数据")
                return {}
        except Exception as e:
            logger.error(f"[InternalTools] 加载工具数据失败: {str(e)}", exc_info=True)
            return {}
    
    def _save_tools(self) -> bool:
        """保存工具数据"""
        logger.debug("[InternalTools] 保存工具数据")
        
        try:
            with open(self.tools_file, 'w', encoding='utf-8') as f:
                json.dump(self.tools, f, ensure_ascii=False, indent=2)
            logger.info("[InternalTools] 工具数据保存成功")
            return True
        except Exception as e:
            logger.error(f"[InternalTools] 保存工具数据失败: {str(e)}", exc_info=True)
            return False
    
    def _validate_tool_data(self, tool_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证工具数据"""
        logger.debug("[InternalTools] 验证工具数据")
        
        required_fields = ["name", "description", "inputs", "outputs", "script"]
        for field in required_fields:
            if field not in tool_data:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 验证输入输出格式
        if not isinstance(tool_data["inputs"], list):
            raise ValueError("inputs 必须是列表")
        
        if not isinstance(tool_data["outputs"], list):
            raise ValueError("outputs 必须是列表")
        
        # 验证脚本
        if not isinstance(tool_data["script"], str) or not tool_data["script"].strip():
            raise ValueError("script 不能为空")
        
        # 添加默认值
        tool_data.setdefault("id", str(uuid.uuid4()))
        tool_data.setdefault("created_at", datetime.now().isoformat())
        tool_data.setdefault("updated_at", datetime.now().isoformat())
        tool_data.setdefault("enabled", True)
        tool_data.setdefault("version", "1.0")
        
        return tool_data
    
    def _save_tool_script(self, tool_id: str, script_content: str) -> str:
        """保存工具脚本"""
        logger.debug(f"[InternalTools] 保存工具脚本，ID: {tool_id}")
        
        try:
            script_path = os.path.join(self.tools_dir, f"{tool_id}.py")
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            logger.info(f"[InternalTools] 工具脚本保存成功，路径: {script_path}")
            return script_path
        except Exception as e:
            logger.error(f"[InternalTools] 保存工具脚本失败: {str(e)}", exc_info=True)
            raise
    
    def _load_tool_script(self, tool_id: str) -> Optional[str]:
        """加载工具脚本"""
        logger.debug(f"[InternalTools] 加载工具脚本，ID: {tool_id}")
        
        try:
            script_path = os.path.join(self.tools_dir, f"{tool_id}.py")
            if os.path.exists(script_path):
                with open(script_path, 'r', encoding='utf-8') as f:
                    script_content = f.read()
                logger.info(f"[InternalTools] 工具脚本加载成功，路径: {script_path}")
                return script_content
            else:
                logger.warning(f"[InternalTools] 工具脚本不存在，ID: {tool_id}")
                return None
        except Exception as e:
            logger.error(f"[InternalTools] 加载工具脚本失败: {str(e)}", exc_info=True)
            return None
    
    def _delete_tool_script(self, tool_id: str) -> bool:
        """删除工具脚本"""
        logger.debug(f"[InternalTools] 删除工具脚本，ID: {tool_id}")
        
        try:
            script_path = os.path.join(self.tools_dir, f"{tool_id}.py")
            if os.path.exists(script_path):
                os.remove(script_path)
                logger.info(f"[InternalTools] 工具脚本删除成功，路径: {script_path}")
                return True
            else:
                logger.warning(f"[InternalTools] 工具脚本不存在，ID: {tool_id}")
                return False
        except Exception as e:
            logger.error(f"[InternalTools] 删除工具脚本失败: {str(e)}", exc_info=True)
            return False
    
    def add_tool(self, tool_data: Dict[str, Any]) -> Dict[str, Any]:
        """添加工具"""
        logger.info("[InternalTools] 添加工具")
        
        try:
            # 验证工具数据
            validated_data = self._validate_tool_data(tool_data)
            
            # 检查工具名称是否已存在
            for existing_id, existing_tool in self.tools.items():
                if existing_tool["name"] == validated_data["name"]:
                    return {
                        "success": False,
                        "error": f"工具名称已存在: {validated_data['name']}"
                    }
            
            # 保存工具脚本
            script_path = self._save_tool_script(validated_data["id"], validated_data["script"])
            
            # 添加到工具列表
            self.tools[validated_data["id"]] = {
                "id": validated_data["id"],
                "name": validated_data["name"],
                "description": validated_data["description"],
                "inputs": validated_data["inputs"],
                "outputs": validated_data["outputs"],
                "script_path": script_path,
                "created_at": validated_data["created_at"],
                "updated_at": validated_data["updated_at"],
                "enabled": validated_data["enabled"],
                "version": validated_data["version"]
            }
            
            # 保存工具数据
            if self._save_tools():
                logger.info(f"[InternalTools] 工具添加成功，ID: {validated_data['id']}")
                return {
                    "success": True,
                    "message": "工具添加成功",
                    "tool_id": validated_data["id"]
                }
            else:
                # 如果保存失败，删除已保存的脚本
                self._delete_tool_script(validated_data["id"])
                return {
                    "success": False,
                    "error": "保存工具数据失败"
                }
                
        except Exception as e:
            logger.error(f"[InternalTools] 添加工具失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def get_tool(self, tool_id: str) -> Dict[str, Any]:
        """获取工具"""
        logger.info(f"[InternalTools] 获取工具，ID: {tool_id}")
        
        try:
            if tool_id not in self.tools:
                return {
                    "success": False,
                    "error": "工具不存在"
                }
            
            tool = self.tools[tool_id].copy()
            
            # 加载脚本内容
            script_content = self._load_tool_script(tool_id)
            if script_content:
                tool["script"] = script_content
            else:
                tool["script"] = ""
            
            logger.info(f"[InternalTools] 工具获取成功，ID: {tool_id}")
            return {
                "success": True,
                "data": tool
            }
            
        except Exception as e:
            logger.error(f"[InternalTools] 获取工具失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def update_tool(self, tool_id: str, tool_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新工具"""
        logger.info(f"[InternalTools] 更新工具，ID: {tool_id}")
        
        try:
            if tool_id not in self.tools:
                return {
                    "success": False,
                    "error": "工具不存在"
                }
            
            # 验证工具数据
            validated_data = self._validate_tool_data(tool_data)
            
            # 检查工具名称是否与其他工具冲突
            for existing_id, existing_tool in self.tools.items():
                if existing_id != tool_id and existing_tool["name"] == validated_data["name"]:
                    return {
                        "success": False,
                        "error": f"工具名称已存在: {validated_data['name']}"
                    }
            
            # 更新工具脚本
            self._save_tool_script(tool_id, validated_data["script"])
            
            # 更新工具数据
            self.tools[tool_id].update({
                "name": validated_data["name"],
                "description": validated_data["description"],
                "inputs": validated_data["inputs"],
                "outputs": validated_data["outputs"],
                "updated_at": datetime.now().isoformat(),
                "enabled": validated_data.get("enabled", True),
                "version": validated_data.get("version", "1.0")
            })
            
            # 保存工具数据
            if self._save_tools():
                logger.info(f"[InternalTools] 工具更新成功，ID: {tool_id}")
                return {
                    "success": True,
                    "message": "工具更新成功",
                    "tool_id": tool_id
                }
            else:
                return {
                    "success": False,
                    "error": "保存工具数据失败"
                }
                
        except Exception as e:
            logger.error(f"[InternalTools] 更新工具失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def delete_tool(self, tool_id: str) -> Dict[str, Any]:
        """删除工具"""
        logger.info(f"[InternalTools] 删除工具，ID: {tool_id}")
        
        try:
            if tool_id not in self.tools:
                return {
                    "success": False,
                    "error": "工具不存在"
                }
            
            # 删除工具脚本
            self._delete_tool_script(tool_id)
            
            # 从工具列表中删除
            del self.tools[tool_id]
            
            # 保存工具数据
            if self._save_tools():
                logger.info(f"[InternalTools] 工具删除成功，ID: {tool_id}")
                return {
                    "success": True,
                    "message": "工具删除成功"
                }
            else:
                return {
                    "success": False,
                    "error": "保存工具数据失败"
                }
                
        except Exception as e:
            logger.error(f"[InternalTools] 删除工具失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def list_tools(self, limit: int = 100, offset: int = 0) -> Dict[str, Any]:
        """列出所有工具"""
        logger.info(f"[InternalTools] 列出所有工具，限制: {limit}, 偏移: {offset}")
        
        try:
            tools_list = list(self.tools.values())
            
            # 分页
            total_count = len(tools_list)
            tools_list = tools_list[offset:offset + limit]
            
            # 移除脚本内容（只保留元数据）
            for tool in tools_list:
                tool.pop("script_path", None)
            
            logger.info(f"[InternalTools] 成功列出 {len(tools_list)} 个工具")
            return {
                "success": True,
                "data": tools_list,
                "total_count": total_count,
                "offset": offset,
                "limit": limit
            }
            
        except Exception as e:
            logger.error(f"[InternalTools] 列出工具失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def execute_tool(self, tool_id: str, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具"""
        logger.info(f"[InternalTools] 执行工具，ID: {tool_id}")
        
        try:
            if tool_id not in self.tools:
                return {
                    "success": False,
                    "error": "工具不存在"
                }
            
            tool = self.tools[tool_id]
            
            if not tool.get("enabled", True):
                return {
                    "success": False,
                    "error": "工具已禁用"
                }
            
            # 加载脚本内容
            script_content = self._load_tool_script(tool_id)
            if not script_content:
                return {
                    "success": False,
                    "error": "工具脚本不存在"
                }
            
            # 创建临时文件执行脚本
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
                f.write(script_content)
                temp_script_path = f.name
            
            try:
                # 动态导入脚本
                spec = importlib.util.spec_from_file_location("tool_script", temp_script_path)
                if spec is None:
                    return {
                        "success": False,
                        "error": "无法加载工具脚本"
                    }
                tool_module = importlib.util.module_from_spec(spec)
                if spec.loader is None:
                    return {
                        "success": False,
                        "error": "工具脚本加载器不存在"
                    }
                spec.loader.exec_module(tool_module)
                
                # 验证输入参数
                tool_inputs = tool.get("inputs", [])
                for input_param in tool_inputs:
                    param_name = input_param.get("name")
                    param_required = input_param.get("required", False)
                    
                    if param_required and param_name not in inputs:
                        return {
                            "success": False,
                            "error": f"缺少必需参数: {param_name}"
                        }
                
                # 准备工具执行环境
                tool_env = {
                    "inputs": inputs,
                    "db_connection": None,
                    "logger": logging.getLogger(f"tool.{tool_id}")
                }
                
                # 尝试获取数据库连接
                try:
                    tool_env["db_connection"] = get_db_connection()
                    logger.info(f"[InternalTools] 为工具 {tool_id} 提供数据库连接")
                except Exception as e:
                    logger.warning(f"[InternalTools] 无法为工具 {tool_id} 提供数据库连接: {str(e)}")
                
                # 执行工具函数
                if hasattr(tool_module, "execute"):
                    # 如果工具有init函数，先执行初始化
                    if hasattr(tool_module, "init"):
                        try:
                            tool_module.init(tool_env)
                        except Exception as e:
                            logger.error(f"[InternalTools] 工具 {tool_id} 初始化失败: {str(e)}", exc_info=True)
                            return {
                                "success": False,
                                "error": f"工具初始化失败: {str(e)}"
                            }
                    
                    # 执行工具主函数
                    result = tool_module.execute(inputs)
                    
                    # 验证输出格式
                    tool_outputs = tool.get("outputs", [])
                    if isinstance(result, dict) and "success" in result:
                        return result
                    else:
                        return {
                            "success": True,
                            "data": result
                        }
                else:
                    return {
                        "success": False,
                        "error": "工具脚本缺少execute函数"
                    }
                    
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_script_path)
                except:
                    pass
                    
        except Exception as e:
            logger.error(f"[InternalTools] 执行工具失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def get_tools_stats(self) -> Dict[str, Any]:
        """获取工具统计信息"""
        logger.info("[InternalTools] 获取工具统计信息")
        
        try:
            total_count = len(self.tools)
            enabled_count = sum(1 for tool in self.tools.values() if tool.get("enabled", True))
            disabled_count = total_count - enabled_count
            
            return {
                "success": True,
                "data": {
                    "total_count": total_count,
                    "enabled_count": enabled_count,
                    "disabled_count": disabled_count
                }
            }
            
        except Exception as e:
            logger.error(f"[InternalTools] 获取工具统计信息失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}


def get_internal_tools_manager(storage_path: Optional[str] = None) -> InternalToolsManager:
    """获取内部工具管理器实例"""
    return InternalToolsManager(storage_path)