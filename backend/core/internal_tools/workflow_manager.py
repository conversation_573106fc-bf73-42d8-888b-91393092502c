"""
微工作流管理器 - 管理基于内置工具组合的工作流
"""

import logging
import os
import uuid
import json
from typing import Dict, Any, List, Optional
from datetime import datetime

from .builtin_tools import get_builtin_tool, list_builtin_tools

logger = logging.getLogger(__name__)

class WorkflowStep:
    """工作流步骤"""
    
    def __init__(self, step_id: str, tool_id: str, inputs: Dict[str, Any], 
                 output_mapping: Optional[Dict[str, str]] = None):
        self.step_id = step_id
        self.tool_id = tool_id
        self.inputs = inputs
        self.output_mapping = output_mapping or {}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "step_id": self.step_id,
            "tool_id": self.tool_id,
            "inputs": self.inputs,
            "output_mapping": self.output_mapping
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkflowStep':
        return cls(
            step_id=data["step_id"],
            tool_id=data["tool_id"],
            inputs=data["inputs"],
            output_mapping=data.get("output_mapping", {})
        )


class Workflow:
    """微工作流"""
    
    def __init__(self, workflow_id: str, name: str, description: str, 
                 steps: List[WorkflowStep], inputs: List[Dict[str, Any]] = None,
                 outputs: List[Dict[str, Any]] = None, enabled: bool = True):
        self.workflow_id = workflow_id
        self.name = name
        self.description = description
        self.steps = steps
        self.inputs = inputs or []  # 工作流级别的输入参数定义
        self.outputs = outputs or []  # 工作流级别的输出参数定义
        self.enabled = enabled
        self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.workflow_id,
            "name": self.name,
            "description": self.description,
            "inputs": self.inputs,
            "outputs": self.outputs,
            "steps": [step.to_dict() for step in self.steps],
            "enabled": self.enabled,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "type": "workflow"
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Workflow':
        workflow = cls(
            workflow_id=data["id"],
            name=data["name"],
            description=data["description"],
            steps=[WorkflowStep.from_dict(step_data) for step_data in data["steps"]],
            inputs=data.get("inputs", []),
            outputs=data.get("outputs", []),
            enabled=data.get("enabled", True)
        )
        workflow.created_at = data.get("created_at", datetime.now().isoformat())
        workflow.updated_at = data.get("updated_at", datetime.now().isoformat())
        return workflow


class WorkflowManager:
    """微工作流管理器"""
    
    def __init__(self, storage_path: Optional[str] = None):
        """初始化工作流管理器"""
        logger.info("[Workflow] 初始化微工作流管理器")
        
        # 设置存储路径
        if not storage_path:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            storage_path = os.path.join(base_dir, "internal_tools")
        
        self.storage_path = storage_path
        self.workflows_file = os.path.join(storage_path, "workflows.json")
        
        # 创建必要的目录
        os.makedirs(self.storage_path, exist_ok=True)
        
        # 初始化工作流数据
        self.workflows = self._load_workflows()
        
        logger.info(f"[Workflow] 微工作流管理器初始化完成，存储路径: {self.storage_path}")
    
    def _load_workflows(self) -> Dict[str, Workflow]:
        """加载工作流数据"""
        logger.debug("[Workflow] 加载工作流数据")
        
        try:
            if os.path.exists(self.workflows_file):
                with open(self.workflows_file, 'r', encoding='utf-8') as f:
                    workflows_data = json.load(f)
                
                workflows = {}
                for workflow_id, workflow_data in workflows_data.items():
                    workflows[workflow_id] = Workflow.from_dict(workflow_data)
                
                logger.info(f"[Workflow] 成功加载 {len(workflows)} 个工作流")
                return workflows
            else:
                logger.info("[Workflow] 工作流数据文件不存在，创建空数据")
                return {}
        except Exception as e:
            logger.error(f"[Workflow] 加载工作流数据失败: {str(e)}", exc_info=True)
            return {}
    
    def _save_workflows(self) -> bool:
        """保存工作流数据"""
        logger.debug("[Workflow] 保存工作流数据")
        
        try:
            workflows_data = {}
            for workflow_id, workflow in self.workflows.items():
                workflows_data[workflow_id] = workflow.to_dict()
            
            with open(self.workflows_file, 'w', encoding='utf-8') as f:
                json.dump(workflows_data, f, ensure_ascii=False, indent=2)
            
            logger.info("[Workflow] 工作流数据保存成功")
            return True
        except Exception as e:
            logger.error(f"[Workflow] 保存工作流数据失败: {str(e)}", exc_info=True)
            return False
    
    def create_workflow(self, name: str, description: str, steps: List[Dict[str, Any]], 
                       inputs: List[Dict[str, Any]] = None, outputs: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """创建工作流"""
        logger.info(f"[Workflow] 创建工作流: {name}")
        
        try:
            # 验证工作流名称
            for existing_workflow in self.workflows.values():
                if existing_workflow.name == name:
                    return {
                        "success": False,
                        "error": f"工作流名称已存在: {name}"
                    }
            
            # 验证步骤
            workflow_steps = []
            for i, step_data in enumerate(steps):
                if "tool_id" not in step_data:
                    return {
                        "success": False,
                        "error": f"步骤 {i+1} 缺少 tool_id"
                    }
                
                tool_id = step_data["tool_id"]
                tool = get_builtin_tool(tool_id)
                if not tool:
                    return {
                        "success": False,
                        "error": f"步骤 {i+1} 使用了不存在的工具: {tool_id}"
                    }
                
                step = WorkflowStep(
                    step_id=step_data.get("step_id", f"step_{i+1}"),
                    tool_id=tool_id,
                    inputs=step_data.get("inputs", {}),
                    output_mapping=step_data.get("output_mapping", {})
                )
                workflow_steps.append(step)
            
            # 创建工作流
            workflow_id = str(uuid.uuid4())
            workflow = Workflow(
                workflow_id=workflow_id,
                name=name,
                description=description,
                steps=workflow_steps,
                inputs=inputs or [],
                outputs=outputs or []
            )
            
            # 保存工作流
            self.workflows[workflow_id] = workflow
            
            if self._save_workflows():
                logger.info(f"[Workflow] 工作流创建成功，ID: {workflow_id}")
                return {
                    "success": True,
                    "message": "工作流创建成功",
                    "workflow_id": workflow_id
                }
            else:
                # 如果保存失败，删除已创建的工作流
                del self.workflows[workflow_id]
                return {
                    "success": False,
                    "error": "保存工作流数据失败"
                }
                
        except Exception as e:
            logger.error(f"[Workflow] 创建工作流失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def get_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """获取工作流"""
        logger.info(f"[Workflow] 获取工作流，ID: {workflow_id}")
        
        try:
            if workflow_id not in self.workflows:
                return {
                    "success": False,
                    "error": "工作流不存在"
                }
            
            workflow = self.workflows[workflow_id]
            
            logger.info(f"[Workflow] 工作流获取成功，ID: {workflow_id}")
            return {
                "success": True,
                "data": workflow.to_dict()
            }
            
        except Exception as e:
            logger.error(f"[Workflow] 获取工作流失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def update_workflow(self, workflow_id: str, name: str, description: str, 
                       steps: List[Dict[str, Any]], inputs: List[Dict[str, Any]] = None, 
                       outputs: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """更新工作流"""
        logger.info(f"[Workflow] 更新工作流，ID: {workflow_id}")
        
        try:
            if workflow_id not in self.workflows:
                return {
                    "success": False,
                    "error": "工作流不存在"
                }
            
            # 检查工作流名称是否与其他工作流冲突
            for existing_id, existing_workflow in self.workflows.items():
                if existing_id != workflow_id and existing_workflow.name == name:
                    return {
                        "success": False,
                        "error": f"工作流名称已存在: {name}"
                    }
            
            # 验证步骤
            workflow_steps = []
            for i, step_data in enumerate(steps):
                if "tool_id" not in step_data:
                    return {
                        "success": False,
                        "error": f"步骤 {i+1} 缺少 tool_id"
                    }
                
                tool_id = step_data["tool_id"]
                tool = get_builtin_tool(tool_id)
                if not tool:
                    return {
                        "success": False,
                        "error": f"步骤 {i+1} 使用了不存在的工具: {tool_id}"
                    }
                
                step = WorkflowStep(
                    step_id=step_data.get("step_id", f"step_{i+1}"),
                    tool_id=tool_id,
                    inputs=step_data.get("inputs", {}),
                    output_mapping=step_data.get("output_mapping", {})
                )
                workflow_steps.append(step)
            
            # 更新工作流
            workflow = self.workflows[workflow_id]
            workflow.name = name
            workflow.description = description
            workflow.steps = workflow_steps
            workflow.inputs = inputs or []
            workflow.outputs = outputs or []
            workflow.updated_at = datetime.now().isoformat()
            
            # 保存工作流数据
            if self._save_workflows():
                logger.info(f"[Workflow] 工作流更新成功，ID: {workflow_id}")
                return {
                    "success": True,
                    "message": "工作流更新成功",
                    "workflow_id": workflow_id
                }
            else:
                return {
                    "success": False,
                    "error": "保存工作流数据失败"
                }
                
        except Exception as e:
            logger.error(f"[Workflow] 更新工作流失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def delete_workflow(self, workflow_id: str) -> Dict[str, Any]:
        """删除工作流"""
        logger.info(f"[Workflow] 删除工作流，ID: {workflow_id}")
        
        try:
            if workflow_id not in self.workflows:
                return {
                    "success": False,
                    "error": "工作流不存在"
                }
            
            # 从工作流列表中删除
            del self.workflows[workflow_id]
            
            # 保存工作流数据
            if self._save_workflows():
                logger.info(f"[Workflow] 工作流删除成功，ID: {workflow_id}")
                return {
                    "success": True,
                    "message": "工作流删除成功"
                }
            else:
                return {
                    "success": False,
                    "error": "保存工作流数据失败"
                }
                
        except Exception as e:
            logger.error(f"[Workflow] 删除工作流失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def list_workflows(self, limit: int = 100, offset: int = 0) -> Dict[str, Any]:
        """列出所有工作流"""
        logger.info(f"[Workflow] 列出所有工作流，限制: {limit}, 偏移: {offset}")
        
        try:
            workflows_list = list(self.workflows.values())
            
            # 分页
            total_count = len(workflows_list)
            workflows_list = workflows_list[offset:offset + limit]
            
            # 转换为字典格式
            workflows_data = [workflow.to_dict() for workflow in workflows_list]
            
            logger.info(f"[Workflow] 成功列出 {len(workflows_data)} 个工作流")
            return {
                "success": True,
                "data": workflows_data,
                "total_count": total_count,
                "offset": offset,
                "limit": limit
            }
            
        except Exception as e:
            logger.error(f"[Workflow] 列出工作流失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def execute_workflow(self, workflow_id: str, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行工作流"""
        logger.info(f"[Workflow] 执行工作流，ID: {workflow_id}")
        
        try:
            if workflow_id not in self.workflows:
                return {
                    "success": False,
                    "error": "工作流不存在"
                }
            
            workflow = self.workflows[workflow_id]
            
            if not workflow.enabled:
                return {
                    "success": False,
                    "error": "工作流已禁用"
                }
            
            # 执行工作流步骤
            context = inputs.copy()  # 工作流上下文，用于步骤间数据传递
            step_results = []
            
            for i, step in enumerate(workflow.steps):
                logger.info(f"[Workflow] 执行步骤 {i+1}: {step.tool_id}")
                
                # 获取工具
                tool = get_builtin_tool(step.tool_id)
                if not tool:
                    return {
                        "success": False,
                        "error": f"步骤 {i+1} 使用了不存在的工具: {step.tool_id}"
                    }
                
                # 准备步骤输入
                step_inputs = {}
                for param_name, param_value in step.inputs.items():
                    resolved_value = self._resolve_parameter_value(param_value, context, i+1)
                    step_inputs[param_name] = resolved_value
                
                # 执行工具
                try:
                    step_result = tool.execute(step_inputs)
                    
                    if not step_result.get("success", False):
                        return {
                            "success": False,
                            "error": f"步骤 {i+1} 执行失败: {step_result.get('error', '未知错误')}"
                        }
                    
                    # 保存步骤结果
                    step_output = {
                        "step_id": step.step_id,
                        "tool_id": step.tool_id,
                        "inputs": step_inputs,
                        "outputs": step_result.get("data", {}),
                        "success": True
                    }
                    step_results.append(step_output)
                    
                    # 更新上下文
                    step_data = step_result.get("data", {})
                    
                    # 将步骤输出添加到上下文，使用步骤ID作为前缀
                    for output_key, output_value in step_data.items():
                        context[f"{step.step_id}_{output_key}"] = output_value
                    
                    # 应用输出映射（可选，用于创建别名）
                    if step.output_mapping:
                        for output_key, context_key in step.output_mapping.items():
                            if output_key in step_data:
                                context[context_key] = step_data[output_key]
                    
                    logger.info(f"[Workflow] 步骤 {i+1} 执行成功")
                    
                except Exception as step_error:
                    logger.error(f"[Workflow] 步骤 {i+1} 执行异常: {str(step_error)}", exc_info=True)
                    return {
                        "success": False,
                        "error": f"步骤 {i+1} 执行异常: {str(step_error)}"
                    }
            
            logger.info(f"[Workflow] 工作流执行成功，ID: {workflow_id}")
            return {
                "success": True,
                "data": {
                    "workflow_id": workflow_id,
                    "step_results": step_results,
                    "final_context": context
                }
            }
            
        except Exception as e:
            logger.error(f"[Workflow] 执行工作流失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def _resolve_parameter_value(self, param_value: Any, context: Dict[str, Any], step_num: int) -> Any:
        """解析参数值，支持多种引用方式"""
        if not isinstance(param_value, str):
            return param_value
        
        # 支持从工作流输入中引用变量，格式：${variable_name}
        if param_value.startswith("${") and param_value.endswith("}"):
            var_name = param_value[2:-1]
            if var_name in context:
                return context[var_name]
            else:
                logger.warning(f"[Workflow] 步骤 {step_num} 引用了不存在的变量: {var_name}")
                return None
        
        # 支持从前置步骤输出中引用，格式：@step_id.output_name 或 @step_1.output_name
        if param_value.startswith("@"):
            parts = param_value[1:].split(".", 1)
            if len(parts) == 2:
                step_ref, output_name = parts
                
                # 查找引用的步骤输出
                step_output_key = f"{step_ref}_{output_name}"
                if step_output_key in context:
                    return context[step_output_key]
                else:
                    logger.warning(f"[Workflow] 步骤 {step_num} 引用了不存在的步骤输出: {param_value}")
                    return None
        
        # 支持数组引用，格式：${array_var}[*] 表示提取数组中所有元素的某个字段
        if "${" in param_value and "}[" in param_value:
            # 这里可以实现更复杂的数组处理逻辑
            pass
        
        return param_value
    
    def get_builtin_tools(self) -> Dict[str, Any]:
        """获取所有内置工具"""
        try:
            tools = list_builtin_tools()
            return {
                "success": True,
                "data": tools
            }
        except Exception as e:
            logger.error(f"[Workflow] 获取内置工具失败: {str(e)}", exc_info=True)
            return {"success": False, "error": str(e)}


def get_workflow_manager(storage_path: Optional[str] = None) -> WorkflowManager:
    """获取工作流管理器实例"""
    return WorkflowManager(storage_path)