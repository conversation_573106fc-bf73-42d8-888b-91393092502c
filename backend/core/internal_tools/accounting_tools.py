"""
会计科目相关工具
"""

import logging
from typing import Dict, Any, List, Optional
from core.db import get_db_connection
from .base import BuiltinTool

logger = logging.getLogger(__name__)


class QuerySubjectAccountsTool(BuiltinTool):
    """查询会计科目工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_subject_accounts",
            name="查询会计科目",
            description="根据条件查询会计科目信息",
            inputs=[
                {"name": "account_code", "type": "string", "description": "科目编码", "required": False},
                {"name": "account_name", "type": "string", "description": "科目名称", "required": False},
                {"name": "category", "type": "string", "description": "科目类别", "required": False},
                {"name": "level", "type": "number", "description": "科目级次", "required": False},
                {"name": "parent_code", "type": "string", "description": "父级科目编码", "required": False},
                {"name": "is_leaf", "type": "boolean", "description": "是否末级科目", "required": False},
                {"name": "status", "type": "string", "description": "科目状态", "required": False}
            ],
            outputs=[
                {"name": "accounts", "type": "array", "description": "会计科目列表"},
                {"name": "count", "type": "number", "description": "查询到的科目数量"}
            ],
            category="accounting"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询会计科目"""
        try:
            self.validate_inputs(inputs)
            
            account_code = inputs.get("account_code")
            account_name = inputs.get("account_name")
            category = inputs.get("category")
            level = inputs.get("level")
            parent_code = inputs.get("parent_code")
            is_leaf = inputs.get("is_leaf")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if account_code:
                conditions.append("code = ?")
                params.append(account_code)
            
            if account_name:
                conditions.append("name LIKE ?")
                params.append(f"%{account_name}%")
            
            if category:
                conditions.append("category = ?")
                params.append(category)
            
            if level is not None:
                conditions.append("level = ?")
                params.append(level)
            
            if parent_code:
                conditions.append("parent_code = ?")
                params.append(parent_code)
            
            if is_leaf is not None:
                conditions.append("is_leaf = ?")
                params.append(1 if is_leaf else 0)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT code, name, level, parent_code, category, direction, 
                       aux, is_leaf, status, remark, quantity
                FROM subject_accounts 
                {where_clause}
                ORDER BY code
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            accounts = []
            for row in rows:
                accounts.append({
                    "code": row[0],
                    "name": row[1],
                    "level": row[2],
                    "parent_code": row[3],
                    "category": row[4],
                    "direction": row[5],
                    "aux": row[6],
                    "is_leaf": bool(row[7]),
                    "status": row[8],
                    "remark": row[9],
                    "quantity": bool(row[10])
                })
            
            return {
                "success": True,
                "data": {
                    "accounts": accounts,
                    "count": len(accounts)
                }
            }
            
        except Exception as e:
            logger.error(f"查询会计科目失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class SubjectAccountStatisticsTool(BuiltinTool):
    """会计科目统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="subject_account_statistics",
            name="会计科目统计分析",
            description="对会计科目进行统计分析",
            inputs=[
                {"name": "group_by", "type": "string", "description": "分组字段(category/level/direction/status)", "required": False},
                {"name": "category", "type": "string", "description": "筛选科目类别", "required": False},
                {"name": "level", "type": "number", "description": "筛选科目级次", "required": False},
                {"name": "parent_code", "type": "string", "description": "筛选父级科目", "required": False}
            ],
            outputs=[
                {"name": "total_count", "type": "number", "description": "科目总数"},
                {"name": "group_statistics", "type": "object", "description": "分组统计结果"},
                {"name": "level_distribution", "type": "object", "description": "级次分布"},
                {"name": "leaf_ratio", "type": "object", "description": "末级科目比例"}
            ],
            category="accounting"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行会计科目统计分析"""
        try:
            self.validate_inputs(inputs)
            
            group_by = inputs.get("group_by", "category")
            category = inputs.get("category")
            level = inputs.get("level")
            parent_code = inputs.get("parent_code")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if category:
                conditions.append("category = ?")
                params.append(category)
            
            if level is not None:
                conditions.append("level = ?")
                params.append(level)
            
            if parent_code:
                conditions.append("parent_code = ?")
                params.append(parent_code)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 总数统计
            total_query = f"SELECT COUNT(*) FROM subject_accounts {where_clause}"
            cursor.execute(total_query, params)
            total_count = cursor.fetchone()[0]
            
            # 分组统计
            valid_group_fields = ["category", "level", "direction", "status"]
            if group_by not in valid_group_fields:
                group_by = "category"
            
            group_query = f"""
                SELECT {group_by}, COUNT(*) as count
                FROM subject_accounts 
                {where_clause}
                GROUP BY {group_by}
                ORDER BY count DESC
            """
            cursor.execute(group_query, params)
            group_rows = cursor.fetchall()
            
            group_statistics = {}
            for row in group_rows:
                key = row[0] if row[0] is not None else "未分类"
                group_statistics[key] = row[1]
            
            # 级次分布
            level_query = f"""
                SELECT level, COUNT(*) as count
                FROM subject_accounts 
                {where_clause}
                GROUP BY level
                ORDER BY level
            """
            cursor.execute(level_query, params)
            level_rows = cursor.fetchall()
            
            level_distribution = {}
            for row in level_rows:
                level_distribution[f"级次{row[0]}"] = row[1]
            
            # 末级科目比例
            leaf_query = f"""
                SELECT is_leaf, COUNT(*) as count
                FROM subject_accounts 
                {where_clause}
                GROUP BY is_leaf
            """
            cursor.execute(leaf_query, params)
            leaf_rows = cursor.fetchall()
            
            leaf_count = 0
            non_leaf_count = 0
            for row in leaf_rows:
                if row[0] == 1:
                    leaf_count = row[1]
                else:
                    non_leaf_count = row[1]
            
            leaf_ratio = {
                "leaf_count": leaf_count,
                "non_leaf_count": non_leaf_count,
                "leaf_percentage": round(leaf_count / total_count * 100, 2) if total_count > 0 else 0,
                "non_leaf_percentage": round(non_leaf_count / total_count * 100, 2) if total_count > 0 else 0
            }
            
            return {
                "success": True,
                "data": {
                    "total_count": total_count,
                    "group_statistics": group_statistics,
                    "level_distribution": level_distribution,
                    "leaf_ratio": leaf_ratio
                }
            }
            
        except Exception as e:
            logger.error(f"会计科目统计分析失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }