"""
初始化示例工作流
"""

import logging
from .workflow_manager import get_workflow_manager

logger = logging.getLogger(__name__)

def create_sample_workflows():
    """创建示例工作流"""
    workflow_manager = get_workflow_manager()
    
    # 示例工作流1：部门员工统计报告
    sample_workflow_1 = {
        "name": "部门员工统计报告",
        "description": "统计指定部门的员工数量和基本信息",
        "inputs": [
            {"name": "department", "type": "string", "description": "部门名称", "required": True}
        ],
        "outputs": [
            {"name": "employees", "type": "array", "description": "员工列表"},
            {"name": "employee_count", "type": "number", "description": "员工数量"},
            {"name": "dept_stats", "type": "object", "description": "部门统计信息"}
        ],
        "steps": [
            {
                "step_id": "step_1",
                "tool_id": "query_employee_info",
                "inputs": {
                    "department": "${department}"
                },
                "output_mapping": {
                    "employee_info": "employees",
                    "count": "employee_count"
                }
            },
            {
                "step_id": "step_2", 
                "tool_id": "count_employees",
                "inputs": {
                    "department": "${department}"
                },
                "output_mapping": {
                    "total_count": "total_employees",
                    "department_breakdown": "dept_stats"
                }
            }
        ]
    }
    
    # 示例工作流2：部门年度报销分析
    sample_workflow_2 = {
        "name": "部门年度报销分析",
        "description": "分析指定部门的年度报销情况，包括总额和月度分布",
        "inputs": [
            {"name": "department", "type": "string", "description": "部门名称", "required": True},
            {"name": "year", "type": "number", "description": "年份", "required": False}
        ],
        "outputs": [
            {"name": "total_expenses", "type": "number", "description": "总报销金额"},
            {"name": "expense_types", "type": "object", "description": "按费用类型分组"},
            {"name": "monthly_expenses", "type": "object", "description": "按月份分组"}
        ],
        "steps": [
            {
                "step_id": "step_1",
                "tool_id": "query_department_expenses",
                "inputs": {
                    "department": "${department}",
                    "year": "${year}"
                },
                "output_mapping": {
                    "total_amount": "total_expenses",
                    "expense_breakdown": "expense_types",
                    "monthly_breakdown": "monthly_expenses"
                }
            }
        ]
    }
    
    # 示例工作流3：凭证搜索与统计
    sample_workflow_3 = {
        "name": "凭证搜索与统计",
        "description": "根据关键词搜索凭证并计算统计数据",
        "inputs": [
            {"name": "keyword", "type": "string", "description": "搜索关键词", "required": False},
            {"name": "date_from", "type": "string", "description": "开始日期", "required": False},
            {"name": "date_to", "type": "string", "description": "结束日期", "required": False}
        ],
        "outputs": [
            {"name": "found_vouchers", "type": "array", "description": "找到的凭证"},
            {"name": "voucher_count", "type": "number", "description": "凭证数量"},
            {"name": "amount_statistics", "type": "object", "description": "金额统计信息"}
        ],
        "steps": [
            {
                "step_id": "step_1",
                "tool_id": "search_vouchers",
                "inputs": {
                    "keyword": "${keyword}",
                    "date_from": "${date_from}",
                    "date_to": "${date_to}",
                    "limit": "50"
                },
                "output_mapping": {
                    "vouchers": "found_vouchers",
                    "total_count": "voucher_count",
                    "total_amount": "voucher_total"
                }
            }
        ]
    }
    
    # 示例工作流4：高额报销筛选与分析
    sample_workflow_4 = {
        "name": "高额报销筛选与分析",
        "description": "筛选出超过指定金额的报销凭证并进行统计分析",
        "inputs": [
            {"name": "date_from", "type": "string", "description": "开始日期", "required": False},
            {"name": "date_to", "type": "string", "description": "结束日期", "required": False},
            {"name": "min_amount", "type": "number", "description": "最小金额阈值", "required": True}
        ],
        "outputs": [
            {"name": "high_amount_vouchers", "type": "array", "description": "高额凭证列表"},
            {"name": "high_amount_count", "type": "number", "description": "高额凭证数量"},
            {"name": "statistics", "type": "object", "description": "统计信息"}
        ],
        "steps": [
            {
                "step_id": "step_1",
                "tool_id": "search_vouchers",
                "inputs": {
                    "date_from": "${date_from}",
                    "date_to": "${date_to}",
                    "limit": "100"
                },
                "output_mapping": {}
            },
            {
                "step_id": "step_2",
                "tool_id": "filter_data",
                "inputs": {
                    "data": "@step_1.vouchers",
                    "field": "total_amount",
                    "operator": "gte",
                    "value": "${min_amount}"
                },
                "output_mapping": {
                    "filtered_data": "high_amount_vouchers",
                    "count": "high_amount_count"
                }
            }
        ]
    }
    
    sample_workflows = [
        sample_workflow_1,
        sample_workflow_2, 
        sample_workflow_3,
        sample_workflow_4
    ]
    
    created_count = 0
    for workflow_data in sample_workflows:
        try:
            result = workflow_manager.create_workflow(
                name=workflow_data["name"],
                description=workflow_data["description"],
                steps=workflow_data["steps"],
                inputs=workflow_data.get("inputs", []),
                outputs=workflow_data.get("outputs", [])
            )
            
            if result["success"]:
                logger.info(f"创建示例工作流成功: {workflow_data['name']}")
                created_count += 1
            else:
                logger.warning(f"创建示例工作流失败: {workflow_data['name']}, 错误: {result['error']}")
                
        except Exception as e:
            logger.error(f"创建示例工作流异常: {workflow_data['name']}, 错误: {str(e)}")
    
    logger.info(f"示例工作流创建完成，成功创建 {created_count} 个工作流")
    return created_count


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建示例工作流
    count = create_sample_workflows()
    print(f"成功创建 {count} 个示例工作流")