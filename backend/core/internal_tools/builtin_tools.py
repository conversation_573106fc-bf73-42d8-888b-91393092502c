"""
内置工具系统 - 提供一系列预定义的基础工具
"""

import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from core.db import get_db_connection

logger = logging.getLogger(__name__)

class BuiltinTool:
    """内置工具基类"""
    
    def __init__(self, tool_id: str, name: str, description: str, 
                 inputs: List[Dict], outputs: List[Dict], category: str = "general"):
        self.tool_id = tool_id
        self.name = name
        self.description = description
        self.inputs = inputs
        self.outputs = outputs
        self.category = category
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具，子类需要实现此方法"""
        raise NotImplementedError("子类必须实现execute方法")
    
    def validate_inputs(self, inputs: Dict[str, Any]) -> bool:
        """验证输入参数"""
        for input_def in self.inputs:
            param_name = input_def.get("name")
            required = input_def.get("required", False)
            
            if required and param_name not in inputs:
                raise ValueError(f"缺少必需参数: {param_name}")
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.tool_id,
            "name": self.name,
            "description": self.description,
            "inputs": self.inputs,
            "outputs": self.outputs,
            "category": self.category,
            "type": "builtin"
        }


class QueryEmployeeInfoTool(BuiltinTool):
    """查询员工信息工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_employee_info",
            name="查询员工信息",
            description="根据员工ID或姓名查询员工详细信息",
            inputs=[
                {"name": "employee_id", "type": "string", "description": "员工ID", "required": False},
                {"name": "employee_name", "type": "string", "description": "员工姓名", "required": False},
                {"name": "department", "type": "string", "description": "部门名称", "required": False}
            ],
            outputs=[
                {"name": "employee_info", "type": "object", "description": "员工信息对象"},
                {"name": "count", "type": "number", "description": "查询到的员工数量"}
            ],
            category="employee"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询员工信息"""
        try:
            self.validate_inputs(inputs)
            
            employee_id = inputs.get("employee_id")
            employee_name = inputs.get("employee_name")
            department = inputs.get("department")
            
            if not any([employee_id, employee_name, department]):
                return {
                    "success": False,
                    "error": "至少需要提供员工ID、姓名或部门中的一个参数"
                }
            
            # 构建查询条件
            conditions = []
            params = []
            
            if employee_id:
                conditions.append("id = ?")
                params.append(employee_id)
            
            if employee_name:
                conditions.append("name LIKE ?")
                params.append(f"%{employee_name}%")
            
            if department:
                conditions.append("department = ?")
                params.append(department)
            
            where_clause = " AND ".join(conditions)
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT id, name, department, position, salary, hire_date, status
                FROM role_staff 
                WHERE {where_clause}
                ORDER BY name
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            employees = []
            for row in rows:
                employees.append({
                    "id": row[0],
                    "name": row[1],
                    "department": row[2],
                    "position": row[3],
                    "salary": row[4],
                    "hire_date": row[5],
                    "status": row[6]
                })
            
            return {
                "success": True,
                "data": {
                    "employee_info": employees,
                    "count": len(employees)
                }
            }
            
        except Exception as e:
            logger.error(f"查询员工信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class CountEmployeesTool(BuiltinTool):
    """统计员工数量工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="count_employees",
            name="统计员工数量",
            description="统计指定条件下的员工数量",
            inputs=[
                {"name": "department", "type": "string", "description": "部门名称", "required": False},
                {"name": "position", "type": "string", "description": "职位", "required": False},
                {"name": "status", "type": "string", "description": "员工状态", "required": False}
            ],
            outputs=[
                {"name": "total_count", "type": "number", "description": "员工总数"},
                {"name": "department_breakdown", "type": "object", "description": "按部门分组统计"}
            ],
            category="employee"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行员工数量统计"""
        try:
            self.validate_inputs(inputs)
            
            department = inputs.get("department")
            position = inputs.get("position")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if department:
                conditions.append("department = ?")
                params.append(department)
            
            if position:
                conditions.append("position = ?")
                params.append(position)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 总数统计
            total_query = f"SELECT COUNT(*) FROM role_staff {where_clause}"
            cursor.execute(total_query, params)
            total_count = cursor.fetchone()[0]
            
            # 按部门分组统计
            dept_query = f"""
                SELECT department, COUNT(*) as count
                FROM role_staff 
                {where_clause}
                GROUP BY department
                ORDER BY count DESC
            """
            cursor.execute(dept_query, params)
            dept_rows = cursor.fetchall()
            
            department_breakdown = {}
            for row in dept_rows:
                department_breakdown[row[0]] = row[1]
            
            return {
                "success": True,
                "data": {
                    "total_count": total_count,
                    "department_breakdown": department_breakdown
                }
            }
            
        except Exception as e:
            logger.error(f"统计员工数量失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class QueryDepartmentExpensesTool(BuiltinTool):
    """查询部门年度报销总额工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_department_expenses",
            name="查询部门年度报销总额",
            description="根据部门和年份查询报销总额",
            inputs=[
                {"name": "department", "type": "string", "description": "部门名称", "required": True},
                {"name": "year", "type": "number", "description": "年份", "required": False},
                {"name": "expense_type", "type": "string", "description": "费用类型", "required": False}
            ],
            outputs=[
                {"name": "total_amount", "type": "number", "description": "总报销金额"},
                {"name": "expense_breakdown", "type": "object", "description": "按费用类型分组"},
                {"name": "monthly_breakdown", "type": "object", "description": "按月份分组"}
            ],
            category="finance"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行部门报销查询"""
        try:
            self.validate_inputs(inputs)
            
            department = inputs.get("department")
            year = inputs.get("year", datetime.now().year)
            expense_type = inputs.get("expense_type")
            
            # 构建查询条件
            conditions = ["v.department = ?"]
            params = [department]
            
            # 添加年份条件
            conditions.append("strftime('%Y', v.voucher_date) = ?")
            params.append(str(year))
            
            if expense_type:
                conditions.append("v.expense_type = ?")
                params.append(expense_type)
            
            where_clause = " AND ".join(conditions)
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 总金额查询
            total_query = f"""
                SELECT SUM(vd.credit_amount) as total
                FROM voucher v
                JOIN voucher_detail vd ON v.id = vd.voucher_id
                WHERE {where_clause}
            """
            cursor.execute(total_query, params)
            total_result = cursor.fetchone()
            total_amount = total_result[0] if total_result[0] else 0
            
            # 按费用类型分组
            type_query = f"""
                SELECT v.expense_type, SUM(vd.credit_amount) as amount
                FROM voucher v
                JOIN voucher_detail vd ON v.id = vd.voucher_id
                WHERE {where_clause}
                GROUP BY v.expense_type
                ORDER BY amount DESC
            """
            cursor.execute(type_query, params)
            type_rows = cursor.fetchall()
            
            expense_breakdown = {}
            for row in type_rows:
                expense_breakdown[row[0] or "未分类"] = float(row[1])
            
            # 按月份分组
            month_query = f"""
                SELECT strftime('%m', v.voucher_date) as month, 
                       SUM(vd.credit_amount) as amount
                FROM voucher v
                JOIN voucher_detail vd ON v.id = vd.voucher_id
                WHERE {where_clause}
                GROUP BY strftime('%m', v.voucher_date)
                ORDER BY month
            """
            cursor.execute(month_query, params)
            month_rows = cursor.fetchall()
            
            monthly_breakdown = {}
            for row in month_rows:
                month_name = f"{year}-{row[0].zfill(2)}"
                monthly_breakdown[month_name] = float(row[1])
            
            return {
                "success": True,
                "data": {
                    "total_amount": float(total_amount),
                    "expense_breakdown": expense_breakdown,
                    "monthly_breakdown": monthly_breakdown
                }
            }
            
        except Exception as e:
            logger.error(f"查询部门报销失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class SearchVouchersTool(BuiltinTool):
    """搜索凭证工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="search_vouchers",
            name="搜索凭证",
            description="根据多种条件搜索会计凭证",
            inputs=[
                {"name": "keyword", "type": "string", "description": "关键词搜索", "required": False},
                {"name": "date_from", "type": "string", "description": "开始日期(YYYY-MM-DD)", "required": False},
                {"name": "date_to", "type": "string", "description": "结束日期(YYYY-MM-DD)", "required": False},
                {"name": "amount_min", "type": "number", "description": "最小金额", "required": False},
                {"name": "amount_max", "type": "number", "description": "最大金额", "required": False},
                {"name": "department", "type": "string", "description": "部门", "required": False},
                {"name": "limit", "type": "number", "description": "返回数量限制", "required": False}
            ],
            outputs=[
                {"name": "vouchers", "type": "array", "description": "凭证列表"},
                {"name": "total_count", "type": "number", "description": "总数量"},
                {"name": "total_amount", "type": "number", "description": "总金额"}
            ],
            category="finance"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行凭证搜索"""
        try:
            self.validate_inputs(inputs)
            
            keyword = inputs.get("keyword")
            date_from = inputs.get("date_from")
            date_to = inputs.get("date_to")
            amount_min = inputs.get("amount_min")
            amount_max = inputs.get("amount_max")
            department = inputs.get("department")
            limit = inputs.get("limit", 100)
            
            # 构建查询条件
            conditions = []
            params = []
            
            if keyword:
                conditions.append("(v.description LIKE ? OR v.summary LIKE ?)")
                params.extend([f"%{keyword}%", f"%{keyword}%"])
            
            if date_from:
                conditions.append("v.voucher_date >= ?")
                params.append(date_from)
            
            if date_to:
                conditions.append("v.voucher_date <= ?")
                params.append(date_to)
            
            if department:
                conditions.append("v.department = ?")
                params.append(department)
            
            # 金额条件需要通过子查询处理
            amount_conditions = []
            if amount_min is not None:
                amount_conditions.append("total_amount >= ?")
                params.append(amount_min)
            
            if amount_max is not None:
                amount_conditions.append("total_amount <= ?")
                params.append(amount_max)
            
            where_clause = ""
            if conditions:
                where_clause = "WHERE " + " AND ".join(conditions)
            
            having_clause = ""
            if amount_conditions:
                having_clause = "HAVING " + " AND ".join(amount_conditions)
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 主查询
            query = f"""
                SELECT v.id, v.voucher_number, v.voucher_date, v.description, 
                       v.department, v.created_by, v.created_at,
                       SUM(vd.debit_amount) as total_amount
                FROM voucher v
                LEFT JOIN voucher_detail vd ON v.id = vd.voucher_id
                {where_clause}
                GROUP BY v.id, v.voucher_number, v.voucher_date, v.description, 
                         v.department, v.created_by, v.created_at
                {having_clause}
                ORDER BY v.voucher_date DESC, v.created_at DESC
                LIMIT ?
            """
            
            params.append(limit)
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            vouchers = []
            total_amount = 0
            for row in rows:
                voucher = {
                    "id": row[0],
                    "voucher_number": row[1],
                    "voucher_date": row[2],
                    "description": row[3],
                    "department": row[4],
                    "created_by": row[5],
                    "created_at": row[6],
                    "total_amount": float(row[7]) if row[7] else 0
                }
                vouchers.append(voucher)
                total_amount += voucher["total_amount"]
            
            return {
                "success": True,
                "data": {
                    "vouchers": vouchers,
                    "total_count": len(vouchers),
                    "total_amount": total_amount
                }
            }
            
        except Exception as e:
            logger.error(f"搜索凭证失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class CalculateStatisticsTool(BuiltinTool):
    """计算统计数据工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="calculate_statistics",
            name="计算统计数据",
            description="对数值数组进行统计计算",
            inputs=[
                {"name": "numbers", "type": "array", "description": "数值数组", "required": True},
                {"name": "operations", "type": "array", "description": "要执行的操作列表", "required": False}
            ],
            outputs=[
                {"name": "sum", "type": "number", "description": "总和"},
                {"name": "average", "type": "number", "description": "平均值"},
                {"name": "min", "type": "number", "description": "最小值"},
                {"name": "max", "type": "number", "description": "最大值"},
                {"name": "count", "type": "number", "description": "数量"}
            ],
            category="calculation"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行统计计算"""
        try:
            self.validate_inputs(inputs)
            
            numbers = inputs.get("numbers", [])
            operations = inputs.get("operations", ["sum", "average", "min", "max", "count"])
            
            if not numbers:
                return {
                    "success": False,
                    "error": "数值数组不能为空"
                }
            
            # 转换为数值类型
            try:
                numeric_values = [float(x) for x in numbers if x is not None]
            except (ValueError, TypeError):
                return {
                    "success": False,
                    "error": "数组中包含无效的数值"
                }
            
            if not numeric_values:
                return {
                    "success": False,
                    "error": "没有有效的数值"
                }
            
            # 计算统计值
            result = {}
            
            if "sum" in operations:
                result["sum"] = sum(numeric_values)
            
            if "average" in operations:
                result["average"] = sum(numeric_values) / len(numeric_values)
            
            if "min" in operations:
                result["min"] = min(numeric_values)
            
            if "max" in operations:
                result["max"] = max(numeric_values)
            
            if "count" in operations:
                result["count"] = len(numeric_values)
            
            return {
                "success": True,
                "data": result
            }
            
        except Exception as e:
            logger.error(f"计算统计数据失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class FilterDataTool(BuiltinTool):
    """数据过滤工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="filter_data",
            name="数据过滤",
            description="根据条件过滤数据数组",
            inputs=[
                {"name": "data", "type": "array", "description": "要过滤的数据数组", "required": True},
                {"name": "field", "type": "string", "description": "过滤字段", "required": True},
                {"name": "operator", "type": "string", "description": "操作符(eq,ne,gt,lt,gte,lte,contains)", "required": True},
                {"name": "value", "type": "string", "description": "比较值", "required": True}
            ],
            outputs=[
                {"name": "filtered_data", "type": "array", "description": "过滤后的数据"},
                {"name": "count", "type": "number", "description": "过滤后的数量"}
            ],
            category="data"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行数据过滤"""
        try:
            self.validate_inputs(inputs)
            
            data = inputs.get("data", [])
            field = inputs.get("field")
            operator = inputs.get("operator")
            value = inputs.get("value")
            
            if not data:
                return {
                    "success": True,
                    "data": {
                        "filtered_data": [],
                        "count": 0
                    }
                }
            
            filtered_data = []
            
            for item in data:
                if not isinstance(item, dict):
                    continue
                
                if field not in item:
                    continue
                
                item_value = item[field]
                
                # 执行比较操作
                match = False
                try:
                    if operator == "eq":
                        match = str(item_value) == str(value)
                    elif operator == "ne":
                        match = str(item_value) != str(value)
                    elif operator == "gt":
                        match = float(item_value) > float(value)
                    elif operator == "lt":
                        match = float(item_value) < float(value)
                    elif operator == "gte":
                        match = float(item_value) >= float(value)
                    elif operator == "lte":
                        match = float(item_value) <= float(value)
                    elif operator == "contains":
                        match = str(value).lower() in str(item_value).lower()
                except (ValueError, TypeError):
                    continue
                
                if match:
                    filtered_data.append(item)
            
            return {
                "success": True,
                "data": {
                    "filtered_data": filtered_data,
                    "count": len(filtered_data)
                }
            }
            
        except Exception as e:
            logger.error(f"数据过滤失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class QueryCompaniesTool(BuiltinTool):
    """查询公司信息工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_companies",
            name="查询公司信息",
            description="根据条件查询公司信息",
            inputs=[
                {"name": "company_id", "type": "number", "description": "公司ID", "required": False},
                {"name": "company_name", "type": "string", "description": "公司名称", "required": False},
                {"name": "industry", "type": "string", "description": "行业", "required": False},
                {"name": "company_size", "type": "string", "description": "公司规模", "required": False},
                {"name": "status", "type": "string", "description": "公司状态", "required": False},
                {"name": "company_type", "type": "string", "description": "公司类型(parent/subsidiary)", "required": False}
            ],
            outputs=[
                {"name": "companies", "type": "array", "description": "公司信息列表"},
                {"name": "count", "type": "number", "description": "查询到的公司数量"}
            ],
            category="company"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询公司信息"""
        try:
            self.validate_inputs(inputs)
            
            company_id = inputs.get("company_id")
            company_name = inputs.get("company_name")
            industry = inputs.get("industry")
            company_size = inputs.get("company_size")
            status = inputs.get("status")
            company_type = inputs.get("company_type")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if company_id:
                conditions.append("id = ?")
                params.append(company_id)
            
            if company_name:
                conditions.append("name LIKE ?")
                params.append(f"%{company_name}%")
            
            if industry:
                conditions.append("industry = ?")
                params.append(industry)
            
            if company_size:
                conditions.append("company_size = ?")
                params.append(company_size)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            if company_type:
                conditions.append("company_type = ?")
                params.append(company_type)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT id, name, business_scope, industry, company_size, tax_id,
                       accounting_standards, parent_company_id, company_type,
                       established_date, registered_capital, status, created_at, updated_at
                FROM companies
                {where_clause}
                ORDER BY name
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            companies = []
            for row in rows:
                companies.append({
                    "id": row[0],
                    "name": row[1],
                    "business_scope": row[2],
                    "industry": row[3],
                    "company_size": row[4],
                    "tax_id": row[5],
                    "accounting_standards": row[6],
                    "parent_company_id": row[7],
                    "company_type": row[8],
                    "established_date": row[9],
                    "registered_capital": float(row[10]) if row[10] else None,
                    "status": row[11],
                    "created_at": row[12],
                    "updated_at": row[13]
                })
            
            return {
                "success": True,
                "data": {
                    "companies": companies,
                    "count": len(companies)
                }
            }
            
        except Exception as e:
            logger.error(f"查询公司信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class CompanyStatisticsTool(BuiltinTool):
    """公司统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="company_statistics",
            name="公司统计分析",
            description="对公司信息进行统计分析",
            inputs=[
                {"name": "group_by", "type": "string", "description": "分组字段(industry/company_size/status/company_type)", "required": False},
                {"name": "industry", "type": "string", "description": "筛选行业", "required": False},
                {"name": "company_size", "type": "string", "description": "筛选公司规模", "required": False},
                {"name": "status", "type": "string", "description": "筛选公司状态", "required": False}
            ],
            outputs=[
                {"name": "total_count", "type": "number", "description": "公司总数"},
                {"name": "group_statistics", "type": "object", "description": "分组统计结果"},
                {"name": "capital_statistics", "type": "object", "description": "注册资本统计"}
            ],
            category="company"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行公司统计分析"""
        try:
            self.validate_inputs(inputs)
            
            group_by = inputs.get("group_by", "industry")
            industry = inputs.get("industry")
            company_size = inputs.get("company_size")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if industry:
                conditions.append("industry = ?")
                params.append(industry)
            
            if company_size:
                conditions.append("company_size = ?")
                params.append(company_size)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 总数统计
            total_query = f"SELECT COUNT(*) FROM companies {where_clause}"
            cursor.execute(total_query, params)
            total_count = cursor.fetchone()[0]
            
            # 分组统计
            valid_group_fields = ["industry", "company_size", "status", "company_type"]
            if group_by not in valid_group_fields:
                group_by = "industry"
            
            group_query = f"""
                SELECT {group_by}, COUNT(*) as count
                FROM companies
                {where_clause}
                GROUP BY {group_by}
                ORDER BY count DESC
            """
            cursor.execute(group_query, params)
            group_rows = cursor.fetchall()
            
            group_statistics = {}
            for row in group_rows:
                group_statistics[row[0] or "未分类"] = row[1]
            
            # 注册资本统计
            capital_query = f"""
                SELECT
                    SUM(registered_capital) as total_capital,
                    AVG(registered_capital) as avg_capital,
                    MIN(registered_capital) as min_capital,
                    MAX(registered_capital) as max_capital
                FROM companies
                {where_clause}
                WHERE registered_capital IS NOT NULL
            """
            cursor.execute(capital_query, params)
            capital_row = cursor.fetchone()
            
            capital_statistics = {
                "total_capital": float(capital_row[0]) if capital_row[0] else 0,
                "avg_capital": float(capital_row[1]) if capital_row[1] else 0,
                "min_capital": float(capital_row[2]) if capital_row[2] else 0,
                "max_capital": float(capital_row[3]) if capital_row[3] else 0
            }
            
            return {
                "success": True,
                "data": {
                    "total_count": total_count,
                    "group_statistics": group_statistics,
                    "capital_statistics": capital_statistics
                }
            }
            
        except Exception as e:
            logger.error(f"公司统计分析失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class QuerySubjectAccountsTool(BuiltinTool):
    """查询会计科目工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_subject_accounts",
            name="查询会计科目",
            description="根据条件查询会计科目信息",
            inputs=[
                {"name": "account_code", "type": "string", "description": "科目编码", "required": False},
                {"name": "account_name", "type": "string", "description": "科目名称", "required": False},
                {"name": "category", "type": "string", "description": "科目类别", "required": False},
                {"name": "level", "type": "number", "description": "科目级次", "required": False},
                {"name": "parent_code", "type": "string", "description": "父级科目编码", "required": False},
                {"name": "is_leaf", "type": "boolean", "description": "是否末级科目", "required": False},
                {"name": "status", "type": "string", "description": "科目状态", "required": False}
            ],
            outputs=[
                {"name": "accounts", "type": "array", "description": "会计科目列表"},
                {"name": "count", "type": "number", "description": "查询到的科目数量"}
            ],
            category="accounting"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询会计科目"""
        try:
            self.validate_inputs(inputs)
            
            account_code = inputs.get("account_code")
            account_name = inputs.get("account_name")
            category = inputs.get("category")
            level = inputs.get("level")
            parent_code = inputs.get("parent_code")
            is_leaf = inputs.get("is_leaf")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if account_code:
                conditions.append("code = ?")
                params.append(account_code)
            
            if account_name:
                conditions.append("name LIKE ?")
                params.append(f"%{account_name}%")
            
            if category:
                conditions.append("category = ?")
                params.append(category)
            
            if level is not None:
                conditions.append("level = ?")
                params.append(level)
            
            if parent_code:
                conditions.append("parent_code = ?")
                params.append(parent_code)
            
            if is_leaf is not None:
                conditions.append("is_leaf = ?")
                params.append(1 if is_leaf else 0)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT code, name, level, parent_code, category, direction,
                       aux, is_leaf, status, remark, quantity
                FROM subject_accounts
                {where_clause}
                ORDER BY code
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            accounts = []
            for row in rows:
                accounts.append({
                    "code": row[0],
                    "name": row[1],
                    "level": row[2],
                    "parent_code": row[3],
                    "category": row[4],
                    "direction": row[5],
                    "aux": row[6],
                    "is_leaf": bool(row[7]),
                    "status": row[8],
                    "remark": row[9],
                    "quantity": bool(row[10])
                })
            
            return {
                "success": True,
                "data": {
                    "accounts": accounts,
                    "count": len(accounts)
                }
            }
            
        except Exception as e:
            logger.error(f"查询会计科目失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class SubjectAccountStatisticsTool(BuiltinTool):
    """会计科目统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="subject_account_statistics",
            name="会计科目统计分析",
            description="对会计科目进行统计分析",
            inputs=[
                {"name": "group_by", "type": "string", "description": "分组字段(category/level/direction/status)", "required": False},
                {"name": "category", "type": "string", "description": "筛选科目类别", "required": False},
                {"name": "level", "type": "number", "description": "筛选科目级次", "required": False},
                {"name": "parent_code", "type": "string", "description": "筛选父级科目", "required": False}
            ],
            outputs=[
                {"name": "total_count", "type": "number", "description": "科目总数"},
                {"name": "group_statistics", "type": "object", "description": "分组统计结果"},
                {"name": "level_distribution", "type": "object", "description": "级次分布"},
                {"name": "leaf_ratio", "type": "object", "description": "末级科目比例"}
            ],
            category="accounting"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行会计科目统计分析"""
        try:
            self.validate_inputs(inputs)
            
            group_by = inputs.get("group_by", "category")
            category = inputs.get("category")
            level = inputs.get("level")
            parent_code = inputs.get("parent_code")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if category:
                conditions.append("category = ?")
                params.append(category)
            
            if level is not None:
                conditions.append("level = ?")
                params.append(level)
            
            if parent_code:
                conditions.append("parent_code = ?")
                params.append(parent_code)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 总数统计
            total_query = f"SELECT COUNT(*) FROM subject_accounts {where_clause}"
            cursor.execute(total_query, params)
            total_count = cursor.fetchone()[0]
            
            # 分组统计
            valid_group_fields = ["category", "level", "direction", "status"]
            if group_by not in valid_group_fields:
                group_by = "category"
            
            group_query = f"""
                SELECT {group_by}, COUNT(*) as count
                FROM subject_accounts
                {where_clause}
                GROUP BY {group_by}
                ORDER BY count DESC
            """
            cursor.execute(group_query, params)
            group_rows = cursor.fetchall()
            
            group_statistics = {}
            for row in group_rows:
                key = row[0] if row[0] is not None else "未分类"
                group_statistics[key] = row[1]
            
            # 级次分布
            level_query = f"""
                SELECT level, COUNT(*) as count
                FROM subject_accounts
                {where_clause}
                GROUP BY level
                ORDER BY level
            """
            cursor.execute(level_query, params)
            level_rows = cursor.fetchall()
            
            level_distribution = {}
            for row in level_rows:
                level_distribution[f"级次{row[0]}"] = row[1]
            
            # 末级科目比例
            leaf_query = f"""
                SELECT is_leaf, COUNT(*) as count
                FROM subject_accounts
                {where_clause}
                GROUP BY is_leaf
            """
            cursor.execute(leaf_query, params)
            leaf_rows = cursor.fetchall()
            
            leaf_count = 0
            non_leaf_count = 0
            for row in leaf_rows:
                if row[0] == 1:
                    leaf_count = row[1]
                else:
                    non_leaf_count = row[1]
            
            leaf_ratio = {
                "leaf_count": leaf_count,
                "non_leaf_count": non_leaf_count,
                "leaf_percentage": round(leaf_count / total_count * 100, 2) if total_count > 0 else 0,
                "non_leaf_percentage": round(non_leaf_count / total_count * 100, 2) if total_count > 0 else 0
            }
            
            return {
                "success": True,
                "data": {
                    "total_count": total_count,
                    "group_statistics": group_statistics,
                    "level_distribution": level_distribution,
                    "leaf_ratio": leaf_ratio
                }
            }
            
        except Exception as e:
            logger.error(f"会计科目统计分析失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class QueryVoucherEntriesTool(BuiltinTool):
    """查询凭证分录工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_voucher_entries",
            name="查询凭证分录",
            description="根据条件查询凭证分录信息",
            inputs=[
                {"name": "voucher_id", "type": "number", "description": "凭证ID", "required": False},
                {"name": "voucher_no", "type": "string", "description": "凭证号", "required": False},
                {"name": "account_code", "type": "string", "description": "科目编码", "required": False},
                {"name": "account_name", "type": "string", "description": "科目名称", "required": False},
                {"name": "date_from", "type": "string", "description": "开始日期(YYYY-MM-DD)", "required": False},
                {"name": "date_to", "type": "string", "description": "结束日期(YYYY-MM-DD)", "required": False},
                {"name": "summary", "type": "string", "description": "摘要关键词", "required": False},
                {"name": "min_amount", "type": "number", "description": "最小金额", "required": False},
                {"name": "max_amount", "type": "number", "description": "最大金额", "required": False}
            ],
            outputs=[
                {"name": "voucher_entries", "type": "array", "description": "凭证分录列表"},
                {"name": "total_count", "type": "number", "description": "分录总数"},
                {"name": "total_debit", "type": "number", "description": "借方合计"},
                {"name": "total_credit", "type": "number", "description": "贷方合计"}
            ],
            category="finance"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询凭证分录"""
        try:
            self.validate_inputs(inputs)
            
            voucher_id = inputs.get("voucher_id")
            voucher_no = inputs.get("voucher_no")
            account_code = inputs.get("account_code")
            account_name = inputs.get("account_name")
            date_from = inputs.get("date_from")
            date_to = inputs.get("date_to")
            summary = inputs.get("summary")
            min_amount = inputs.get("min_amount")
            max_amount = inputs.get("max_amount")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if voucher_id:
                conditions.append("ve.voucher_id = ?")
                params.append(voucher_id)
            
            if voucher_no:
                conditions.append("v.voucher_no LIKE ?")
                params.append(f"%{voucher_no}%")
            
            if account_code:
                conditions.append("ve.account_code = ?")
                params.append(account_code)
            
            if account_name:
                conditions.append("ve.account_name LIKE ?")
                params.append(f"%{account_name}%")
            
            if date_from:
                conditions.append("v.date >= ?")
                params.append(date_from)
            
            if date_to:
                conditions.append("v.date <= ?")
                params.append(date_to)
            
            if summary:
                conditions.append("ve.summary LIKE ?")
                params.append(f"%{summary}%")
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 金额条件
            amount_conditions = []
            if min_amount is not None:
                amount_conditions.append("(ve.debit >= ? OR ve.credit >= ?)")
                params.extend([min_amount, min_amount])
            
            if max_amount is not None:
                amount_conditions.append("(ve.debit <= ? OR ve.credit <= ?)")
                params.extend([max_amount, max_amount])
            
            having_clause = ""
            if amount_conditions:
                having_clause = "HAVING " + " AND ".join(amount_conditions)
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT ve.id, ve.voucher_id, v.voucher_no, v.date, ve.summary,
                       ve.account_code, ve.account_name, ve.debit, ve.credit
                FROM voucher_entries ve
                JOIN vouchers v ON ve.voucher_id = v.id
                {where_clause}
                {having_clause}
                ORDER BY v.date DESC, v.voucher_no DESC, ve.id
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            voucher_entries = []
            total_debit = 0
            total_credit = 0
            
            for row in rows:
                entry = {
                    "id": row[0],
                    "voucher_id": row[1],
                    "voucher_no": row[2],
                    "date": row[3],
                    "summary": row[4],
                    "account_code": row[5],
                    "account_name": row[6],
                    "debit": float(row[7]) if row[7] else 0,
                    "credit": float(row[8]) if row[8] else 0
                }
                voucher_entries.append(entry)
                total_debit += entry["debit"]
                total_credit += entry["credit"]
            
            return {
                "success": True,
                "data": {
                    "voucher_entries": voucher_entries,
                    "total_count": len(voucher_entries),
                    "total_debit": total_debit,
                    "total_credit": total_credit
                }
            }
            
        except Exception as e:
            logger.error(f"查询凭证分录失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class VoucherStatisticsTool(BuiltinTool):
    """凭证统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="voucher_statistics",
            name="凭证统计分析",
            description="对凭证进行统计分析",
            inputs=[
                {"name": "date_from", "type": "string", "description": "开始日期(YYYY-MM-DD)", "required": False},
                {"name": "date_to", "type": "string", "description": "结束日期(YYYY-MM-DD)", "required": False},
                {"name": "group_by", "type": "string", "description": "分组字段(month/account_category)", "required": False},
                {"name": "account_category", "type": "string", "description": "筛选科目类别", "required": False}
            ],
            outputs=[
                {"name": "voucher_count", "type": "number", "description": "凭证总数"},
                {"name": "entry_count", "type": "number", "description": "分录总数"},
                {"name": "total_debit", "type": "number", "description": "借方合计"},
                {"name": "total_credit", "type": "number", "description": "贷方合计"},
                {"name": "group_statistics", "type": "object", "description": "分组统计结果"},
                {"name": "top_accounts", "type": "array", "description": "最常用科目"}
            ],
            category="finance"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行凭证统计分析"""
        try:
            self.validate_inputs(inputs)
            
            date_from = inputs.get("date_from")
            date_to = inputs.get("date_to")
            group_by = inputs.get("group_by", "month")
            account_category = inputs.get("account_category")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if date_from:
                conditions.append("v.date >= ?")
                params.append(date_from)
            
            if date_to:
                conditions.append("v.date <= ?")
                params.append(date_to)
            
            if account_category:
                conditions.append("sa.category = ?")
                params.append(account_category)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 基本统计
            basic_query = f"""
                SELECT
                    COUNT(DISTINCT v.id) as voucher_count,
                    COUNT(ve.id) as entry_count,
                    SUM(ve.debit) as total_debit,
                    SUM(ve.credit) as total_credit
                FROM vouchers v
                JOIN voucher_entries ve ON v.id = ve.voucher_id
                LEFT JOIN subject_accounts sa ON ve.account_code = sa.code
                {where_clause}
            """
            cursor.execute(basic_query, params)
            basic_row = cursor.fetchone()
            
            voucher_count = basic_row[0] if basic_row[0] else 0
            entry_count = basic_row[1] if basic_row[1] else 0
            total_debit = float(basic_row[2]) if basic_row[2] else 0
            total_credit = float(basic_row[3]) if basic_row[3] else 0
            
            # 分组统计
            group_statistics = {}
            if group_by == "month":
                month_query = f"""
                    SELECT strftime('%Y-%m', v.date) as month,
                           COUNT(DISTINCT v.id) as voucher_count,
                           SUM(ve.debit) as debit_amount,
                           SUM(ve.credit) as credit_amount
                    FROM vouchers v
                    JOIN voucher_entries ve ON v.id = ve.voucher_id
                    {where_clause}
                    GROUP BY strftime('%Y-%m', v.date)
                    ORDER BY month
                """
                cursor.execute(month_query, params)
                month_rows = cursor.fetchall()
                
                for row in month_rows:
                    group_statistics[row[0]] = {
                        "voucher_count": row[1],
                        "debit_amount": float(row[2]) if row[2] else 0,
                        "credit_amount": float(row[3]) if row[3] else 0
                    }
            
            elif group_by == "account_category":
                category_query = f"""
                    SELECT sa.category,
                           COUNT(ve.id) as entry_count,
                           SUM(ve.debit) as debit_amount,
                           SUM(ve.credit) as credit_amount
                    FROM voucher_entries ve
                    JOIN vouchers v ON ve.voucher_id = v.id
                    LEFT JOIN subject_accounts sa ON ve.account_code = sa.code
                    {where_clause}
                    GROUP BY sa.category
                    ORDER BY entry_count DESC
                """
                cursor.execute(category_query, params)
                category_rows = cursor.fetchall()
                
                for row in category_rows:
                    category = row[0] or "未分类"
                    group_statistics[category] = {
                        "entry_count": row[1],
                        "debit_amount": float(row[2]) if row[2] else 0,
                        "credit_amount": float(row[3]) if row[3] else 0
                    }
            
            # 最常用科目
            top_accounts_query = f"""
                SELECT ve.account_code, ve.account_name, sa.category,
                       COUNT(ve.id) as usage_count,
                       SUM(ve.debit) as total_debit,
                       SUM(ve.credit) as total_credit
                FROM voucher_entries ve
                JOIN vouchers v ON ve.voucher_id = v.id
                LEFT JOIN subject_accounts sa ON ve.account_code = sa.code
                {where_clause}
                GROUP BY ve.account_code, ve.account_name, sa.category
                ORDER BY usage_count DESC
                LIMIT 10
            """
            cursor.execute(top_accounts_query, params)
            top_account_rows = cursor.fetchall()
            
            top_accounts = []
            for row in top_account_rows:
                top_accounts.append({
                    "account_code": row[0],
                    "account_name": row[1],
                    "category": row[2] or "未分类",
                    "usage_count": row[3],
                    "total_debit": float(row[4]) if row[4] else 0,
                    "total_credit": float(row[5]) if row[5] else 0
                })
            
            return {
                "success": True,
                "data": {
                    "voucher_count": voucher_count,
                    "entry_count": entry_count,
                    "total_debit": total_debit,
                    "total_credit": total_credit,
                    "group_statistics": group_statistics,
                    "top_accounts": top_accounts
                }
            }
            
        except Exception as e:
            logger.error(f"凭证统计分析失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class QueryAssetsTool(BuiltinTool):
    """查询资产信息工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_assets",
            name="查询资产信息",
            description="根据条件查询资产信息",
            inputs=[
                {"name": "asset_id", "type": "number", "description": "资产ID", "required": False},
                {"name": "asset_name", "type": "string", "description": "资产名称", "required": False},
                {"name": "asset_type", "type": "string", "description": "资产类别", "required": False},
                {"name": "status", "type": "string", "description": "资产状态", "required": False},
                {"name": "min_value", "type": "number", "description": "最小价值", "required": False},
                {"name": "max_value", "type": "number", "description": "最大价值", "required": False}
            ],
            outputs=[
                {"name": "assets", "type": "array", "description": "资产信息列表"},
                {"name": "count", "type": "number", "description": "查询到的资产数量"},
                {"name": "total_value", "type": "number", "description": "资产总价值"}
            ],
            category="asset"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询资产信息"""
        try:
            self.validate_inputs(inputs)
            
            asset_id = inputs.get("asset_id")
            asset_name = inputs.get("asset_name")
            asset_type = inputs.get("asset_type")
            status = inputs.get("status")
            min_value = inputs.get("min_value")
            max_value = inputs.get("max_value")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if asset_id:
                conditions.append("id = ?")
                params.append(asset_id)
            
            if asset_name:
                conditions.append("name LIKE ?")
                params.append(f"%{asset_name}%")
            
            if asset_type:
                conditions.append("type = ?")
                params.append(asset_type)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            if min_value is not None:
                conditions.append("value >= ?")
                params.append(min_value)
            
            if max_value is not None:
                conditions.append("value <= ?")
                params.append(max_value)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT id, name, type, value, status, remark
                FROM assets
                {where_clause}
                ORDER BY name
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            assets = []
            total_value = 0
            
            for row in rows:
                asset = {
                    "id": row[0],
                    "name": row[1],
                    "type": row[2],
                    "value": float(row[3]) if row[3] else 0,
                    "status": row[4],
                    "remark": row[5]
                }
                assets.append(asset)
                total_value += asset["value"]
            
            return {
                "success": True,
                "data": {
                    "assets": assets,
                    "count": len(assets),
                    "total_value": total_value
                }
            }
            
        except Exception as e:
            logger.error(f"查询资产信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class AssetStatisticsTool(BuiltinTool):
    """资产统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="asset_statistics",
            name="资产统计分析",
            description="对资产进行统计分析",
            inputs=[
                {"name": "group_by", "type": "string", "description": "分组字段(type/status)", "required": False},
                {"name": "asset_type", "type": "string", "description": "筛选资产类别", "required": False},
                {"name": "status", "type": "string", "description": "筛选资产状态", "required": False}
            ],
            outputs=[
                {"name": "total_count", "type": "number", "description": "资产总数"},
                {"name": "total_value", "type": "number", "description": "资产总价值"},
                {"name": "average_value", "type": "number", "description": "平均价值"},
                {"name": "group_statistics", "type": "object", "description": "分组统计结果"},
                {"name": "value_distribution", "type": "object", "description": "价值分布"}
            ],
            category="asset"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行资产统计分析"""
        try:
            self.validate_inputs(inputs)
            
            group_by = inputs.get("group_by", "type")
            asset_type = inputs.get("asset_type")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if asset_type:
                conditions.append("type = ?")
                params.append(asset_type)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 基本统计
            basic_query = f"""
                SELECT
                    COUNT(*) as total_count,
                    SUM(value) as total_value,
                    AVG(value) as average_value
                FROM assets
                {where_clause}
            """
            cursor.execute(basic_query, params)
            basic_row = cursor.fetchone()
            
            total_count = basic_row[0] if basic_row[0] else 0
            total_value = float(basic_row[1]) if basic_row[1] else 0
            average_value = float(basic_row[2]) if basic_row[2] else 0
            
            # 分组统计
            valid_group_fields = ["type", "status"]
            if group_by not in valid_group_fields:
                group_by = "type"
            
            group_query = f"""
                SELECT {group_by},
                       COUNT(*) as count,
                       SUM(value) as total_value,
                       AVG(value) as avg_value
                FROM assets
                {where_clause}
                GROUP BY {group_by}
                ORDER BY total_value DESC
            """
            cursor.execute(group_query, params)
            group_rows = cursor.fetchall()
            
            group_statistics = {}
            for row in group_rows:
                key = row[0] or "未分类"
                group_statistics[key] = {
                    "count": row[1],
                    "total_value": float(row[2]) if row[2] else 0,
                    "avg_value": float(row[3]) if row[3] else 0
                }
            
            # 价值分布
            value_ranges = [
                ("0-1000", 0, 1000),
                ("1000-10000", 1000, 10000),
                ("10000-100000", 10000, 100000),
                ("100000+", 100000, None)
            ]
            
            value_distribution = {}
            for range_name, min_val, max_val in value_ranges:
                if max_val is None:
                    range_conditions = conditions + ["value >= ?"]
                    range_params = params + [min_val]
                else:
                    range_conditions = conditions + ["value >= ? AND value < ?"]
                    range_params = params + [min_val, max_val]
                
                range_where = "WHERE " + " AND ".join(range_conditions) if range_conditions else ""
                range_query = f"SELECT COUNT(*) FROM assets {range_where}"
                cursor.execute(range_query, range_params)
                count = cursor.fetchone()[0]
                
                value_distribution[range_name] = count
            
            return {
                "success": True,
                "data": {
                    "total_count": total_count,
                    "total_value": total_value,
                    "average_value": average_value,
                    "group_statistics": group_statistics,
                    "value_distribution": value_distribution
                }
            }
            
        except Exception as e:
            logger.error(f"资产统计分析失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class QueryStaffsTool(BuiltinTool):
    """查询员工信息工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_staffs",
            name="查询员工信息",
            description="根据条件查询员工详细信息",
            inputs=[
                {"name": "staff_id", "type": "number", "description": "员工ID", "required": False},
                {"name": "job_no", "type": "string", "description": "工号", "required": False},
                {"name": "name", "type": "string", "description": "员工姓名", "required": False},
                {"name": "role_code", "type": "string", "description": "角色代码", "required": False},
                {"name": "level", "type": "string", "description": "员工级别", "required": False},
                {"name": "phone", "type": "string", "description": "联系电话", "required": False},
                {"name": "status", "type": "string", "description": "员工状态", "required": False}
            ],
            outputs=[
                {"name": "staffs", "type": "array", "description": "员工信息列表"},
                {"name": "count", "type": "number", "description": "查询到的员工数量"}
            ],
            category="employee"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询员工信息"""
        try:
            self.validate_inputs(inputs)
            
            staff_id = inputs.get("staff_id")
            job_no = inputs.get("job_no")
            name = inputs.get("name")
            role_code = inputs.get("role_code")
            level = inputs.get("level")
            phone = inputs.get("phone")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if staff_id:
                conditions.append("s.id = ?")
                params.append(staff_id)
            
            if job_no:
                conditions.append("s.job_no = ?")
                params.append(job_no)
            
            if name:
                conditions.append("s.name LIKE ?")
                params.append(f"%{name}%")
            
            if role_code:
                conditions.append("s.role_code = ?")
                params.append(role_code)
            
            if level:
                conditions.append("s.level = ?")
                params.append(level)
            
            if phone:
                conditions.append("s.phone = ?")
                params.append(phone)
            
            if status:
                conditions.append("s.status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT s.id, s.job_no, s.name, s.role_code, r.name as role_name,
                       s.level, s.phone, s.status, s.remark
                FROM staffs s
                LEFT JOIN roles r ON s.role_code = r.code
                {where_clause}
                ORDER BY s.name
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            staffs = []
            for row in rows:
                staffs.append({
                    "id": row[0],
                    "job_no": row[1],
                    "name": row[2],
                    "role_code": row[3],
                    "role_name": row[4],
                    "level": row[5],
                    "phone": row[6],
                    "status": row[7],
                    "remark": row[8]
                })
            
            return {
                "success": True,
                "data": {
                    "staffs": staffs,
                    "count": len(staffs)
                }
            }
            
        except Exception as e:
            logger.error(f"查询员工信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class QueryRolesTool(BuiltinTool):
    """查询角色信息工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_roles",
            name="查询角色信息",
            description="根据条件查询角色信息",
            inputs=[
                {"name": "role_code", "type": "string", "description": "角色代码", "required": False},
                {"name": "role_name", "type": "string", "description": "角色名称", "required": False},
                {"name": "include_staff_count", "type": "boolean", "description": "是否包含员工数量", "required": False}
            ],
            outputs=[
                {"name": "roles", "type": "array", "description": "角色信息列表"},
                {"name": "count", "type": "number", "description": "查询到的角色数量"}
            ],
            category="employee"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询角色信息"""
        try:
            self.validate_inputs(inputs)
            
            role_code = inputs.get("role_code")
            role_name = inputs.get("role_name")
            include_staff_count = inputs.get("include_staff_count", False)
            
            # 构建查询条件
            conditions = []
            params = []
            
            if role_code:
                conditions.append("r.code = ?")
                params.append(role_code)
            
            if role_name:
                conditions.append("r.name LIKE ?")
                params.append(f"%{role_name}%")
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            if include_staff_count:
                query = f"""
                    SELECT r.code, r.name, r.description, COUNT(s.id) as staff_count
                    FROM roles r
                    LEFT JOIN staffs s ON r.code = s.role_code
                    {where_clause}
                    GROUP BY r.code, r.name, r.description
                    ORDER BY r.name
                """
            else:
                query = f"""
                    SELECT r.code, r.name, r.description
                    FROM roles r
                    {where_clause}
                    ORDER BY r.name
                """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            roles = []
            for row in rows:
                if include_staff_count:
                    roles.append({
                        "code": row[0],
                        "name": row[1],
                        "description": row[2],
                        "staff_count": row[3]
                    })
                else:
                    roles.append({
                        "code": row[0],
                        "name": row[1],
                        "description": row[2]
                    })
            
            return {
                "success": True,
                "data": {
                    "roles": roles,
                    "count": len(roles)
                }
            }
            
        except Exception as e:
            logger.error(f"查询角色信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class StaffRoleStatisticsTool(BuiltinTool):
    """员工角色统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="staff_role_statistics",
            name="员工角色统计分析",
            description="对员工和角色进行统计分析",
            inputs=[
                {"name": "group_by", "type": "string", "description": "分组字段(role/level/status)", "required": False},
                {"name": "role_code", "type": "string", "description": "筛选角色代码", "required": False},
                {"name": "level", "type": "string", "description": "筛选员工级别", "required": False},
                {"name": "status", "type": "string", "description": "筛选员工状态", "required": False}
            ],
            outputs=[
                {"name": "total_staffs", "type": "number", "description": "员工总数"},
                {"name": "total_roles", "type": "number", "description": "角色总数"},
                {"name": "group_statistics", "type": "object", "description": "分组统计结果"},
                {"name": "role_distribution", "type": "object", "description": "角色分布"},
                {"name": "level_distribution", "type": "object", "description": "级别分布"}
            ],
            category="employee"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行员工角色统计分析"""
        try:
            self.validate_inputs(inputs)
            
            group_by = inputs.get("group_by", "role")
            role_code = inputs.get("role_code")
            level = inputs.get("level")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if role_code:
                conditions.append("s.role_code = ?")
                params.append(role_code)
            
            if level:
                conditions.append("s.level = ?")
                params.append(level)
            
            if status:
                conditions.append("s.status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 基本统计
            staff_query = f"SELECT COUNT(*) FROM staffs s {where_clause}"
            cursor.execute(staff_query, params)
            total_staffs = cursor.fetchone()[0]
            
            role_query = "SELECT COUNT(*) FROM roles"
            cursor.execute(role_query)
            total_roles = cursor.fetchone()[0]
            
            # 分组统计
            valid_group_fields = ["role", "level", "status"]
            if group_by not in valid_group_fields:
                group_by = "role"
            
            if group_by == "role":
                group_query = f"""
                    SELECT r.name, COUNT(s.id) as count
                    FROM staffs s
                    LEFT JOIN roles r ON s.role_code = r.code
                    {where_clause}
                    GROUP BY r.name
                    ORDER BY count DESC
                """
            elif group_by == "level":
                group_query = f"""
                    SELECT s.level, COUNT(s.id) as count
                    FROM staffs s
                    {where_clause}
                    GROUP BY s.level
                    ORDER BY count DESC
                """
            else:  # status
                group_query = f"""
                    SELECT s.status, COUNT(s.id) as count
                    FROM staffs s
                    {where_clause}
                    GROUP BY s.status
                    ORDER BY count DESC
                """
            
            cursor.execute(group_query, params)
            group_rows = cursor.fetchall()
            
            group_statistics = {}
            for row in group_rows:
                key = row[0] or "未分类"
                group_statistics[key] = row[1]
            
            # 角色分布
            role_dist_query = f"""
                SELECT r.name, COUNT(s.id) as count
                FROM roles r
                LEFT JOIN staffs s ON r.code = s.role_code
                GROUP BY r.name
                ORDER BY count DESC
            """
            cursor.execute(role_dist_query)
            role_dist_rows = cursor.fetchall()
            
            role_distribution = {}
            for row in role_dist_rows:
                role_distribution[row[0] or "未分类"] = row[1]
            
            # 级别分布
            level_dist_query = f"""
                SELECT s.level, COUNT(s.id) as count
                FROM staffs s
                GROUP BY s.level
                ORDER BY count DESC
            """
            cursor.execute(level_dist_query)
            level_dist_rows = cursor.fetchall()
            
            level_distribution = {}
            for row in level_dist_rows:
                level_distribution[row[0] or "未分类"] = row[1]
            
            return {
                "success": True,
                "data": {
                    "total_staffs": total_staffs,
                    "total_roles": total_roles,
                    "group_statistics": group_statistics,
                    "role_distribution": role_distribution,
                    "level_distribution": level_distribution
                }
            }
            
        except Exception as e:
            logger.error(f"员工角色统计分析失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class CrossTableQueryTool(BuiltinTool):
    """跨表关联查询工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="cross_table_query",
            name="跨表关联查询",
            description="跨多个表进行关联查询",
            inputs=[
                {"name": "main_table", "type": "string", "description": "主表(companies/vouchers/assets/staffs)", "required": True},
                {"name": "join_tables", "type": "array", "description": "要关联的表列表", "required": False},
                {"name": "conditions", "type": "array", "description": "查询条件列表", "required": False},
                {"name": "fields", "type": "array", "description": "要返回的字段列表", "required": False},
                {"name": "limit", "type": "number", "description": "返回记录数限制", "required": False}
            ],
            outputs=[
                {"name": "results", "type": "array", "description": "查询结果"},
                {"name": "count", "type": "number", "description": "记录数量"},
                {"name": "query_info", "type": "object", "description": "查询信息"}
            ],
            category="cross_table"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行跨表关联查询"""
        try:
            self.validate_inputs(inputs)
            
            main_table = inputs.get("main_table")
            join_tables = inputs.get("join_tables", [])
            conditions = inputs.get("conditions", [])
            fields = inputs.get("fields", [])
            limit = inputs.get("limit", 100)
            
            # 验证主表
            valid_tables = ["companies", "vouchers", "assets", "staffs", "subject_accounts", "voucher_entries"]
            if main_table not in valid_tables:
                return {
                    "success": False,
                    "error": f"无效的主表: {main_table}"
                }
            
            # 构建查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 构建SELECT部分
            if fields:
                select_fields = ", ".join(fields)
            else:
                select_fields = f"{main_table}.*"
            
            # 构建FROM和JOIN部分
            from_clause = main_table
            join_clauses = []
            
            # 预定义的关联关系
            join_relations = {
                ("vouchers", "voucher_entries"): "vouchers.id = voucher_entries.voucher_id",
                ("voucher_entries", "vouchers"): "voucher_entries.voucher_id = vouchers.id",
                ("voucher_entries", "subject_accounts"): "voucher_entries.account_code = subject_accounts.code",
                ("subject_accounts", "voucher_entries"): "subject_accounts.code = voucher_entries.account_code",
                ("staffs", "roles"): "staffs.role_code = roles.code",
                ("roles", "staffs"): "roles.code = staffs.role_code"
            }
            
            for join_table in join_tables:
                if join_table in valid_tables:
                    relation_key = (main_table, join_table)
                    reverse_key = (join_table, main_table)
                    
                    if relation_key in join_relations:
                        join_clauses.append(f"LEFT JOIN {join_table} ON {join_relations[relation_key]}")
                    elif reverse_key in join_relations:
                        join_clauses.append(f"LEFT JOIN {join_table} ON {join_relations[reverse_key]}")
                    else:
                        # 如果没有预定义关系，简单关联
                        join_clauses.append(f"LEFT JOIN {join_table} ON 1=1")
            
            # 构建WHERE部分
            where_clauses = []
            params = []
            
            for condition in conditions:
                if isinstance(condition, dict) and "field" in condition and "operator" in condition and "value" in condition:
                    field = condition["field"]
                    operator = condition["operator"]
                    value = condition["value"]
                    
                    if operator in ["=", ">", "<", ">=", "<=", "!="]:
                        where_clauses.append(f"{field} {operator} ?")
                        params.append(value)
                    elif operator.upper() == "LIKE":
                        where_clauses.append(f"{field} LIKE ?")
                        params.append(f"%{value}%")
                    elif operator.upper() == "IN":
                        if isinstance(value, list):
                            placeholders = ", ".join(["?"] * len(value))
                            where_clauses.append(f"{field} IN ({placeholders})")
                            params.extend(value)
            
            where_clause = "WHERE " + " AND ".join(where_clauses) if where_clauses else ""
            
            # 构建完整查询
            query = f"""
                SELECT {select_fields}
                FROM {from_clause}
                {" ".join(join_clauses)}
                {where_clause}
                LIMIT {limit}
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 获取列名
            column_names = [description[0] for description in cursor.description]
            
            # 转换结果
            results = []
            for row in rows:
                result = {}
                for i, value in enumerate(row):
                    result[column_names[i]] = value
                results.append(result)
            
            query_info = {
                "main_table": main_table,
                "join_tables": join_tables,
                "conditions_count": len(conditions),
                "fields_count": len(fields) if fields else len(column_names)
            }
            
            return {
                "success": True,
                "data": {
                    "results": results,
                    "count": len(results),
                    "query_info": query_info
                }
            }
            
        except Exception as e:
            logger.error(f"跨表关联查询失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class BusinessSummaryTool(BuiltinTool):
    """业务汇总统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="business_summary",
            name="业务汇总统计",
            description="对整个系统的业务数据进行汇总统计",
            inputs=[
                {"name": "date_from", "type": "string", "description": "开始日期(YYYY-MM-DD)", "required": False},
                {"name": "date_to", "type": "string", "description": "结束日期(YYYY-MM-DD)", "required": False},
                {"name": "include_companies", "type": "boolean", "description": "是否包含公司统计", "required": False},
                {"name": "include_vouchers", "type": "boolean", "description": "是否包含凭证统计", "required": False},
                {"name": "include_assets", "type": "boolean", "description": "是否包含资产统计", "required": False},
                {"name": "include_staffs", "type": "boolean", "description": "是否包含员工统计", "required": False}
            ],
            outputs=[
                {"name": "summary", "type": "object", "description": "业务汇总数据"},
                {"name": "company_stats", "type": "object", "description": "公司统计"},
                {"name": "voucher_stats", "type": "object", "description": "凭证统计"},
                {"name": "asset_stats", "type": "object", "description": "资产统计"},
                {"name": "staff_stats", "type": "object", "description": "员工统计"}
            ],
            category="cross_table"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行业务汇总统计"""
        try:
            self.validate_inputs(inputs)
            
            date_from = inputs.get("date_from")
            date_to = inputs.get("date_to")
            include_companies = inputs.get("include_companies", True)
            include_vouchers = inputs.get("include_vouchers", True)
            include_assets = inputs.get("include_assets", True)
            include_staffs = inputs.get("include_staffs", True)
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            summary = {}
            company_stats = {}
            voucher_stats = {}
            asset_stats = {}
            staff_stats = {}
            
            # 公司统计
            if include_companies:
                company_query = """
                    SELECT
                        COUNT(*) as total_companies,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_companies,
                        COUNT(CASE WHEN company_type = 'parent' THEN 1 END) as parent_companies,
                        COUNT(CASE WHEN company_type = 'subsidiary' THEN 1 END) as subsidiary_companies,
                        SUM(registered_capital) as total_capital
                    FROM companies
                """
                cursor.execute(company_query)
                company_row = cursor.fetchone()
                
                company_stats = {
                    "total_companies": company_row[0] or 0,
                    "active_companies": company_row[1] or 0,
                    "parent_companies": company_row[2] or 0,
                    "subsidiary_companies": company_row[3] or 0,
                    "total_capital": float(company_row[4]) if company_row[4] else 0
                }
            
            # 凭证统计
            if include_vouchers:
                voucher_conditions = []
                voucher_params = []
                
                if date_from:
                    voucher_conditions.append("date >= ?")
                    voucher_params.append(date_from)
                
                if date_to:
                    voucher_conditions.append("date <= ?")
                    voucher_params.append(date_to)
                
                voucher_where = "WHERE " + " AND ".join(voucher_conditions) if voucher_conditions else ""
                
                voucher_query = f"""
                    SELECT
                        COUNT(*) as total_vouchers,
                        SUM(total_debit) as total_debit,
                        SUM(total_credit) as total_credit,
                        COUNT(CASE WHEN date >= date('now', '-30 days') THEN 1 END) as recent_vouchers
                    FROM vouchers
                    {voucher_where}
                """
                cursor.execute(voucher_query, voucher_params)
                voucher_row = cursor.fetchone()
                
                # 分录统计
                entry_conditions = []
                entry_params = []
                
                if date_from:
                    entry_conditions.append("v.date >= ?")
                    entry_params.append(date_from)
                
                if date_to:
                    entry_conditions.append("v.date <= ?")
                    entry_params.append(date_to)
                
                entry_where = "WHERE " + " AND ".join(entry_conditions) if entry_conditions else ""
                
                entry_query = f"""
                    SELECT COUNT(*) as total_entries
                    FROM voucher_entries ve
                    JOIN vouchers v ON ve.voucher_id = v.id
                    {entry_where}
                """
                cursor.execute(entry_query, entry_params)
                entry_row = cursor.fetchone()
                
                voucher_stats = {
                    "total_vouchers": voucher_row[0] or 0,
                    "total_debit": float(voucher_row[1]) if voucher_row[1] else 0,
                    "total_credit": float(voucher_row[2]) if voucher_row[2] else 0,
                    "recent_vouchers": voucher_row[3] or 0,
                    "total_entries": entry_row[0] or 0
                }
            
            # 资产统计
            if include_assets:
                asset_query = """
                    SELECT
                        COUNT(*) as total_assets,
                        SUM(value) as total_value,
                        COUNT(CASE WHEN status = '在用' THEN 1 END) as active_assets,
                        COUNT(CASE WHEN status = '报废' THEN 1 END) as scrapped_assets
                    FROM assets
                """
                cursor.execute(asset_query)
                asset_row = cursor.fetchone()
                
                asset_stats = {
                    "total_assets": asset_row[0] or 0,
                    "total_value": float(asset_row[1]) if asset_row[1] else 0,
                    "active_assets": asset_row[2] or 0,
                    "scrapped_assets": asset_row[3] or 0
                }
            
            # 员工统计
            if include_staffs:
                staff_query = """
                    SELECT
                        COUNT(*) as total_staffs,
                        COUNT(CASE WHEN status = '在职' THEN 1 END) as active_staffs,
                        COUNT(DISTINCT role_code) as total_roles
                    FROM staffs
                """
                cursor.execute(staff_query)
                staff_row = cursor.fetchone()
                
                staff_stats = {
                    "total_staffs": staff_row[0] or 0,
                    "active_staffs": staff_row[1] or 0,
                    "total_roles": staff_row[2] or 0
                }
            
            # 汇总数据
            summary = {
                "timestamp": datetime.now().isoformat(),
                "date_range": {
                    "from": date_from,
                    "to": date_to
                },
                "includes": {
                    "companies": include_companies,
                    "vouchers": include_vouchers,
                    "assets": include_assets,
                    "staffs": include_staffs
                }
            }
            
            return {
                "success": True,
                "data": {
                    "summary": summary,
                    "company_stats": company_stats if include_companies else None,
                    "voucher_stats": voucher_stats if include_vouchers else None,
                    "asset_stats": asset_stats if include_assets else None,
                    "staff_stats": staff_stats if include_staffs else None
                }
            }
            
        except Exception as e:
            logger.error(f"业务汇总统计失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


# 内置工具注册表
BUILTIN_TOOLS = {
    "query_employee_info": QueryEmployeeInfoTool(),
    "count_employees": CountEmployeesTool(),
    "query_department_expenses": QueryDepartmentExpensesTool(),
    "search_vouchers": SearchVouchersTool(),
    "calculate_statistics": CalculateStatisticsTool(),
    "filter_data": FilterDataTool(),
    "query_companies": QueryCompaniesTool(),
    "company_statistics": CompanyStatisticsTool(),
    "query_subject_accounts": QuerySubjectAccountsTool(),
    "subject_account_statistics": SubjectAccountStatisticsTool(),
    "query_voucher_entries": QueryVoucherEntriesTool(),
    "voucher_statistics": VoucherStatisticsTool(),
    "query_assets": QueryAssetsTool(),
    "asset_statistics": AssetStatisticsTool(),
    "query_staffs": QueryStaffsTool(),
    "query_roles": QueryRolesTool(),
    "staff_role_statistics": StaffRoleStatisticsTool(),
    "cross_table_query": CrossTableQueryTool(),
    "business_summary": BusinessSummaryTool(),
}


def get_builtin_tool(tool_id: str) -> Optional[BuiltinTool]:
    """获取内置工具"""
    return BUILTIN_TOOLS.get(tool_id)


def list_builtin_tools() -> List[Dict[str, Any]]:
    """列出所有内置工具"""
    return [tool.to_dict() for tool in BUILTIN_TOOLS.values()]


def get_builtin_tools_by_category() -> Dict[str, List[Dict[str, Any]]]:
    """按分类获取内置工具"""
    categories = {}
    for tool in BUILTIN_TOOLS.values():
        category = tool.category
        if category not in categories:
            categories[category] = []
        categories[category].append(tool.to_dict())
    
    return categories