"""
工具注册表
"""

from typing import Dict, Any, List, Optional
from .base import BuiltinTool
from .employee_tools import (
    QueryEmployeeInfoTool,
    CountEmployeesTool,
    QueryStaffsTool,
    QueryRolesTool,
    StaffRoleStatisticsTool
)
from .finance_tools import (
    QueryDepartmentExpensesTool,
    SearchVouchersTool,
    QueryVoucherEntriesTool,
    VoucherStatisticsTool
)
from .company_tools import (
    QueryCompaniesTool,
    CompanyStatisticsTool
)
from .accounting_tools import (
    QuerySubjectAccountsTool,
    SubjectAccountStatisticsTool
)
from .asset_tools import (
    QueryAssetsTool,
    AssetStatisticsTool
)
from .cross_table_tools import (
    CrossTableQueryTool,
    BusinessSummaryTool
)
from .general_tools import (
    CalculateStatisticsTool,
    FilterDataTool
)

# 内置工具注册表
BUILTIN_TOOLS = {
    "query_employee_info": QueryEmployeeInfoTool(),
    "count_employees": CountEmployeesTool(),
    "query_department_expenses": QueryDepartmentExpensesTool(),
    "search_vouchers": SearchVouchersTool(),
    "calculate_statistics": CalculateStatisticsTool(),
    "filter_data": FilterDataTool(),
    "query_companies": QueryCompaniesTool(),
    "company_statistics": CompanyStatisticsTool(),
    "query_subject_accounts": QuerySubjectAccountsTool(),
    "subject_account_statistics": SubjectAccountStatisticsTool(),
    "query_voucher_entries": QueryVoucherEntriesTool(),
    "voucher_statistics": VoucherStatisticsTool(),
    "query_assets": QueryAssetsTool(),
    "asset_statistics": AssetStatisticsTool(),
    "query_staffs": QueryStaffsTool(),
    "query_roles": QueryRolesTool(),
    "staff_role_statistics": StaffRoleStatisticsTool(),
    "cross_table_query": CrossTableQueryTool(),
    "business_summary": BusinessSummaryTool(),
}


def get_builtin_tool(tool_id: str) -> Optional[BuiltinTool]:
    """获取内置工具"""
    return BUILTIN_TOOLS.get(tool_id)


def list_builtin_tools() -> List[Dict[str, Any]]:
    """列出所有内置工具"""
    return [tool.to_dict() for tool in BUILTIN_TOOLS.values()]


def get_builtin_tools_by_category() -> Dict[str, List[Dict[str, Any]]]:
    """按分类获取内置工具"""
    categories = {}
    for tool in BUILTIN_TOOLS.values():
        category = tool.category
        if category not in categories:
            categories[category] = []
        categories[category].append(tool.to_dict())
    
    return categories