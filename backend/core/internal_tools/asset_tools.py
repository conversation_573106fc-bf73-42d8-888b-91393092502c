"""
资产相关工具
"""

import logging
from typing import Dict, Any, List, Optional
from core.db import get_db_connection
from .base import BuiltinTool

logger = logging.getLogger(__name__)


class QueryAssetsTool(BuiltinTool):
    """查询资产信息工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="query_assets",
            name="查询资产信息",
            description="根据条件查询资产信息",
            inputs=[
                {"name": "asset_id", "type": "number", "description": "资产ID", "required": False},
                {"name": "asset_name", "type": "string", "description": "资产名称", "required": False},
                {"name": "asset_type", "type": "string", "description": "资产类别", "required": False},
                {"name": "status", "type": "string", "description": "资产状态", "required": False},
                {"name": "min_value", "type": "number", "description": "最小价值", "required": False},
                {"name": "max_value", "type": "number", "description": "最大价值", "required": False}
            ],
            outputs=[
                {"name": "assets", "type": "array", "description": "资产信息列表"},
                {"name": "count", "type": "number", "description": "查询到的资产数量"},
                {"name": "total_value", "type": "number", "description": "资产总价值"}
            ],
            category="asset"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行查询资产信息"""
        try:
            self.validate_inputs(inputs)
            
            asset_id = inputs.get("asset_id")
            asset_name = inputs.get("asset_name")
            asset_type = inputs.get("asset_type")
            status = inputs.get("status")
            min_value = inputs.get("min_value")
            max_value = inputs.get("max_value")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if asset_id:
                conditions.append("id = ?")
                params.append(asset_id)
            
            if asset_name:
                conditions.append("name LIKE ?")
                params.append(f"%{asset_name}%")
            
            if asset_type:
                conditions.append("type = ?")
                params.append(asset_type)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            if min_value is not None:
                conditions.append("value >= ?")
                params.append(min_value)
            
            if max_value is not None:
                conditions.append("value <= ?")
                params.append(max_value)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 执行查询
            conn = get_db_connection()
            cursor = conn.cursor()
            
            query = f"""
                SELECT id, name, type, value, status, remark
                FROM assets 
                {where_clause}
                ORDER BY name
            """
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            # 转换结果
            assets = []
            total_value = 0
            
            for row in rows:
                asset = {
                    "id": row[0],
                    "name": row[1],
                    "type": row[2],
                    "value": float(row[3]) if row[3] else 0,
                    "status": row[4],
                    "remark": row[5]
                }
                assets.append(asset)
                total_value += asset["value"]
            
            return {
                "success": True,
                "data": {
                    "assets": assets,
                    "count": len(assets),
                    "total_value": total_value
                }
            }
            
        except Exception as e:
            logger.error(f"查询资产信息失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }


class AssetStatisticsTool(BuiltinTool):
    """资产统计工具"""
    
    def __init__(self):
        super().__init__(
            tool_id="asset_statistics",
            name="资产统计分析",
            description="对资产进行统计分析",
            inputs=[
                {"name": "group_by", "type": "string", "description": "分组字段(type/status)", "required": False},
                {"name": "asset_type", "type": "string", "description": "筛选资产类别", "required": False},
                {"name": "status", "type": "string", "description": "筛选资产状态", "required": False}
            ],
            outputs=[
                {"name": "total_count", "type": "number", "description": "资产总数"},
                {"name": "total_value", "type": "number", "description": "资产总价值"},
                {"name": "average_value", "type": "number", "description": "平均价值"},
                {"name": "group_statistics", "type": "object", "description": "分组统计结果"},
                {"name": "value_distribution", "type": "object", "description": "价值分布"}
            ],
            category="asset"
        )
    
    def execute(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """执行资产统计分析"""
        try:
            self.validate_inputs(inputs)
            
            group_by = inputs.get("group_by", "type")
            asset_type = inputs.get("asset_type")
            status = inputs.get("status")
            
            # 构建查询条件
            conditions = []
            params = []
            
            if asset_type:
                conditions.append("type = ?")
                params.append(asset_type)
            
            if status:
                conditions.append("status = ?")
                params.append(status)
            
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
            
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # 基本统计
            basic_query = f"""
                SELECT 
                    COUNT(*) as total_count,
                    SUM(value) as total_value,
                    AVG(value) as average_value
                FROM assets 
                {where_clause}
            """
            cursor.execute(basic_query, params)
            basic_row = cursor.fetchone()
            
            total_count = basic_row[0] if basic_row[0] else 0
            total_value = float(basic_row[1]) if basic_row[1] else 0
            average_value = float(basic_row[2]) if basic_row[2] else 0
            
            # 分组统计
            valid_group_fields = ["type", "status"]
            if group_by not in valid_group_fields:
                group_by = "type"
            
            group_query = f"""
                SELECT {group_by}, 
                       COUNT(*) as count,
                       SUM(value) as total_value,
                       AVG(value) as avg_value
                FROM assets 
                {where_clause}
                GROUP BY {group_by}
                ORDER BY total_value DESC
            """
            cursor.execute(group_query, params)
            group_rows = cursor.fetchall()
            
            group_statistics = {}
            for row in group_rows:
                key = row[0] or "未分类"
                group_statistics[key] = {
                    "count": row[1],
                    "total_value": float(row[2]) if row[2] else 0,
                    "avg_value": float(row[3]) if row[3] else 0
                }
            
            # 价值分布
            value_ranges = [
                ("0-1000", 0, 1000),
                ("1000-10000", 1000, 10000),
                ("10000-100000", 10000, 100000),
                ("100000+", 100000, None)
            ]
            
            value_distribution = {}
            for range_name, min_val, max_val in value_ranges:
                if max_val is None:
                    range_conditions = conditions + ["value >= ?"]
                    range_params = params + [min_val]
                else:
                    range_conditions = conditions + ["value >= ? AND value < ?"]
                    range_params = params + [min_val, max_val]
                
                range_where = "WHERE " + " AND ".join(range_conditions) if range_conditions else ""
                range_query = f"SELECT COUNT(*) FROM assets {range_where}"
                cursor.execute(range_query, range_params)
                count = cursor.fetchone()[0]
                
                value_distribution[range_name] = count
            
            return {
                "success": True,
                "data": {
                    "total_count": total_count,
                    "total_value": total_value,
                    "average_value": average_value,
                    "group_statistics": group_statistics,
                    "value_distribution": value_distribution
                }
            }
            
        except Exception as e:
            logger.error(f"资产统计分析失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e)
            }