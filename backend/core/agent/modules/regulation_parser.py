"""
规章制度解析模块 - 处理规章制度解析相关的方法
"""

import logging
from typing import Dict, Any, List
import json
import re
from datetime import datetime
from langchain_core.messages import HumanMessage, SystemMessage

logger = logging.getLogger(__name__)

class RegulationParser:
    """规章制度解析器"""
    
    def __init__(self, agent_executor):
        self.agent = agent_executor
        self.max_chunk_size = 8000  # 每个chunk的最大字符数
        self.chunk_overlap = 200   # chunk之间的重叠字符数
    
    def _split_document_into_chunks(self, document_content: str) -> List[str]:
        """将长文档分割成多个chunk"""
        logger.info(f"开始分割文档，原始文档长度: {len(document_content)} 字符")
        
        # 如果文档长度不超过最大chunk大小，直接返回
        if len(document_content) <= self.max_chunk_size:
            logger.info("文档长度较短，无需分割")
            return [document_content]
        
        chunks = []
        start_pos = 0
        
        while start_pos < len(document_content):
            # 计算当前chunk的结束位置
            end_pos = min(start_pos + self.max_chunk_size, len(document_content))
            
            # 如果不是最后一个chunk，尝试在句子边界处分割
            if end_pos < len(document_content):
                # 寻找最近的句子结束符（。！？.!?）
                for i in range(end_pos - 1, max(start_pos, end_pos - 500), -1):
                    if document_content[i] in '。！？.!?':
                        end_pos = i + 1
                        break
                else:
                    # 如果没有找到句子结束符，寻找段落分隔符
                    for i in range(end_pos - 1, max(start_pos, end_pos - 300), -1):
                        if document_content[i] == '\n' and document_content[i-1:i+2] != '\n\n':
                            end_pos = i + 1
                            break
            
            # 提取当前chunk
            chunk = document_content[start_pos:end_pos].strip()
            
            # 为chunk添加上下文信息（除了第一个chunk）
            if start_pos > 0:
                chunk = f"[文档片段，从位置 {start_pos} 开始]\n{chunk}"
            
            chunks.append(chunk)
            logger.debug(f"创建chunk {len(chunks)}: 位置 {start_pos}-{end_pos}, 长度 {len(chunk)} 字符")
            
            # 更新下一个chunk的起始位置，考虑重叠
            start_pos = end_pos - self.chunk_overlap if end_pos < len(document_content) else end_pos
        
        logger.info(f"文档分割完成，共生成 {len(chunks)} 个chunk")
        return chunks
    
    async def parse_audit_regulations(self, document_content: str) -> Dict[str, Any]:
        """解析规章制度文档，提取分类规则"""
        logger.info("开始解析规章制度文档")
        
        if not self.agent.llm_manager.is_configured():
            return {"success": False, "error": "LLM未配置"}
        
        try:
            # 检查文档长度，决定是否需要分块处理
            if len(document_content) > self.max_chunk_size:
                logger.info(f"文档较长({len(document_content)}字符)，将采用分块处理")
                return await self._parse_document_in_chunks(document_content)
            else:
                logger.info(f"文档较短({len(document_content)}字符)，将直接处理")
                return await self._parse_single_chunk(document_content)
                
        except Exception as e:
            logger.error(f"解析规章制度失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _parse_single_chunk(self, document_content: str) -> Dict[str, Any]:
        """解析单个文档chunk"""
        try:
            # 构建解析提示
            parse_prompt = self._build_regulation_parse_prompt(document_content)
            
            # 发送请求给LLM
            messages = [
                SystemMessage(content="你是一个专业的财务规章制度解析专家，擅长从文档中提取和分类各种报销规定。"),
                HumanMessage(content=parse_prompt)
            ]
            
            # 记录发送给AI服务器的消息内容
            formatted_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    if msg.__class__.__name__ == 'SystemMessage':
                        formatted_messages.append({"role": "system", "content": msg.content})
                    elif msg.__class__.__name__ == 'HumanMessage':
                        formatted_messages.append({"role": "user", "content": msg.content})
                    elif msg.__class__.__name__ == 'AIMessage':
                        formatted_messages.append({"role": "assistant", "content": msg.content})
            
            logger.info(f"[REGULATION_PARSE_REQUEST] 发送给AI服务器的规章制度解析消息: {json.dumps({'messages': formatted_messages}, ensure_ascii=False)}")
            
            # 获取LLM响应
            response = await self.agent.llm_manager.get_llm().ainvoke(messages)
            response_content = response.content
            
            # 尝试解析JSON响应
            try:
                parsed_data = json.loads(response_content)
                return {
                    "success": True,
                    "data": parsed_data,
                    "raw_response": response_content,
                    "chunk_count": 1
                }
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试提取JSON部分
                json_match = re.search(r'\{[\s\S]*\}', response_content)
                if json_match:
                    try:
                        parsed_data = json.loads(json_match.group())
                        return {
                            "success": True,
                            "data": parsed_data,
                            "raw_response": response_content,
                            "chunk_count": 1
                        }
                    except json.JSONDecodeError:
                        pass
                
                return {
                    "success": False,
                    "error": "无法解析AI响应为JSON格式",
                    "raw_response": response_content
                }
                
        except Exception as e:
            logger.error(f"解析单个文档chunk失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _parse_document_in_chunks(self, document_content: str) -> Dict[str, Any]:
        """分块解析长文档"""
        logger.info("开始分块解析文档")
        
        try:
            # 将文档分割成多个chunk
            chunks = self._split_document_into_chunks(document_content)
            chunk_results = []
            
            # 逐个解析chunk
            for i, chunk in enumerate(chunks):
                logger.info(f"正在解析第 {i+1}/{len(chunks)} 个chunk")
                
                # 为分块解析构建特殊的提示
                chunk_prompt = self._build_chunk_parse_prompt(chunk, i+1, len(chunks))
                
                # 发送请求给LLM
                messages = [
                    SystemMessage(content="你是一个专业的财务规章制度解析专家，擅长从文档片段中提取和分类各种报销规定。"),
                    HumanMessage(content=chunk_prompt)
                ]
                
                # 记录发送给AI服务器的消息内容
                formatted_messages = []
                for msg in messages:
                    if hasattr(msg, 'content'):
                        if msg.__class__.__name__ == 'SystemMessage':
                            formatted_messages.append({"role": "system", "content": msg.content})
                        elif msg.__class__.__name__ == 'HumanMessage':
                            formatted_messages.append({"role": "user", "content": msg.content})
                        elif msg.__class__.__name__ == 'AIMessage':
                            formatted_messages.append({"role": "assistant", "content": msg.content})
                
                logger.info(f"[REGULATION_CHUNK_PARSE_REQUEST] 发送给AI服务器的规章制度分块解析消息: {json.dumps({'messages': formatted_messages}, ensure_ascii=False)}")
                
                # 获取LLM响应
                response = await self.agent.llm_manager.get_llm().ainvoke(messages)
                response_content = response.content
                
                # 尝试解析JSON响应
                try:
                    chunk_data = json.loads(response_content)
                    chunk_results.append({
                        "chunk_index": i,
                        "chunk_data": chunk_data,
                        "raw_response": response_content
                    })
                    logger.info(f"第 {i+1} 个chunk解析成功")
                except json.JSONDecodeError:
                    # 如果不是JSON格式，尝试提取JSON部分
                    json_match = re.search(r'\{[\s\S]*\}', response_content)
                    if json_match:
                        try:
                            chunk_data = json.loads(json_match.group())
                            chunk_results.append({
                                "chunk_index": i,
                                "chunk_data": chunk_data,
                                "raw_response": response_content
                            })
                            logger.info(f"第 {i+1} 个chunk解析成功（提取JSON）")
                        except json.JSONDecodeError:
                            logger.warning(f"第 {i+1} 个chunk解析失败，无法解析JSON")
                            chunk_results.append({
                                "chunk_index": i,
                                "chunk_data": None,
                                "raw_response": response_content,
                                "error": "无法解析JSON"
                            })
                    else:
                        logger.warning(f"第 {i+1} 个chunk解析失败，无有效JSON")
                        chunk_results.append({
                            "chunk_index": i,
                            "chunk_data": None,
                            "raw_response": response_content,
                            "error": "无有效JSON"
                        })
            
            # 合并所有chunk的解析结果
            merged_result = self._merge_chunk_results(chunk_results)
            
            return {
                "success": True,
                "data": merged_result,
                "chunk_results": chunk_results,
                "chunk_count": len(chunks)
            }
            
        except Exception as e:
            logger.error(f"分块解析文档失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _build_chunk_parse_prompt(self, chunk_content: str, chunk_index: int, total_chunks: int) -> str:
        """构建分块解析提示"""
        # 读取prompt模板文件
        import os
        template_path = os.path.join(os.path.dirname(__file__), '..', 'prompt_templates', 'extract_regulations_with_workflows.txt')
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # 替换模板中的占位符
        base_prompt = template_content.replace('{document_content}', chunk_content)
        
        # 添加分块特定的说明
        chunk_specific_prompt = f"""
请仔细分析以下规章制度文档片段（第 {chunk_index}/{total_chunks} 片段），提取其中的报销规定，并按照报销类型进行分类整理。

{base_prompt}

9. 这是文档的第 {chunk_index}/{total_chunks} 片段，请专注于当前片段的内容
"""
        return chunk_specific_prompt
    
    def _merge_chunk_results(self, chunk_results: List[Dict]) -> Dict[str, Any]:
        """合并多个chunk的解析结果"""
        logger.info("开始合并多个chunk的解析结果")
        
        try:
            merged_categories = {}
            category_type_mapping = {}  # 用于处理类型名称的标准化
            
            # 处理每个chunk的结果
            for chunk_result in chunk_results:
                if chunk_result.get("chunk_data") and "regulation_categories" in chunk_result["chunk_data"]:
                    categories = chunk_result["chunk_data"]["regulation_categories"]
                    
                    for category in categories:
                        category_type = category.get("type", "").strip()
                        
                        if not category_type:
                            continue
                        
                        # 标准化类型名称（处理可能的变体）
                        normalized_type = self._normalize_category_type(category_type)
                        
                        # 如果该类型已存在，合并规则
                        if normalized_type in merged_categories:
                            existing_category = merged_categories[normalized_type]
                            
                            # 合并描述（取更详细的描述）
                            existing_desc = existing_category.get("description", "")
                            new_desc = category.get("description", "")
                            if len(new_desc) > len(existing_desc):
                                existing_category["description"] = new_desc
                            
                            # 合并规则（去重）
                            existing_rules = existing_category.get("rules", [])
                            new_rules = category.get("rules", [])
                            
                            for new_rule in new_rules:
                                # 处理新旧格式的规则
                                if isinstance(new_rule, str):
                                    # 旧格式：字符串
                                    rule_content = new_rule.strip()
                                    rule_workflows = []
                                elif isinstance(new_rule, dict):
                                    # 新格式：对象
                                    rule_content = new_rule.get("rule_content", "").strip()
                                    rule_workflows = new_rule.get("workflows", [])
                                else:
                                    continue
                                
                                if rule_content and not self._is_duplicate_rule_object(rule_content, rule_workflows, existing_rules):
                                    existing_rules.append({
                                        "rule_content": rule_content,
                                        "workflows": rule_workflows
                                    })
                            
                            existing_category["rules"] = existing_rules
                            logger.debug(f"合并类型 '{normalized_type}'，现有规则数: {len(existing_category['rules'])}")
                        else:
                            # 新类型，直接添加
                            # 处理规则格式
                            processed_rules = []
                            for rule in category.get("rules", []):
                                if isinstance(rule, str):
                                    # 旧格式：字符串
                                    processed_rules.append({
                                        "rule_content": rule.strip(),
                                        "workflows": []
                                    })
                                elif isinstance(rule, dict):
                                    # 新格式：对象
                                    processed_rules.append({
                                        "rule_content": rule.get("rule_content", "").strip(),
                                        "workflows": rule.get("workflows", [])
                                    })
                            
                            merged_categories[normalized_type] = {
                                "type": normalized_type,
                                "description": category.get("description", ""),
                                "rules": processed_rules
                            }
                            logger.debug(f"添加新类型 '{normalized_type}'，规则数: {len(merged_categories[normalized_type]['rules'])}")
            
            # 转换为最终格式
            final_categories = list(merged_categories.values())
            
            logger.info(f"合并完成，共生成 {len(final_categories)} 个分类")
            for category in final_categories:
                logger.debug(f"分类 '{category['type']}' 包含 {len(category['rules'])} 条规则")
            
            return {
                "regulation_categories": final_categories,
                "merge_summary": {
                    "total_chunks": len(chunk_results),
                    "successful_chunks": len([r for r in chunk_results if r.get("chunk_data")]),
                    "merged_categories": len(final_categories)
                }
            }
            
        except Exception as e:
            logger.error(f"合并chunk结果失败: {str(e)}")
            return {"regulation_categories": []}
    
    def _normalize_category_type(self, category_type: str) -> str:
        """标准化分类类型名称"""
        # 定义类型名称的标准化映射
        type_mapping = {
            "差旅": "差旅费",
            "差旅费": "差旅费",
            "出差": "差旅费",
            "出差费用": "差旅费",
            "交通": "交通费",
            "交通费": "交通费",
            "住宿": "住宿费",
            "住宿费": "住宿费",
            "办公": "办公用品",
            "办公用品": "办公用品",
            "办公费": "办公用品",
            "招待": "业务招待费",
            "业务招待": "业务招待费",
            "业务招待费": "业务招待费",
            "招待费": "业务招待费",
            "通讯": "通讯费",
            "通讯费": "通讯费",
            "电话": "通讯费",
            "电话费": "通讯费",
            "培训": "培训费",
            "培训费": "培训费",
            "福利": "福利费",
            "福利费": "福利费"
        }
        
        # 精确匹配
        if category_type in type_mapping:
            return type_mapping[category_type]
        
        # 模糊匹配
        for key, value in type_mapping.items():
            if key in category_type or category_type in key:
                return value
        
        # 如果没有匹配，返回原类型
        return category_type
    
    def _is_duplicate_rule(self, new_rule: str, existing_rules: set) -> bool:
        """检查是否为重复规则"""
        new_rule_normalized = new_rule.lower().strip()
        
        for existing_rule in existing_rules:
            existing_rule_normalized = existing_rule.lower().strip()
            
            # 完全匹配
            if new_rule_normalized == existing_rule_normalized:
                return True
            
            # 高度相似（编辑距离或包含关系）
            if (len(new_rule_normalized) > 10 and len(existing_rule_normalized) > 10 and
                (new_rule_normalized in existing_rule_normalized or
                 existing_rule_normalized in new_rule_normalized)):
                return True
        
        return False
    
    def _is_duplicate_rule_object(self, rule_content: str, rule_workflows: List, existing_rules: List) -> bool:
        """检查是否为重复规则对象"""
        rule_content_normalized = rule_content.lower().strip()
        
        for existing_rule in existing_rules:
            if isinstance(existing_rule, dict):
                existing_rule_content = existing_rule.get("rule_content", "").lower().strip()
                existing_rule_workflows = existing_rule.get("workflows", [])
            else:
                # 处理旧格式的规则（字符串）
                existing_rule_content = existing_rule.lower().strip()
                existing_rule_workflows = []
            
            # 完全匹配
            if rule_content_normalized == existing_rule_content:
                # 如果规则内容相同，检查工作流是否也相同
                if len(rule_workflows) == len(existing_rule_workflows):
                    # 简单检查工作流数量是否相同
                    # 实际应用中可能需要更详细的比较
                    return True
            
            # 高度相似（编辑距离或包含关系）
            if (len(rule_content_normalized) > 10 and len(existing_rule_content) > 10 and
                (rule_content_normalized in existing_rule_content or
                 existing_rule_content in rule_content_normalized)):
                return True
        
        return False
    
    def _build_regulation_parse_prompt(self, document_content: str) -> str:
        """构建规章制度解析提示"""
        # 读取prompt模板文件
        import os
        template_path = os.path.join(os.path.dirname(__file__), '..', 'prompt_templates', 'extract_regulations_with_workflows.txt')
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # 替换模板中的占位符
        return template_content.replace('{document_content}', document_content)
    
    async def save_parsed_regulations(self, parsed_data: Dict[str, Any], original_filename: str = "") -> Dict[str, Any]:
        """保存解析后的规章制度数据"""
        logger.info("保存解析后的规章制度数据")
        
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 检查是否有分块信息，如果有则使用专门的分块保存方法
            if parsed_data.get("merge_summary") and parsed_data.get("regulation_categories"):
                logger.info("检测到分块解析结果，使用add_chunked_regulation_document方法保存")
                result = rag_manager.add_chunked_regulation_document(parsed_data, original_filename)
            else:
                logger.info("未检测到分块解析结果，使用常规add_documents方法保存")
                # 将解析后的数据转换为RAG格式
                rag_documents = []
                
                for category in parsed_data.get("regulation_categories", []):
                    # 为每个分类的每条规定创建一个单独的文档
                    for rule in category.get("rules", []):
                        # 处理新旧格式的规则
                        if isinstance(rule, str):
                            # 旧格式：字符串
                            rule_content = rule.strip()
                            rule_workflows = []
                        elif isinstance(rule, dict):
                            # 新格式：对象
                            rule_content = rule.get("rule_content", "").strip()
                            rule_workflows = rule.get("workflows", [])
                        else:
                            continue
                        
                        if rule_content:  # 确保规则不为空
                            doc_content = f"类型：{category['type']}\n描述：{category['description']}\n\n规定：\n{rule_content}"
                            
                            rag_documents.append({
                                "title": f"{category['type']}报销规定",
                                "content": doc_content,
                                "category": category['type'],
                                "source": original_filename or "规章制度文档解析",
                                "rule_type": category['type'],
                                "rule_description": category['description'],
                                "rule_content": rule_content,
                                "workflows": rule_workflows
                            })
                
                # 保存到RAG系统
                result = rag_manager.add_documents(rag_documents)
            
            if result["success"]:
                return {
                    "success": True,
                    "message": result.get("message", f"成功保存解析后的规章制度数据"),
                    "saved_count": result.get("ids", []).__len__(),
                    "chunk_info": result.get("chunk_info", None)
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "保存失败")
                }
                
        except Exception as e:
            logger.error(f"保存解析后的规章制度失败: {str(e)}")
            return {"success": False, "error": str(e)}


# 扩展AgentExecutor类，添加规章制度解析功能
async def parse_audit_regulations(self, document_content: str) -> Dict[str, Any]:
    """解析规章制度文档"""
    parser = RegulationParser(self)
    return await parser.parse_audit_regulations(document_content)


async def save_parsed_regulations(self, parsed_data: Dict[str, Any], original_filename: str = "") -> Dict[str, Any]:
    """保存解析后的规章制度数据"""
    parser = RegulationParser(self)
    return await parser.save_parsed_regulations(parsed_data, original_filename)