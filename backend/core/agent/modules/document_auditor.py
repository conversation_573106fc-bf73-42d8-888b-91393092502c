"""
单据审核模块 - 处理单据审核相关的方法
"""

import logging
from typing import Dict, Any, List, Optional, AsyncGenerator
import json
import re
from datetime import datetime
from langchain_core.messages import HumanMessage, SystemMessage

logger = logging.getLogger(__name__)

class DocumentAuditor:
    """单据审核器"""
    
    def __init__(self, agent_executor):
        self.agent = agent_executor
    
    async def stream_audit_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """流式处理单据审核消息"""
        logger.info(f"Entering stream_audit_message with message={message}, user_id={user_id}")
        if not self.agent.llm_manager.is_configured():
            yield json.dumps({"success": False, "error": "LLM未配置"})
            return
        
        try:
            # 处理文件内容
            file_content = ""
            structured_content = ""
            if files and len(files) > 0:
                # 通过AI服务处理每个文件并提取结构化内容
                structured_content = await self.agent._process_files_with_ai(files, "invoice")
                # 将结构化内容添加到用户消息中
                if structured_content:
                    enhanced_message = f"{message}\n\n结构化信息：\n{structured_content}" if message.strip() else f"请分析以下文件内容并进行单据审核：\n{structured_content}"
                else:
                    enhanced_message = message
            else:
                enhanced_message = message

            logger.info(f"enhanced_message : {enhanced_message}")
            
            self.agent.memory.add_user_message(enhanced_message)
            
            # 单据审核模式不需要收集完整的上下文数据，只需要公司信息
            context_data = {
                "company": None,  # 单据审核模式不需要公司信息
                "subjects": [],   # 单据审核模式不需要科目信息
                "assets": [],     # 单据审核模式不需要资产信息
                "staff": [],      # 单据审核模式不需要员工信息
                "experience": []  # 单据审核模式不需要经验信息
            }
            
            # 使用RAG搜索相关的规章制度
            rag_results = await self._search_audit_rag(enhanced_message)
            
            # 格式化RAG结果为字符串
            rag_results_str = ""
            if rag_results:
                rag_results_str = "\n".join([
                    f"- {result.get('title', '无标题')} ({result.get('category', '无分类')}): {result.get('content', '')}"
                    for result in rag_results
                ])
            
            # 准备系统提示和用户提示
            system_prompt = self.agent.prompt_manager.build_prompt("audit_system_base", user_id=user_id)
            
            # 构建审核提示
            audit_prompt = self.agent.prompt_manager.build_prompt(
                "audit_document",
                user_input=enhanced_message,
                rag_results=rag_results_str,
                current_date=datetime.now().strftime("%Y-%m-%d"),
                user_id=user_id
            )
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=audit_prompt)
            ]
            
            # 流式生成
            full_response = ""
            # 记录发送给AI服务器的消息内容
            formatted_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    if msg.__class__.__name__ == 'SystemMessage':
                        formatted_messages.append({"role": "system", "content": msg.content})
                    elif msg.__class__.__name__ == 'HumanMessage':
                        formatted_messages.append({"role": "user", "content": msg.content})
                    elif msg.__class__.__name__ == 'AIMessage':
                        formatted_messages.append({"role": "assistant", "content": msg.content})
            
            logger.info(f"[STREAM_AUDIT_REQUEST] 发送给AI服务器的审核流式消息: {json.dumps({'messages': formatted_messages}, ensure_ascii=False)}")
            
            # 收集完整响应
            async for token in self.agent.llm_manager.stream(messages):
                full_response += token
                # 流式返回原始token给前端显示
                yield token
            
            # 保存完整回复到记忆
            self.agent.memory.add_ai_message(full_response)
            
            # 解析AI响应并生成结构化JSON
            try:
                # 尝试从响应中提取JSON结构
                audit_result = await self._parse_audit_response(full_response)
                
                # 生成最终的JSON响应
                final_response = {
                    "success": True,
                    "action": audit_result.get("action", "none"),
                    "answer": audit_result.get("answer", full_response),
                    "audit_conclusion": audit_result.get("audit_conclusion", "需要更多信息"),
                    "needs_more_info": audit_result.get("needs_more_info", False),
                    "required_info": audit_result.get("required_info", []),
                    "is_finished": audit_result.get("is_finished", True),
                    "analysis": audit_result.get("analysis", "")
                }
                
                # 返回结构化JSON响应，添加换行符分隔
                yield "\n" + json.dumps(final_response, ensure_ascii=False)
                
            except Exception as e:
                logger.error(f"解析审核响应失败: {str(e)}")
                # 如果解析失败，返回基本结构
                fallback_response = {
                    "success": True,
                    "action": "none",
                    "answer": full_response,
                    "audit_conclusion": "需要更多信息",
                    "needs_more_info": True,
                    "required_info": ["请提供更完整的单据信息"],
                    "is_finished": False,
                    "analysis": full_response
                }
                yield "\n" + json.dumps(fallback_response, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"流式处理单据审核消息失败: {str(e)}")
            error_msg = json.dumps({"success": False, "error": str(e)})
            yield error_msg
    
    async def _search_audit_rag(self, query: str) -> List[Dict]:
        """搜索单据审核相关的规章制度"""
        logger.info(f"Searching audit RAG with query: {query}")
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 搜索相关文档
            result = rag_manager.search_documents(query, n_results=5)
            
            if result["success"]:
                # 格式化搜索结果
                rag_results = []
                for doc in result["data"]:
                    rag_results.append({
                        "title": doc["metadata"].get("title", ""),
                        "content": doc["content"],
                        "category": doc["metadata"].get("category", ""),
                        "source": doc["metadata"].get("source", "")
                    })
                return rag_results
            else:
                logger.error(f"RAG搜索失败: {result.get('error', '未知错误')}")
                return []
        except Exception as e:
            logger.error(f"搜索审核RAG失败: {str(e)}")
            return []
    
    async def add_audit_rag_data(self, data: List[Dict]) -> Dict:
        """添加单据审核相关的RAG数据"""
        logger.info(f"Adding audit RAG data: {len(data)} items")
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 添加文档
            result = rag_manager.add_documents(data)
            
            if result["success"]:
                return {
                    "success": True,
                    "message": result["message"],
                    "ids": result.get("ids", [])
                }
            else:
                logger.error(f"添加RAG数据失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "未知错误")
                }
        except Exception as e:
            logger.error(f"添加审核RAG数据失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def list_audit_rag_data(self) -> Dict:
        """列出单据审核相关的RAG数据"""
        logger.info("Listing audit RAG data")
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 列出文档
            result = rag_manager.list_documents(limit=100, offset=0)
            
            if result["success"]:
                return {
                    "success": True,
                    "data": result["data"],
                    "count": result.get("count", 0)
                }
            else:
                logger.error(f"列出RAG数据失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "未知错误")
                }
        except Exception as e:
            logger.error(f"列出审核RAG数据失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def delete_audit_rag_data(self, item_id: str) -> Dict:
        """删除单据审核相关的RAG数据"""
        logger.info(f"Deleting audit RAG data: {item_id}")
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 删除文档
            result = rag_manager.delete_document(item_id)
            
            if result["success"]:
                return {
                    "success": True,
                    "message": result["message"]
                }
            else:
                logger.error(f"删除RAG数据失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "未知错误")
                }
        except Exception as e:
            logger.error(f"删除审核RAG数据失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def update_audit_rag_data(self, item_id: str, data: Dict) -> Dict:
        """更新单据审核相关的RAG数据"""
        logger.info(f"Updating audit RAG data: {item_id}")
        try:
            # 导入RAG管理器
            from core.rag import get_rag_manager
            
            # 获取RAG管理器
            rag_manager = get_rag_manager()
            
            # 更新文档
            result = rag_manager.update_document(item_id, data)
            
            if result["success"]:
                return {
                    "success": True,
                    "message": result["message"]
                }
            else:
                logger.error(f"更新RAG数据失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "未知错误")
                }
        except Exception as e:
            logger.error(f"更新审核RAG数据失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    
    async def _parse_audit_response(self, response_text: str) -> Dict[str, Any]:
        """解析AI审核响应，提取结构化信息"""
        try:
            import re
            import json
            
            # 默认值
            result = {
                "action": "none",
                "answer": response_text,
                "audit_conclusion": "需要更多信息",
                "needs_more_info": False,
                "required_info": [],
                "is_finished": True,
                "analysis": response_text
            }
            
            # 首先尝试提取标记格式的响应 - 使用更健壮的正则表达式
            tag_extracted = False
            
            # 改进的标记提取模式，支持多行内容和更灵活的格式
            tag_patterns = {
                'audit_conclusion': r'<\|audit_conclusion\|>\s*(.*?)\s*<\|/audit_conclusion\|>',
                'action': r'<\|action\|>\s*(.*?)\s*<\|/action\|>',
                'needs_more_info': r'<\|needs_more_info\|>\s*(.*?)\s*<\|/needs_more_info\|>',
                'required_info': r'<\|required_info\|>\s*(.*?)\s*<\|/required_info\|>',
                'is_finished': r'<\|is_finished\|>\s*(.*?)\s*<\|/is_finished\|>'
            }
            
            extracted_data = {}
            for key, pattern in tag_patterns.items():
                # 使用 DOTALL 标志支持多行匹配，IGNORECASE 提高容错性
                match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)
                if match:
                    extracted_value = match.group(1).strip()
                    # 清理内容中的多余空白字符
                    extracted_value = re.sub(r'\s+', ' ', extracted_value)
                    extracted_data[key] = extracted_value
                    logger.info(f"提取到标记 {key}: {extracted_data[key]}")
            
            # 如果提取到了关键标记，则认为是结构化响应
            if extracted_data and ('action' in extracted_data or 'audit_conclusion' in extracted_data):
                # 处理action字段
                action = extracted_data.get("action", "none").lower().strip()
                # 标准化action值
                if action in ["audit_complete", "审核通过", "通过"]:
                    action = "audit_complete"
                elif action in ["audit_rejected", "审核不通过", "不通过", "拒绝"]:
                    action = "audit_rejected"
                elif action in ["request_more_info", "需要更多信息", "补充信息"]:
                    action = "request_more_info"
                else:
                    action = "none"
                
                # 处理needs_more_info字段
                needs_more_info_str = extracted_data.get("needs_more_info", "false").lower().strip()
                needs_more_info = needs_more_info_str in ["true", "是", "需要", "1"]
                
                # 处理required_info字段
                required_info_str = extracted_data.get("required_info", "").strip()
                required_info = []
                if required_info_str:
                    # 支持多种分隔符
                    items = re.split(r'[，,、；;|\n]', required_info_str)
                    for item in items:
                        cleaned = item.strip()
                        if len(cleaned) > 1:  # 降低最小长度要求
                            required_info.append(cleaned)
                
                # 处理is_finished字段
                is_finished_str = extracted_data.get("is_finished", "true").lower().strip()
                is_finished = is_finished_str not in ["false", "否", "未完成", "0"]
                
                # 如果action是request_more_info，自动设置相关字段
                if action == "request_more_info":
                    needs_more_info = True
                    is_finished = False
                    if not required_info:
                        required_info = ["请提供更完整的单据信息"]
                
                # 更新结果
                result.update({
                    "action": action,
                    "audit_conclusion": extracted_data.get("audit_conclusion", "需要更多信息"),
                    "needs_more_info": needs_more_info,
                    "required_info": required_info,
                    "is_finished": is_finished,
                    "analysis": response_text
                })
                tag_extracted = True
                logger.info(f"成功解析标记格式响应: action={action}, needs_more_info={needs_more_info}, required_info={required_info}")
            
            # 如果标记解析成功，直接返回结果
            if tag_extracted:
                return result
            
            # 备用：尝试提取JSON格式的响应
            json_extracted = False
            
            # 尝试提取```json代码块中的JSON
            json_block_pattern = r'```json\s*\n?([\s\S]*?)```'
            json_match = re.search(json_block_pattern, response_text, re.IGNORECASE)
            
            if json_match:
                try:
                    json_str = json_match.group(1).strip()
                    logger.info(f"提取到JSON代码块: {json_str}")
                    json_data = json.loads(json_str)
                    
                    # 更新结果
                    if isinstance(json_data, dict):
                        result.update({
                            "action": json_data.get("action", "none"),
                            "audit_conclusion": json_data.get("audit_conclusion", "需要更多信息"),
                            "needs_more_info": json_data.get("needs_more_info", False),
                            "required_info": json_data.get("required_info", []),
                            "is_finished": json_data.get("is_finished", True),
                            "analysis": json_data.get("analysis", response_text)
                        })
                        json_extracted = True
                        logger.info(f"成功解析JSON格式响应: {result}")
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON代码块解析失败: {e}")
            
            # 如果JSON解析成功，直接返回结果
            if json_extracted:
                return result
            
            # 最后的文本分析备用方案 - 简化逻辑，提高准确性
            logger.info("使用文本分析备用方案解析响应")
            
            # 检查审核结论关键词
            if any(keyword in response_text for keyword in ["审核通过", "通过审核", "符合要求", "审核合格"]):
                result.update({
                    "audit_conclusion": "审核通过",
                    "action": "audit_complete",
                    "needs_more_info": False,
                    "is_finished": True
                })
            elif any(keyword in response_text for keyword in ["审核不通过", "审核拒绝", "不符合要求", "审核不合格"]):
                result.update({
                    "audit_conclusion": "审核不通过",
                    "action": "audit_rejected",
                    "needs_more_info": False,
                    "is_finished": True
                })
            elif any(keyword in response_text for keyword in ["需要补充", "请提供", "缺少", "信息不足", "需要更多"]):
                # 简化的信息提取逻辑
                required_info = []
                
                # 查找编号列表
                numbered_matches = re.findall(r'(?:^|\n)\s*\d+[\.、]\s*([^\n]+)', response_text, re.MULTILINE)
                for match in numbered_matches:
                    cleaned = match.strip()
                    if len(cleaned) > 2:
                        required_info.append(cleaned)
                
                # 查找项目符号列表
                bullet_matches = re.findall(r'(?:^|\n)\s*[-*•]\s*([^\n]+)', response_text, re.MULTILINE)
                for match in bullet_matches:
                    cleaned = match.strip()
                    if len(cleaned) > 2:
                        required_info.append(cleaned)
                
                if not required_info:
                    required_info = ["请提供更完整的单据信息"]
                
                result.update({
                    "audit_conclusion": "需要更多信息",
                    "action": "request_more_info",
                    "needs_more_info": True,
                    "required_info": required_info,
                    "is_finished": False
                })
            else:
                # 默认情况
                result.update({
                    "action": "none",
                    "audit_conclusion": "分析完成",
                    "is_finished": True
                })
            
            logger.info(f"文本分析结果: action={result['action']}, audit_conclusion={result['audit_conclusion']}")
            return result
            
        except Exception as e:
            logger.error(f"解析审核响应失败: {str(e)}")
            return {
                "action": "none",
                "answer": response_text,
                "audit_conclusion": "解析失败，请重试",
                "needs_more_info": True,
                "required_info": ["请重新提交审核请求"],
                "is_finished": False,
                "analysis": response_text
            }