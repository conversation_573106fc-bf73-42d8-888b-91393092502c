"""
智能体执行器基础类 - 协调各个组件，执行智能体任务的基础功能
"""

import logging
from typing import Dict, Any, List, Optional, Callable, AsyncGenerator
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage, BaseMessage
import json
import asyncio
from datetime import datetime
import uuid
import re

from ..llm import get_llm_manager
from ..memory import get_memory
from ..tools import get_tools, get_tool_registry
from ..prompts import get_prompt_manager
from ..chains import get_chain_manager
from ..monitoring import monitor_performance, get_cache_manager
from ...utils.picgo_util import get_picgo_util

from core.db import AsyncSessionLocal, engine
from models.subject import SubjectAccount
from models.asset import Asset
from models.role_staff import Staff
from models.company import Company
from sqlalchemy.future import select

# 其余代码已全部通过数据库异步获取，无需全局变量

logger = logging.getLogger(__name__)

class AgentExecutor:
    """智能体执行器"""
    
    def __init__(self, session_id: Optional[str] = None):
        self.session_id = session_id or str(uuid.uuid4())
        self.llm_manager = get_llm_manager()
        self.memory = get_memory(self.session_id)
        self.prompt_manager = get_prompt_manager()
        self.chain_manager = get_chain_manager()
        self.tool_registry = get_tool_registry()
        self.cache_manager = get_cache_manager()
        self.picgo_util = get_picgo_util()
        
        # 用于跟踪上传的图片，以便后续清理
        self.uploaded_images = []

        # 设置链式调用的LLM
        if self.llm_manager.is_configured():
            llm = self.llm_manager.get_llm()
            if llm is not None:
                self.chain_manager.set_llm(llm)
    
    def configure_llm(self, api_key: str, base_url: str, model: str, supports_vision: bool = False, use_ollama: bool = False, **kwargs) -> bool:
        """配置语言模型"""
        logger.info(f"[MULTIMODAL_DEBUG] Entering configure_llm with api_key={api_key[:5]}..., base_url={base_url}, model={model}, supports_vision={supports_vision}, use_ollama={use_ollama}")
        try:
            # 确定服务类型
            service_type = "ollama" if use_ollama else kwargs.get('service_type', 'openai_compatible')
            
            # 从 kwargs 中移除 service_type，避免重复传递
            kwargs_without_service_type = {k: v for k, v in kwargs.items() if k != 'service_type'}
            
            # 将 supports_vision 和 service_type 参数传递给 llm_manager
            self.llm_manager.configure(api_key, base_url, model, supports_vision=supports_vision, use_ollama=use_ollama, service_type=service_type, **kwargs_without_service_type)
            llm = self.llm_manager.get_llm()
            if llm is not None:
                self.chain_manager.set_llm(llm)
            logger.info(f"[MULTIMODAL_DEBUG] LLM配置成功，多模态支持: {supports_vision}, 服务类型: {service_type}")
            return True
        except Exception as e:
            logger.error(f"[MULTIMODAL_DEBUG] 配置LLM失败: {str(e)}")
            return False
    
    async def execute_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """执行工具"""
        logger.info(f"Entering execute_tool with tool_name={tool_name}, kwargs={kwargs}")
        try:
            result = await self.tool_registry.execute_tool(tool_name, **kwargs)

            logger.info(f"execute_tool, tool_name: {tool_name}, result: {result}")

            return {
                "success": result.success,
                "data": result.data,
                "error": result.error,
                "execution_time": result.execution_time,
                "tool_name": result.tool_name
            }
        except Exception as e:
            logger.error(f"执行工具失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        logger.info("Entering get_conversation_history")
        messages = self.memory.chat_history.get_messages()
        history = []
        
        for msg in messages:
            if isinstance(msg, HumanMessage):
                history.append({"role": "user", "content": msg.content})
            elif isinstance(msg, AIMessage):
                history.append({"role": "assistant", "content": msg.content})
            elif isinstance(msg, SystemMessage):
                history.append({"role": "system", "content": msg.content})
        
        return history
    
    def clear_history(self) -> None:
        """清空对话历史"""
        logger.info("Entering clear_history")
        self.memory.clear()
        # 清理上传的图片
        self._cleanup_uploaded_images()
    
    def _cleanup_uploaded_images(self) -> None:
        """清理已上传的图片"""
        logger.info(f"清理 {len(self.uploaded_images)} 张上传的图片")
        for image_info in self.uploaded_images:
            if 'delete_url' in image_info:
                try:
                    self.picgo_util.delete_image(image_info['delete_url'])
                except Exception as e:
                    logger.error(f"删除图片失败: {e}")
        self.uploaded_images.clear()
    
    async def _upload_image_for_multimodal(self, file_path: str) -> Optional[str]:
        """
        为多模态大模型上传图片到图床
        
        Args:
            file_path: 本地图片文件路径
            
        Returns:
            图片的公网 URL，失败时返回 None
        """
        logger.info(f"为多模态大模型上传图片: {file_path}")
        
        try:
            upload_result = self.picgo_util.upload_image(file_path)
            if upload_result:
                # 记录上传的图片信息，用于后续清理
                self.uploaded_images.append(upload_result)
                logger.info(f"图片上传成功，URL: {upload_result['url']}")
                return upload_result['url']
            else:
                logger.error(f"图片上传失败: {file_path}")
                return None
        except Exception as e:
            logger.error(f"上传图片时发生错误: {e}")
            return None
    
    def _is_multimodal_model(self) -> bool:
        """
        检查当前配置的模型是否支持多模态
        
        Returns:
            是否支持多模态
        """
        logger.info("[MULTIMODAL_DEBUG] 开始检查模型是否支持多模态")
        if not self.llm_manager.is_configured():
            logger.debug("[MULTIMODAL_DEBUG] LLM未配置，不支持多模态")
            return False
        
        # 从配置中获取前端设置的多模态支持标记
        supports_vision = self.llm_manager._config.get('supports_vision', False)
        model_name = self.llm_manager._config.get('model', 'unknown')
        
        logger.info(f"[MULTIMODAL_DEBUG] 模型 {model_name} 多模态支持标记: {supports_vision}")
        logger.info(f"[MULTIMODAL_DEBUG] LLM管理器配置: {self.llm_manager._config}")
        
        return supports_vision
    
    def _is_lmstudio_service(self) -> bool:
        """
        检查当前配置的服务是否为 LMStudio
        
        Returns:
            是否为 LMStudio 服务
        """
        if not self.llm_manager.is_configured():
            return False
        
        # 直接从配置中获取服务类型
        service_type = self.llm_manager._config.get('service_type', '')
        is_lmstudio = service_type == 'lmstudio'
        
        logger.info(f"[LMSTUDIO_DEBUG] 检查是否为LMStudio服务: service_type={service_type}, 结果={is_lmstudio}")
        return is_lmstudio
    
    def _create_multimodal_message(self, text_content: str, image_url: str) -> BaseMessage:
        """
        创建包含文本和图片的多模态消息
        
        Args:
            text_content: 文本内容
            image_url: 图片 URL 或 base64 编码
            
        Returns:
            多模态消息对象
        """
        logger.info(f"[MULTIMODAL_DEBUG] 创建多模态消息，文本长度: {len(text_content)}, 图片URL/base64: {image_url[:50] if image_url else 'None'}...")
        
        # 检查是否为 LMStudio 服务
        is_lmstudio = self._is_lmstudio_service()
        logger.info(f"[LMSTUDIO_DEBUG] 是否为LMStudio服务: {is_lmstudio}")
        
        if is_lmstudio:
            # LMStudio 使用纯 base64 格式，不需要 data URL 前缀
            # 检查 image_url 是否已经是 base64 格式（不包含 data URL 前缀）
            if image_url and not image_url.startswith('data:'):
                # LMStudio 特定的消息格式
                content = [
                    {"type": "text", "text": text_content},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_url}"}}
                ]
            else:
                # 如果已经是 data URL 格式，直接使用
                content = [
                    {"type": "text", "text": text_content},
                    {"type": "image_url", "image_url": {"url": image_url}}
                ]
        else:
            # 其他服务使用标准格式
            content = [
                {"type": "text", "text": text_content},
                {"type": "image_url", "image_url": {"url": image_url}}
            ]
        
        message = HumanMessage(content=content)
        logger.info(f"[LMSTUDIO_DEBUG] 多模态消息创建完成: {message}")
        return message
    
    def _convert_image_to_base64(self, file_path: str) -> Optional[str]:
        """
        将图片文件转换为 base64 编码
        
        Args:
            file_path: 本地图片文件路径
            
        Returns:
            图片的 base64 编码，失败时返回 None
        """
        logger.info(f"[LMSTUDIO_DEBUG] 将图片转换为base64: {file_path}")
        
        try:
            import base64
            from pathlib import Path
            
            if not Path(file_path).exists():
                logger.error(f"[LMSTUDIO_DEBUG] 文件不存在: {file_path}")
                return None
            
            # 读取图片文件并转换为 base64
            with open(file_path, 'rb') as image_file:
                image_data = image_file.read()
                base64_encoded = base64.b64encode(image_data).decode('utf-8')
                
                logger.info(f"[LMSTUDIO_DEBUG] 图片base64转换成功，长度: {len(base64_encoded)}")
                return base64_encoded
                
        except Exception as e:
            logger.error(f"[LMSTUDIO_DEBUG] 转换图片为base64时发生错误: {e}")
            return None
    
    async def _process_files(self, files: List[Dict]) -> str:
        """处理文件内容"""
        logger.info(f"Entering _process_files with {len(files) if files else 0} files")
        file_contents = []
        for file_info in files:
            try:
                file_path = file_info.get("file_path")
                logger.info(f"处理文件: {file_path}")  # 路径调试日志，改为 info
                file_name = file_info.get("file_name", "未知文件")
                file_type = file_info.get("file_type", "未知类型")
                
                if file_type == "image":
                    # OCR处理图片
                    result = await self.execute_tool("ocr_processor", file_path=file_path)
                    if result["success"]:
                        content = result["data"]["text"]
                        file_contents.append(f"文件：{file_name} (图片)\n内容：\n{content}\n")
                    else:
                        file_contents.append(f"文件：{file_name} (图片)\n处理失败：{result['error']}\n")
                elif file_type == "pdf":
                    # PDF处理
                    result = await self.execute_tool("pdf_processor", file_path=file_path)
                    if result["success"]:
                        content = result["data"]["text"]
                        file_contents.append(f"文件：{file_name} (PDF)\n内容：\n{content}\n")
                    else:
                        file_contents.append(f"文件：{file_name} (PDF)\n处理失败：{result['error']}\n")
                else:
                    file_contents.append(f"文件：{file_name}\n不支持的文件类型：{file_type}\n")
            except Exception as e:
                logger.error(f"处理文件失败: {str(e)}")
                file_contents.append(f"文件：{file_info.get('file_name', '未知文件')}\n处理失败：{str(e)}\n")
        
        return "\n".join(file_contents)

    async def _process_files_with_ai(self, files: List[Dict], extraction_type: str = "general") -> str:
        """通过AI服务处理文件，提取结构化JSON内容"""
        logger.info(f"[MULTIMODAL_DEBUG] Processing {len(files)} files with AI service for structured content extraction, type: {extraction_type}")
        structured_contents = []
        
        # 检查是否支持多模态
        is_multimodal = self._is_multimodal_model()
        logger.info(f"[MULTIMODAL_DEBUG] 当前模型是否支持多模态: {is_multimodal}")
        
        if is_multimodal:
            logger.info("[MULTIMODAL_DEBUG] 使用多模态处理流程")
        else:
            logger.info("[MULTIMODAL_DEBUG] 使用传统OCR处理流程")
        
        for file_info in files:
            try:
                file_path = file_info.get("file_path")
                file_name = file_info.get("file_name", "未知文件")
                file_type = file_info.get("file_type", "未知类型")
                
                # 处理图片文件
                if file_type == "image":
                    logger.info(f"Processing image file with AI: {file_name}")
                    
                    # 如果支持多模态，结合OCR结果和图片URL进行处理
                    if is_multimodal:
                        logger.info(f"使用多模态处理图片: {file_name}")
                        # 首先获取OCR结果作为参考
                        ocr_result = await self.execute_tool("ocr_processor", file_path=file_path)
                        ocr_content = ""
                        if ocr_result["success"]:
                            ocr_text = ocr_result["data"]["text"]
                            if isinstance(ocr_text, list):
                                ocr_content = "\n".join(ocr_text)
                            else:
                                ocr_content = str(ocr_text)
                        
                        # 检查是否为LMStudio服务
                        is_lmstudio = self._is_lmstudio_service()
                        logger.info(f"[LMSTUDIO_DEBUG] 是否为LMStudio服务: {is_lmstudio}")
                        
                        if is_lmstudio:
                            # LMStudio使用base64格式
                            logger.info(f"[LMSTUDIO_DEBUG] 使用base64格式处理图片: {file_name}")
                            image_url = self._convert_image_to_base64(file_path)
                            if image_url:
                                # 根据提取类型调用不同的AI服务（多模态版本，包含OCR参考）
                                if extraction_type == "invoice":
                                    structured_result = await self._extract_invoice_fields_multimodal(image_url, ocr_content)
                                elif extraction_type == "voucher":
                                    structured_result = await self._extract_voucher_fields_multimodal(image_url, ocr_content)
                                else:
                                    structured_result = await self._extract_document_fields_multimodal(image_url, ocr_content)
                            else:
                                logger.warning(f"[LMSTUDIO_DEBUG] 图片base64转换失败，回退到OCR处理: {file_name}")
                                structured_result = await self._process_image_with_ocr(file_path, extraction_type)
                        else:
                            # 其他服务使用picgo上传图片获取URL
                            logger.info(f"[MULTIMODAL_DEBUG] 使用picgo上传图片: {file_name}")
                            image_url = await self._upload_image_for_multimodal(file_path)
                            if image_url:
                                # 根据提取类型调用不同的AI服务（多模态版本，包含OCR参考）
                                if extraction_type == "invoice":
                                    structured_result = await self._extract_invoice_fields_multimodal(image_url, ocr_content)
                                elif extraction_type == "voucher":
                                    structured_result = await self._extract_voucher_fields_multimodal(image_url, ocr_content)
                                else:
                                    structured_result = await self._extract_document_fields_multimodal(image_url, ocr_content)
                            else:
                                logger.warning(f"图片上传失败，回退到OCR处理: {file_name}")
                                structured_result = await self._process_image_with_ocr(file_path, extraction_type)
                    else:
                        # 不支持多模态，使用OCR处理
                        logger.info(f"使用OCR处理图片: {file_name}")
                        structured_result = await self._process_image_with_ocr(file_path, extraction_type)
                    
                    if structured_result:
                        structured_contents.append(f"文件：{file_name}\n结构化信息：\n{json.dumps(structured_result, ensure_ascii=False, indent=2)}\n")
                    else:
                        structured_contents.append(f"文件：{file_name}\n结构化信息提取失败\n")
                
                elif file_type == "pdf":
                    logger.info(f"Processing PDF file with AI: {file_name}")
                    
                    # 首先进行PDF处理获取文本内容
                    pdf_result = await self.execute_tool("pdf_processor", file_path=file_path)
                    if not pdf_result["success"]:
                        logger.error(f"PDF processing failed for {file_name}: {pdf_result['error']}")
                        structured_contents.append(f"文件：{file_name}\n处理失败：{pdf_result['error']}\n")
                        continue
                    
                    pdf_text = pdf_result["data"]["text"]
                    if isinstance(pdf_text, list):
                        pdf_content = "\n".join(pdf_text)
                    else:
                        pdf_content = str(pdf_text)
                    
                    # 对于PDF文件，通常使用通用文档提取
                    structured_result = await self._extract_document_fields(pdf_content)
                    if structured_result:
                        structured_contents.append(f"文件：{file_name}\n结构化信息：\n{json.dumps(structured_result, ensure_ascii=False, indent=2)}\n")
                    else:
                        structured_contents.append(f"文件：{file_name}\n结构化信息提取失败\n")
                
                else:
                    structured_contents.append(f"文件：{file_name}\n不支持的文件类型：{file_type}\n")
                
            except Exception as e:
                logger.error(f"Error processing file {file_info.get('file_name', 'unknown')}: {str(e)}")
                structured_contents.append(f"文件：{file_info.get('file_name', '未知文件')}\n处理失败：{str(e)}\n")
        
        return "\n".join(structured_contents)
    
    async def _process_image_with_ocr(self, file_path: str, extraction_type: str) -> Dict[str, Any]:
        """使用OCR处理图片文件"""
        logger.info(f"使用OCR处理图片: {file_path}")
        
        # 首先进行OCR处理获取文本内容
        ocr_result = await self.execute_tool("ocr_processor", file_path=file_path)
        if not ocr_result["success"]:
            logger.error(f"OCR processing failed: {ocr_result['error']}")
            return None
        
        ocr_text = ocr_result["data"]["text"]
        if isinstance(ocr_text, list):
            ocr_content = "\n".join(ocr_text)
        else:
            ocr_content = str(ocr_text)
        
        # 根据提取类型调用不同的AI服务
        if extraction_type == "invoice":
            return await self._extract_invoice_fields(ocr_content)
        elif extraction_type == "voucher":
            return await self._extract_voucher_fields(ocr_content)
        else:
            return await self._extract_document_fields(ocr_content)

    async def _extract_invoice_fields(self, ocr_content: str) -> Dict[str, Any]:
        """从OCR文本中提取发票字段"""
        logger.info("开始提取发票字段...")
        
        # 构建发票字段提取的提示
        prompt_template = self.prompt_manager.build_prompt("extract_invoice_fields", ocr_content=ocr_content)
        
        try:
            # 调用AI服务
            messages = [
                SystemMessage(content="你是一个专业的发票信息提取助手，擅长从发票OCR文本中提取结构化信息。"),
                HumanMessage(content=prompt_template)
            ]
            
            # 使用非流式方式获取完整响应
            ai_result = await self.llm_manager.generate(messages)
            ai_response = ai_result.generations[0].message.content
            
            logger.info(f"AI发票字段提取响应: {ai_response}")
            
            # 解析JSON响应
            json_start = ai_response.find('{')
            json_end = ai_response.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_str = ai_response[json_start:json_end]
                result = json.loads(json_str)
                
                # 验证和修复提取的字段
                result = self._validate_and_fix_fields(result)
                return result
            else:
                logger.warning("未在AI响应中找到有效的JSON对象")
                return self._get_empty_invoice_result()
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return self._get_empty_invoice_result()
        except Exception as e:
            logger.error(f"调用AI服务提取发票字段失败: {e}")
            return self._get_empty_invoice_result()
    
    async def _extract_invoice_fields_multimodal(self, image_url: str, ocr_content: str = "") -> Dict[str, Any]:
        """使用多模态大模型从图片中直接提取发票字段"""
        logger.info("使用多模态大模型提取发票字段...")
        
        # 构建多模态发票字段提取的提示，包含OCR参考内容
        prompt_template = self.prompt_manager.build_prompt("extract_invoice_fields_multimodal")
        
        # 如果有OCR内容，添加到提示中
        if ocr_content.strip():
            prompt_template += f"\n\n参考OCR识别结果（可能包含错误，请以图片内容为准）：\n{ocr_content}"
        
        try:
            logger.info("[MULTIMODAL_DEBUG] 开始创建多模态消息并调用AI服务")
            # 创建多模态消息
            multimodal_message = self._create_multimodal_message(prompt_template, image_url)
            
            messages = [
                SystemMessage(content="你是一个专业的发票信息提取助手，擅长从发票图片中直接提取结构化信息。请仔细分析图片中的发票内容。"),
                multimodal_message
            ]
            
            logger.info("[MULTIMODAL_DEBUG] 多模态消息已创建，准备调用AI服务")
            # 使用非流式方式获取完整响应
            ai_result = await self.llm_manager.generate(messages)
            ai_response = ai_result.generations[0].message.content
            
            logger.info(f"[MULTIMODAL_DEBUG] 多模态AI发票字段提取响应: {ai_response}")
            
            # 解析JSON响应
            json_start = ai_response.find('{')
            json_end = ai_response.rfind('}') + 1
            
            if json_start != -1 and json_end != -1:
                json_str = ai_response[json_start:json_end]
                result = json.loads(json_str)
                
                # 验证和修复提取的字段
                result = self._validate_and_fix_fields(result)
                
                # 多模态分析完成，立即清理上传的图片
                logger.info("[MULTIMODAL_DEBUG] 多模态分析完成，开始清理上传的图片")
                self._cleanup_uploaded_images()
                
                return result
            else:
                logger.warning("未在多模态AI响应中找到有效的JSON对象")
                # 即使失败也要清理图片
                self._cleanup_uploaded_images()
                return self._get_empty_invoice_result()
                
        except json.JSONDecodeError as e:
            logger.error(f"多模态JSON解析失败: {e}")
            # 即使失败也要清理图片
            self._cleanup_uploaded_images()
            return self._get_empty_invoice_result()
        except Exception as e:
            logger.error(f"调用多模态AI服务提取发票字段失败: {e}")
            # 即使失败也要清理图片
            self._cleanup_uploaded_images()
            return self._get_empty_invoice_result()

    async def _extract_voucher_fields(self, content: str) -> Dict[str, Any]:
        """从内容中提取凭证相关字段"""
        logger.info("开始提取凭证相关字段...")
        
        # 构建凭证字段提取的提示
        prompt_template = self.prompt_manager.build_prompt("extract_voucher_fields", content=content)
        
        try:
            # 调用AI服务
            messages = [
                SystemMessage(content="你是一个专业的财务凭证分析助手，擅长从单据中提取用于生成会计凭证的结构化信息。"),
                HumanMessage(content=prompt_template)
            ]
            
            # 使用非流式方式获取完整响应
            ai_result = await self.llm_manager.generate(messages)
            ai_response = ai_result.generations[0].message.content
            
            logger.info(f"AI凭证字段提取响应: {ai_response}")
            
            # 尝试提取JSON内容
            import re
            json_match = re.search(r'```json\s*\n?([\s\S]*?)```', ai_response, re.IGNORECASE)
            if json_match:
                json_str = json_match.group(1).strip()
                try:
                    result = json.loads(json_str)
                    return result
                except json.JSONDecodeError:
                    logger.warning("JSON格式无效，返回原始响应")
                    return {"raw_response": ai_response}
            else:
                # 尝试直接解析JSON
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1
                
                if json_start != -1 and json_end != -1:
                    json_str = ai_response[json_start:json_end]
                    try:
                        result = json.loads(json_str)
                        return result
                    except json.JSONDecodeError:
                        pass
                
                # 如果无法解析JSON，返回原始响应
                return {"raw_response": ai_response}
                
        except Exception as e:
            logger.error(f"调用AI服务提取凭证字段失败: {e}")
            return {"error": str(e)}
    
    async def _extract_voucher_fields_multimodal(self, image_url: str, ocr_content: str = "") -> Dict[str, Any]:
        """使用多模态大模型从图片中直接提取凭证相关字段"""
        logger.info("使用多模态大模型提取凭证相关字段...")
        
        # 构建多模态凭证字段提取的提示，包含OCR参考内容
        prompt_template = self.prompt_manager.build_prompt("extract_voucher_fields_multimodal")
        
        # 如果有OCR内容，添加到提示中
        if ocr_content.strip():
            prompt_template += f"\n\n参考OCR识别结果（可能包含错误，请以图片内容为准）：\n{ocr_content}"
        
        try:
            # 创建多模态消息
            multimodal_message = self._create_multimodal_message(prompt_template, image_url)
            
            messages = [
                SystemMessage(content="你是一个专业的财务凭证分析助手，擅长从单据图片中直接提取用于生成会计凭证的结构化信息。"),
                multimodal_message
            ]
            
            # 使用非流式方式获取完整响应
            ai_result = await self.llm_manager.generate(messages)
            ai_response = ai_result.generations[0].message.content
            
            logger.info(f"多模态AI凭证字段提取响应: {ai_response}")
            
            # 尝试提取JSON内容
            import re
            json_match = re.search(r'```json\s*\n?([\s\S]*?)```', ai_response, re.IGNORECASE)
            if json_match:
                json_str = json_match.group(1).strip()
                try:
                    result = json.loads(json_str)
                    return result
                except json.JSONDecodeError:
                    logger.warning("多模态JSON格式无效，返回原始响应")
                    return {"raw_response": ai_response}
            else:
                # 尝试直接解析JSON
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1
                
                if json_start != -1 and json_end != -1:
                    json_str = ai_response[json_start:json_end]
                    try:
                        result = json.loads(json_str)
                        return result
                    except json.JSONDecodeError:
                        pass
                
                # 如果无法解析JSON，返回原始响应
                return {"raw_response": ai_response}
                
        except Exception as e:
            logger.error(f"调用多模态AI服务提取凭证字段失败: {e}")
            return {"error": str(e)}

    async def _extract_document_fields(self, content: str) -> Dict[str, Any]:
        """从文档内容中提取结构化信息"""
        logger.info("开始提取文档字段...")
        
        # 构建文档字段提取的提示
        prompt_template = self.prompt_manager.build_prompt("extract_document_fields", content=content)
        
        try:
            # 调用AI服务
            messages = [
                SystemMessage(content="你是一个专业的文档分析助手，擅长从文档中提取结构化信息。"),
                HumanMessage(content=prompt_template)
            ]
            
            # 使用非流式方式获取完整响应
            ai_result = await self.llm_manager.generate(messages)
            ai_response = ai_result.generations[0].message.content
            
            logger.info(f"AI文档字段提取响应: {ai_response}")
            
            # 尝试提取JSON内容
            import re
            json_match = re.search(r'```json\s*\n?([\s\S]*?)```', ai_response, re.IGNORECASE)
            if json_match:
                json_str = json_match.group(1).strip()
                try:
                    result = json.loads(json_str)
                    return result
                except json.JSONDecodeError:
                    logger.warning("JSON格式无效，返回原始响应")
                    return {"raw_response": ai_response}
            else:
                # 尝试直接解析JSON
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1
                
                if json_start != -1 and json_end != -1:
                    json_str = ai_response[json_start:json_end]
                    try:
                        result = json.loads(json_str)
                        return result
                    except json.JSONDecodeError:
                        pass
                
                # 如果无法解析JSON，返回原始响应
                return {"raw_response": ai_response}
                
        except Exception as e:
            logger.error(f"调用AI服务提取文档字段失败: {e}")
            return {"error": str(e)}
    
    async def _extract_document_fields_multimodal(self, image_url: str, ocr_content: str = "") -> Dict[str, Any]:
        """使用多模态大模型从图片中直接提取文档结构化信息"""
        logger.info("使用多模态大模型提取文档字段...")
        
        # 构建多模态文档字段提取的提示，包含OCR参考内容
        prompt_template = self.prompt_manager.build_prompt("extract_document_fields_multimodal")
        
        # 如果有OCR内容，添加到提示中
        if ocr_content.strip():
            prompt_template += f"\n\n参考OCR识别结果（可能包含错误，请以图片内容为准）：\n{ocr_content}"
        
        try:
            # 创建多模态消息
            multimodal_message = self._create_multimodal_message(prompt_template, image_url)
            
            messages = [
                SystemMessage(content="你是一个专业的文档分析助手，擅长从文档图片中直接提取结构化信息。"),
                multimodal_message
            ]
            
            # 使用非流式方式获取完整响应
            ai_result = await self.llm_manager.generate(messages)
            ai_response = ai_result.generations[0].message.content
            
            logger.info(f"多模态AI文档字段提取响应: {ai_response}")
            
            # 尝试提取JSON内容
            import re
            json_match = re.search(r'```json\s*\n?([\s\S]*?)```', ai_response, re.IGNORECASE)
            if json_match:
                json_str = json_match.group(1).strip()
                try:
                    result = json.loads(json_str)
                    return result
                except json.JSONDecodeError:
                    logger.warning("多模态JSON格式无效，返回原始响应")
                    return {"raw_response": ai_response}
            else:
                # 尝试直接解析JSON
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1
                
                if json_start != -1 and json_end != -1:
                    json_str = ai_response[json_start:json_end]
                    try:
                        result = json.loads(json_str)
                        return result
                    except json.JSONDecodeError:
                        pass
                
                # 如果无法解析JSON，返回原始响应
                return {"raw_response": ai_response}
                
        except Exception as e:
            logger.error(f"调用多模态AI服务提取文档字段失败: {e}")
            return {"error": str(e)}

    def _validate_and_fix_fields(self, invoice_fields: Dict[str, Any]) -> Dict[str, Any]:
        """验证和修复发票字段"""
        logger.info("开始验证和修复发票字段...")
        
        import re
        
        # 定义电话号码模式
        phone_patterns = [
            r'1[3-9]\d{9}',      # 中国手机号码模式
            r'\d{3,4}[-\s]?\d{7,8}',  # 固定电话模式
            r'\+?\d{1,3}[-\s]?\d{3,4}[-\s]?\d{4,6}[-\s]?\d{4,6}',  # 国际格式
            r'[\d\s\-\(\)\+]{7,}',  # 基本电话模式
            r'\d{7,}'            # 纯数字（至少7位）
        ]
        
        # 验证和提取购买方和销售方的电话和地址字段
        for party_type in ["购买方信息", "销售方信息"]:
            if party_type not in invoice_fields:
                continue
                
            address = invoice_fields[party_type].get("地址", "无")
            phone = invoice_fields[party_type].get("电话", "无")
            
            # 从地址中提取电话号码
            address_phone = None
            clean_address = address
            
            if address != "无":
                # 策略1：在地址末尾查找电话号码模式
                for pattern in phone_patterns:
                    phone_match = re.search(r'(.+?)\s*(' + pattern + r')\s*$', address)
                    if phone_match:
                        clean_address = phone_match.group(1).strip()
                        extracted_phone = phone_match.group(2).strip()
                        # 清理提取的电话号码
                        extracted_phone = re.sub(r'[<>*]', '', extracted_phone)
                        # 验证提取的电话号码
                        for phone_pattern in phone_patterns:
                            if re.fullmatch(phone_pattern, extracted_phone.replace(' ', '').replace('-', '')):
                                address_phone = extracted_phone
                                break
                        break
            
            # 验证AI返回的电话号码
            phone_valid = False
            final_phone = "无"
            
            if phone != "无":
                # 清理电话号码
                clean_phone = re.sub(r'[<>*]', '', phone)
                for pattern in phone_patterns:
                    if re.fullmatch(pattern, clean_phone.replace(' ', '').replace('-', '')):
                        phone_valid = True
                        final_phone = clean_phone
                        break
            
            # 如果AI返回的电话无效但地址中有电话，使用地址中的电话
            if not phone_valid and address_phone is not None:
                final_phone = address_phone
                invoice_fields[party_type]["地址"] = clean_address
            
            invoice_fields[party_type]["电话"] = final_phone
            
            # 验证银行账号格式
            bank_account = invoice_fields[party_type].get("开户行账号", "无")
            if bank_account != "无":
                # 从组合文本中提取账号
                account_pattern = r'账号[：:]\s*(\d+)'
                account_match = re.search(account_pattern, bank_account)
                if account_match:
                    invoice_fields[party_type]["开户行账号"] = account_match.group(1)
                elif not re.search(r'\d+', bank_account):
                    invoice_fields[party_type]["开户行账号"] = "无"
            
            # 验证银行名称格式
            bank_name = invoice_fields[party_type].get("开户行", "无")
            if bank_name != "无":
                if re.search(r'^\d+$', bank_name):
                    invoice_fields[party_type]["开户行"] = "无"
        
        # 验证发票日期格式
        if "通用字段" in invoice_fields:
            date = invoice_fields["通用字段"].get("开票日期", "无")
            if date != "无":
                date_pattern = r'\d{4}[-\/年]\d{1,2}[-\/月]\d{1,2}[日]?'
                if not re.search(date_pattern, date):
                    invoice_fields["通用字段"]["开票日期"] = "无"
            
            # 验证金额格式
            amount_fields = ["金额", "税额", "价税合计"]
            for field in amount_fields:
                value = invoice_fields["通用字段"].get(field, "无")
                if value != "无":
                    if not re.search(r'[\d\.]+', value):
                        invoice_fields["通用字段"][field] = "无"
        
        # 验证数量字段
        if "商品信息" in invoice_fields:
            quantity = invoice_fields["商品信息"].get("数量", "无")
            if quantity != "无":
                quantity_match = re.search(r'(\d+(?:\.\d+)?)', quantity)
                if quantity_match:
                    invoice_fields["商品信息"]["数量"] = quantity_match.group(1)
                else:
                    invoice_fields["商品信息"]["数量"] = "无"
        
        logger.info("发票字段验证和修复完成")
        return invoice_fields

    def _get_empty_invoice_result(self) -> Dict[str, Any]:
        """返回空的发票字段结构"""
        empty_result = {
            "通用字段": {
                "发票号码": "无",
                "发票代码": "无",
                "开票日期": "无",
                "金额": "无",
                "税额": "无",
                "价税合计": "无"
            },
            "购买方信息": {
                "名称": "无",
                "纳税人识别号": "无",
                "地址": "无",
                "电话": "无",
                "开户行": "无",
                "开户行账号": "无"
            },
            "销售方信息": {
                "名称": "无",
                "纳税人识别号": "无",
                "地址": "无",
                "电话": "无",
                "开户行": "无",
                "开户行账号": "无"
            },
            "商品信息": {
                "商品名称": "无",
                "规格型号": "无",
                "数量": "无",
                "单价": "无",
                "税率": "无"
            },
            "其他信息": {
                "收款人": "无",
                "复核": "无",
                "开票人": "无"
            }
        }
        return self._validate_and_fix_fields(empty_result)

    async def _gather_context_data(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """异步收集上下文数据"""
        logger.info(f"Entering _gather_context_data with user_id={user_id}")
        db = AsyncSessionLocal(bind=engine)
        try:
            subjects_result = await db.execute(select(SubjectAccount))
            assets_result = await db.execute(select(Asset))
            staffs_result = await db.execute(select(Staff))
            # NEW: Collect company information
            company_result = await db.execute(select(Company))
            company = company_result.scalars().first()  # Assuming single company for now
            
            subjects = [
                {c.name: getattr(s, c.name) for c in s.__table__.columns}
                for s in subjects_result.scalars().all()
            ]
            assets = [
                {c.name: getattr(a, c.name) for c in a.__table__.columns}
                for a in assets_result.scalars().all()
            ]
            staff = [
                {c.name: getattr(st, c.name) for c in st.__table__.columns}
                for st in staffs_result.scalars().all()
            ]
        except asyncio.CancelledError:
            logger.warning("Database operation was cancelled, gracefully closing connection")
            try:
                await db.close()
            except Exception as e:
                logger.error(f"Error closing database connection during cancellation: {e}")
            raise
        except Exception as e:
            logger.error(f"Error gathering context data: {e}")
            try:
                await db.close()
            except Exception as close_error:
                logger.error(f"Error closing database connection: {close_error}")
            raise
        else:
            await db.close()
        from api.experience import get_experience
        experience = get_experience(user_id, limit=5) if user_id else []
        return {
            "subjects": subjects,
            "assets": assets,
            "staff": staff,
            "experience": experience,
            "company": {  # NEW: Add company data
                "name": company.name if company else "",
                "business_scope": company.business_scope if company else "",
                "industry": company.industry if company else "",
                "accounting_standards": company.accounting_standards if company else "",
                "tax_info": company.tax_id if company else ""
            } if company else None
        }
    
    def __del__(self):
        """析构函数，清理上传的图片"""
        try:
            self._cleanup_uploaded_images()
        except Exception as e:
            logger.error(f"析构函数清理图片失败: {e}")


# 全局智能体执行器实例字典
_agent_executors: Dict[str, AgentExecutor] = {}

def get_agent_executor(session_id: Optional[str] = None) -> AgentExecutor:
    """获取智能体执行器实例"""
    logger.info(f"Entering get_agent_executor with session_id={session_id}")
    global _agent_executors
    
    if not session_id:
        session_id = str(uuid.uuid4())
    
    if session_id not in _agent_executors:
        _agent_executors[session_id] = AgentExecutor(session_id)
    
    return _agent_executors[session_id]