"""
凭证生成模块 - 处理凭证生成和验证相关的方法
"""

import logging
from typing import Dict, Any, List, Optional
import json
import asyncio
from datetime import datetime

from ..monitoring import monitor_performance

logger = logging.getLogger(__name__)

class VoucherGenerator:
    """凭证生成器"""
    
    def __init__(self, agent_executor):
        self.agent = agent_executor
    
    @monitor_performance("process_message")
    async def process_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """处理用户消息"""
        logger.info(f"Entering process_message with message={message}, user_id={user_id}")
        if not self.agent.llm_manager.is_configured():
            return {"success": False, "error": "LLM未配置"}
        try:
            # 处理文件内容
            structured_content = ""
            if files and len(files) > 0:
                # 通过AI服务处理每个文件并提取结构化内容
                structured_content = await self.agent._process_files_with_ai(files, "voucher")
                # 将结构化内容添加到用户消息中
                if structured_content:
                    enhanced_message = f"{message}\n\n结构化信息：\n{structured_content}" if message.strip() else f"请分析以下文件内容：\n{structured_content}"
                else:
                    enhanced_message = message
            else:
                enhanced_message = message
            
            self.agent.memory.add_user_message(enhanced_message)
            
            # 收集上下文数据
            context_data = await self.agent._gather_context_data(user_id)
            
            # subjects 压缩为 {'借': [...], '贷': [...]} 格式
            subjects_raw = context_data.get("subjects", [])
            subjects_compact = [subj.get('科目名称') or subj.get('name') for subj in subjects_raw]
            
            # Process the entire chain with retry mechanism
            voucher_result = await self._process_chain_with_retry(
                enhanced_message, context_data, user_id
            )
            
            if not voucher_result["success"]:
                return {"success": False, "error": voucher_result["error"]}
            
            data = voucher_result["data"]
            
            self.agent.memory.add_ai_message(json.dumps(data, ensure_ascii=False))
            return {
                "success": True,
                "data": data,
                "session_id": self.agent.session_id
            }
        except asyncio.CancelledError:
            logger.warning("Process message was cancelled by client")
            return {"success": False, "error": "请求被用户取消"}
        except Exception as e:
            logger.error(f"处理消息失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    @monitor_performance("stream_message")
    async def stream_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None):
        """流式处理用户消息"""
        logger.info(f"Entering stream_message with message={message}, user_id={user_id}")
        if not self.agent.llm_manager.is_configured():
            yield json.dumps({"success": False, "error": "LLM未配置"})
            return
        try:
            # 处理文件内容
            structured_content = ""
            if files and len(files) > 0:
                # 通过AI服务处理每个文件并提取结构化内容
                structured_content = await self.agent._process_files_with_ai(files, "voucher")
                # 将结构化内容添加到用户消息中
                if structured_content:
                    enhanced_message = f"{message}\n\n结构化信息：\n{structured_content}" if message.strip() else f"请分析以下文件内容：\n{structured_content}"
                else:
                    enhanced_message = message
            else:
                enhanced_message = message

            logger.info(f"enhanced_message : {enhanced_message}")
            
            self.agent.memory.add_user_message(enhanced_message)
            
            # 收集上下文数据
            context_data = await self.agent._gather_context_data(user_id)
            
            # subjects 压缩为 {'借': [...], '贷': [...]} 格式
            subjects_raw = context_data.get("subjects", [])
            subjects_compact = [subj.get('科目名称') or subj.get('name') for subj in subjects_raw]
            
            # Step 1: Analyze the transaction
            context_company = context_data.get("company", {})
            transaction_inputs = {
                "user_input": enhanced_message,
                "current_date": datetime.now().strftime("%Y-%m-%d"),
                "company_name": context_company.get("name", ""),
                "business_scope": context_company.get("business_scope", ""),
                "industry": context_company.get("industry", "")
            }
            logger.info(f"[CHAIN_REQUEST] transaction_analysis 链式调用输入: {json.dumps(transaction_inputs, ensure_ascii=False)}")
            transaction_result = await self.agent.chain_manager.run_chain("transaction_analysis", transaction_inputs)
            if not transaction_result.success:
                yield json.dumps({"success": False, "error": transaction_result.error})
                return
            transaction_data = transaction_result.data
            if isinstance(transaction_data, str):
                try:
                    transaction_data = json.loads(transaction_data)
                except Exception:
                    transaction_data = {}

            logger.info("[DEBUG] Transaction data: %s", transaction_data)
            
            # Step 2: Match subjects
            # Prepare subjects for matching (simplified approach)
            subjects_compact = [subj.get('科目名称') or subj.get('name') for subj in subjects_raw]
            
            subject_inputs = {
                "transaction_type": transaction_data.get("transaction_type", ""),
                "content": transaction_data.get("content", ""),
                "context": transaction_data.get("context", ""),
                "amount": transaction_data.get("primary_amount", 0),
                "subjects": subjects_compact,
                "current_date": datetime.now().strftime("%Y-%m-%d"),
                "user_id": user_id,
                "company_name": context_company.get("name", ""),
                "business_scope": context_company.get("business_scope", ""),
                "industry": context_company.get("industry", ""),
                "accounting_standards": context_company.get("accounting_standards", "")
            }
            logger.info(f"[CHAIN_REQUEST] subject_matching 链式调用输入: {json.dumps(subject_inputs, ensure_ascii=False)}")
            subject_result = await self.agent.chain_manager.run_chain("subject_matching", subject_inputs)
            if not subject_result.success:
                yield json.dumps({"success": False, "error": subject_result.error})
                return
            subject_data = subject_result.data
            if isinstance(subject_data, str):
                try:
                    subject_data = json.loads(subject_data)
                except Exception:
                    subject_data = {}
            
            # 转换 subject_data 格式，将 matched_entries 分为 debit_subjects 和 credit_subjects
            if "matched_entries" in subject_data:
                debit_subjects = []
                credit_subjects = []
                for entry in subject_data["matched_entries"]:
                    # 确保 entry 是字典类型
                    if isinstance(entry, dict):
                        if entry.get("direction") == "debit":
                            debit_subjects.append(entry)
                        elif entry.get("direction") == "credit":
                            credit_subjects.append(entry)
                # 更新 subject_data 以匹配后续处理的期望格式
                subject_data["debit_subjects"] = debit_subjects
                subject_data["credit_subjects"] = credit_subjects
            
            logger.info("[DEBUG] Subject data: %s", subject_data)

            # Process the entire chain with retry mechanism
            voucher_result = await self._process_chain_with_retry(
                enhanced_message, context_data, user_id
            )
            
            if not voucher_result["success"]:
                yield json.dumps({"success": False, "error": voucher_result["error"]})
                return
            
            data = voucher_result["data"]
            self.agent.memory.add_ai_message(json.dumps(data, ensure_ascii=False))
            
            # 确保响应包含前端期望的字段
            if isinstance(data, dict):
                # 如果data已经是字典，确保包含必要字段
                formatted_data = {
                    "success": True,
                    "action": data.get("action", "none"),
                    "answer": data.get("answer", ""),
                    "analysis": data.get("analysis", ""),
                    "cards": data.get("cards", []),
                    "is_finished": data.get("is_finished", True),
                    **data  # 保留其他字段
                }
            else:
                # 如果data不是字典，包装成前端期望的格式
                formatted_data = {
                    "success": True,
                    "action": "none",
                    "answer": str(data),
                    "analysis": "",
                    "cards": [],
                    "is_finished": True
                }
            
            yield json.dumps(formatted_data, ensure_ascii=False)
            return

        except asyncio.CancelledError:
            logger.warning("Stream message was cancelled by client")
            yield json.dumps({"success": False, "error": "请求被用户取消"})
            return
        except Exception as e:
            logger.error(f"流式处理消息失败: {str(e)}")
            error_msg = json.dumps({"success": False, "error": str(e)})
            yield error_msg
    
    async def _process_voucher_with_retry(self, transaction_data: Dict[str, Any], subject_data: Dict[str, Any],
                                           amount_data: Dict[str, Any], context_data: Dict[str, Any],
                                           user_id: Optional[str] = None) -> Dict[str, Any]:
        """处理凭证生成并自动重试以纠正不平衡"""
        logger.info("开始处理凭证生成并自动重试以纠正不平衡")
        
        # 初始化重试参数
        max_attempts = 3
        attempt_count = 0
        adjustment_suggestions = None
        previous_attempts = []
        
        context_company = context_data.get("company", {})
        
        while attempt_count < max_attempts:
            attempt_count += 1
            logger.info(f"凭证生成尝试 {attempt_count}/{max_attempts}")
            
            # Step 1: Generate voucher with adjustment information if available
            voucher_inputs = {
                "date": transaction_data.get("date", datetime.now().strftime("%Y-%m-%d")),
                "summary": transaction_data.get("summary", ""),
                "debit_subjects": subject_data.get("debit_subjects", []),
                "credit_subjects": subject_data.get("credit_subjects", []),
                "amount_breakdown": amount_data.get("amount_breakdown", {}),
                "tax_details": amount_data.get("tax_details", {}),
                "other_fees": amount_data.get("other_fees", {}),
                "retry_count": attempt_count - 1,  # 0 for first attempt
                "adjustment_suggestions": adjustment_suggestions,
                "previous_attempts": previous_attempts
            }
            
            logger.info(f"[CHAIN_REQUEST] voucher_generation_improved 链式调用输入: {json.dumps(voucher_inputs, ensure_ascii=False)}")
            voucher_result = await self.agent.chain_manager.run_chain("voucher_generation_improved", voucher_inputs)
            if not voucher_result.success:
                return {"success": False, "error": voucher_result.error}
            voucher_data = voucher_result.data
            if isinstance(voucher_data, str):
                try:
                    voucher_data = json.loads(voucher_data)
                except Exception:
                    voucher_data = {}
            
            logger.info(f"Voucher data: {voucher_data}")

            # Step 2: Mathematically verify balance
            voucher_content = voucher_data.get("voucher", {})
            balance_check_result = await self.agent.execute_tool(
                "balance_checker",
                debit_entries=voucher_content.get("debit_entries", []),
                credit_entries=voucher_content.get("credit_entries", [])
            )
            if not balance_check_result["success"]:
                return {"success": False, "error": balance_check_result["error"]}
            balance_check_data = balance_check_result["data"]

            logger.info(f"[DEBUG] Balance check data: {balance_check_data}")

            # Step 3: Verify balance with LLM
            balance_inputs = {
                "debit_entries": voucher_content.get("debit_entries", []),
                "credit_entries": voucher_content.get("credit_entries", [])
            }
            logger.info(f"[CHAIN_REQUEST] balance_verification 链式调用输入: {json.dumps(balance_inputs, ensure_ascii=False)}")
            balance_result = await self.agent.chain_manager.run_chain("balance_verification", balance_inputs)
            if not balance_result.success:
                return {"success": False, "error": balance_result.error}
            balance_data = balance_result.data
            if isinstance(balance_data, str):
                try:
                    balance_data = json.loads(balance_data)
                except Exception:
                    balance_data = {}

            # Combine balance verification results
            combined_balance_data = {
                **balance_data,
                "mathematical_check": balance_check_data
            }

            logger.info(f"[DEBUG] Combined balance data: {combined_balance_data}")
            
            # Check if balanced
            is_balanced = (balance_data.get("is_balanced", False) and
                          balance_check_data.get("is_balanced", False))
            
            # Store attempt information
            attempt_info = {
                "attempt": attempt_count,
                "voucher_data": voucher_data,
                "balance_verification": combined_balance_data,
                "is_balanced": is_balanced
            }
            previous_attempts.append(attempt_info)
            
            # If balanced or max attempts reached, break
            if is_balanced or attempt_count >= max_attempts:
                break
            
            # Prepare adjustment suggestions for next attempt
            adjustment_suggestions = balance_data.get("adjustment_suggestions", [])
            logger.info(f"凭证不平衡，调整建议: {adjustment_suggestions}")
        
        # Combine all data into the final response
        final_voucher_data = previous_attempts[-1]["voucher_data"]
        final_balance_data = previous_attempts[-1]["balance_verification"]
        
        data = {
            "analysis": "凭证已生成",
            "action": "create_card",
            "is_finished": True,
            "cards": [{
                "type": "voucher",
                "op": "create",
                "data": final_voucher_data.get("voucher", {})
            }],
            "validation": final_balance_data,
            "retry_info": {
                "attempts": attempt_count,
                "max_attempts": max_attempts,
                "all_attempts": previous_attempts
            }
        }
        
        return {
            "success": True,
            "data": data
        }
    
    async def _process_chain_with_retry(self, enhanced_message: str, context_data: Dict[str, Any],
                                         user_id: Optional[str] = None) -> Dict[str, Any]:
        """处理整个链式调用过程并自动重试以纠正不平衡"""
        logger.info("开始处理整个链式调用过程并自动重试以纠正不平衡")
        
        # 初始化重试参数
        max_attempts = 3
        attempt_count = 0
        adjustment_suggestions = None
        previous_attempts = []
        subjects_raw = context_data.get("subjects", [])
        context_company = context_data.get("company", {})
        
        while attempt_count < max_attempts:
            attempt_count += 1
            logger.info(f"链式调用尝试 {attempt_count}/{max_attempts}")
            
            # Step 1: Analyze the transaction with adjustment information if available
            transaction_inputs = {
                "user_input": enhanced_message,
                "current_date": datetime.now().strftime("%Y-%m-%d"),
                "company_name": context_company.get("name", ""),
                "business_scope": context_company.get("business_scope", ""),
                "industry": context_company.get("industry", ""),
                "current_attempt": attempt_count,
                "max_attempts": max_attempts,
                "adjustment_suggestions": adjustment_suggestions if adjustment_suggestions else ""
            }
            logger.info(f"[CHAIN_REQUEST] transaction_analysis 链式调用输入: {json.dumps(transaction_inputs, ensure_ascii=False)}")
            transaction_result = await self.agent.chain_manager.run_chain("transaction_analysis", transaction_inputs)
            if not transaction_result.success:
                return {"success": False, "error": transaction_result.error}
            transaction_data = transaction_result.data
            if isinstance(transaction_data, str):
                try:
                    transaction_data = json.loads(transaction_data)
                except Exception:
                    transaction_data = {}
            
            logger.info("[DEBUG] Transaction data: %s", transaction_data)

            # Step 2: Match subjects with adjustment information if available
            subjects_compact = [subj.get('科目名称') or subj.get('name') for subj in subjects_raw]
            subject_inputs = {
                "transaction_type": transaction_data.get("transaction_type", ""),
                "content": transaction_data.get("content", ""),
                "context": transaction_data.get("context", ""),
                "amount": transaction_data.get("primary_amount", 0),
                "subjects": subjects_compact,
                "current_date": datetime.now().strftime("%Y-%m-%d"),
                "user_id": user_id,
                "company_name": context_company.get("name", ""),
                "business_scope": context_company.get("business_scope", ""),
                "industry": context_company.get("industry", ""),
                "accounting_standards": context_company.get("accounting_standards", ""),
                "current_attempt": attempt_count,
                "max_attempts": max_attempts,
                "adjustment_suggestions": adjustment_suggestions if adjustment_suggestions else ""
            }
            logger.info(f"[CHAIN_REQUEST] subject_matching 链式调用输入: {json.dumps(subject_inputs, ensure_ascii=False)}")
            subject_result = await self.agent.chain_manager.run_chain("subject_matching", subject_inputs)
            if not subject_result.success:
                return {"success": False, "error": subject_result.error}
            subject_data = subject_result.data
            if isinstance(subject_data, str):
                try:
                    subject_data = json.loads(subject_data)
                except Exception:
                    subject_data = {}
            
            # 转换 subject_data 格式，将 matched_entries 分为 debit_subjects 和 credit_subjects
            if "matched_entries" in subject_data:
                debit_subjects = []
                credit_subjects = []
                for entry in subject_data["matched_entries"]:
                    # 确保 entry 是字典类型
                    if isinstance(entry, dict):
                        if entry.get("direction") == "debit":
                            debit_subjects.append(entry)
                        elif entry.get("direction") == "credit":
                            credit_subjects.append(entry)
                # 更新 subject_data 以匹配后续处理的期望格式
                subject_data["debit_subjects"] = debit_subjects
                subject_data["credit_subjects"] = credit_subjects
            
            logger.info("[DEBUG] Subject data: %s", subject_data)

            # Step 3: Calculate amounts with adjustment information if available
            amount_inputs = {
                "transaction_type": transaction_data.get("transaction_type", ""),
                "content": transaction_data.get("content", ""),
                "context": transaction_data.get("context", ""),
                "primary_amount": transaction_data.get("primary_amount", 0),
                "secondary_amounts": transaction_data.get("secondary_amounts", []),
                "current_date": datetime.now().strftime("%Y-%m-%d"),
                "user_id": user_id,
                "company_name": context_company.get("name", ""),
                "industry": context_company.get("industry", ""),
                "tax_info": context_company.get("tax_info", ""),
                "current_attempt": attempt_count,
                "max_attempts": max_attempts,
                "adjustment_suggestions": adjustment_suggestions if adjustment_suggestions else ""
            }
            logger.info(f"[CHAIN_REQUEST] amount_calculation 链式调用输入: {json.dumps(amount_inputs, ensure_ascii=False)}")
            amount_result = await self.agent.chain_manager.run_chain("amount_calculation", amount_inputs)
            if not amount_result.success:
                return {"success": False, "error": amount_result.error}
            amount_data = amount_result.data
            if isinstance(amount_data, str):
                try:
                    amount_data = json.loads(amount_data)
                except Exception:
                    amount_data = {}

            logger.info("[DEBUG] Amount data: %s", amount_data)
            
            # Step 4: Generate voucher with adjustment information if available
            voucher_inputs = {
                "date": transaction_data.get("date", datetime.now().strftime("%Y-%m-%d")),
                "summary": transaction_data.get("summary", ""),
                "debit_subjects": subject_data.get("debit_subjects", []),
                "credit_subjects": subject_data.get("credit_subjects", []),
                "amount_breakdown": amount_data.get("amount_breakdown", {}),
                "tax_details": amount_data.get("tax_details", {}),
                "other_fees": amount_data.get("other_fees", {}),
                "retry_count": attempt_count - 1,  # 0 for first attempt
                "adjustment_suggestions": adjustment_suggestions if adjustment_suggestions else "",
                "previous_attempts": json.dumps(previous_attempts, ensure_ascii=False)
            }
            
            logger.info(f"[CHAIN_REQUEST] voucher_generation_improved 链式调用输入: {json.dumps(voucher_inputs, ensure_ascii=False)}")
            voucher_result = await self.agent.chain_manager.run_chain("voucher_generation_improved", voucher_inputs)
            if not voucher_result.success:
                return {"success": False, "error": voucher_result.error}
            voucher_data = voucher_result.data
            if isinstance(voucher_data, str):
                try:
                    voucher_data = json.loads(voucher_data)
                except Exception:
                    voucher_data = {}
            
            logger.info(f"Voucher data: {voucher_data}")

            # Step 5: Mathematically verify balance
            voucher_content = voucher_data.get("voucher", {})
            balance_check_result = await self.agent.execute_tool(
                "balance_checker",
                debit_entries=voucher_content.get("debit_entries", []),
                credit_entries=voucher_content.get("credit_entries", [])
            )
            if not balance_check_result["success"]:
                return {"success": False, "error": balance_check_result["error"]}
            balance_check_data = balance_check_result["data"]

            logger.info(f"[DEBUG] Balance check data: {balance_check_data}")

            # Step 6: Verify balance with LLM
            balance_inputs = {
                "debit_entries": voucher_content.get("debit_entries", []),
                "credit_entries": voucher_content.get("credit_entries", [])
            }
            logger.info(f"[CHAIN_REQUEST] balance_verification 链式调用输入: {json.dumps(balance_inputs, ensure_ascii=False)}")
            balance_result = await self.agent.chain_manager.run_chain("balance_verification", balance_inputs)
            if not balance_result.success:
                return {"success": False, "error": balance_result.error}
            balance_data = balance_result.data
            if isinstance(balance_data, str):
                try:
                    balance_data = json.loads(balance_data)
                except Exception:
                    balance_data = {}

            # Combine balance verification results
            combined_balance_data = {
                **balance_data,
                "mathematical_check": balance_check_data
            }

            logger.info(f"[DEBUG] Combined balance data: {combined_balance_data}")
            
            # Check if balanced
            is_balanced = (balance_data.get("is_balanced", False) and
                          balance_check_data.get("is_balanced", False))
            
            # Store attempt information
            attempt_info = {
                "attempt": attempt_count,
                "transaction_data": transaction_data,
                "subject_data": subject_data,
                "amount_data": amount_data,
                "voucher_data": voucher_data,
                "balance_verification": combined_balance_data,
                "is_balanced": is_balanced
            }
            previous_attempts.append(attempt_info)
            
            # If balanced or max attempts reached, break
            if is_balanced or attempt_count >= max_attempts:
                break
            
            # Prepare adjustment suggestions for next attempt
            # Extract suggestion text from adjustment suggestions
            adjustment_list = balance_data.get("adjustment_suggestions", [])
            if adjustment_list and isinstance(adjustment_list, list):
                adjustment_suggestions = "\n".join([
                    item.get("suggestion", "") if isinstance(item, dict) else str(item)
                    for item in adjustment_list
                ])
            else:
                adjustment_suggestions = ""
            logger.info(f"凭证不平衡，调整建议: {adjustment_suggestions}")
        
        # Combine all data into the final response
        final_attempt = previous_attempts[-1]
        final_voucher_data = final_attempt["voucher_data"]
        final_balance_data = final_attempt["balance_verification"]
        
        data = {
            "analysis": "凭证已生成",
            "action": "create_card",
            "is_finished": True,
            "cards": [{
                "type": "voucher",
                "op": "create",
                "data": final_voucher_data.get("voucher", {})
            }],
            "validation": final_balance_data,
            "retry_info": {
                "attempts": attempt_count,
                "max_attempts": max_attempts,
                "all_attempts": previous_attempts
            }
        }
        
        return {
            "success": True,
            "data": data
        }    
        
