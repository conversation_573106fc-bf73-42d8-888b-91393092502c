"""
智能体执行器 - 协调各个组件，执行智能体任务
"""

import logging
from typing import Dict, Any, List, Optional, AsyncGenerator

from .modules.agent_base import AgentExecutor as BaseAgentExecutor
from .modules.voucher_generator import VoucherGenerator
from .modules.document_auditor import DocumentAuditor
from .modules.document_processor import DocumentProcessor
from .modules.regulation_parser import RegulationParser, parse_audit_regulations, save_parsed_regulations
from .modules.financial_consultant import FinancialConsultant

logger = logging.getLogger(__name__)

# 全局 LLM 配置存储，确保所有 AgentExecutor 实例共享相同的 LLM 配置
_global_llm_config = {
    'api_key': None,
    'base_url': None,
    'model': None,
    'use_ollama': False,
    'supports_vision': False,
    'configured': False
}

def set_global_llm_config(api_key: str, base_url: str, model: str, use_ollama: bool = False, supports_vision: bool = False, **kwargs):
    """设置全局 LLM 配置"""
    global _global_llm_config
    _global_llm_config.update({
        'api_key': api_key,
        'base_url': base_url,
        'model': model,
        'use_ollama': use_ollama,
        'supports_vision': supports_vision,
        'configured': True
    })
    logger.info(f"[GLOBAL_CONFIG] 全局LLM配置已更新: model={model}, supports_vision={supports_vision}")

def get_global_llm_config() -> Dict[str, Any]:
    """获取全局 LLM 配置"""
    global _global_llm_config
    return _global_llm_config.copy()

def is_global_llm_configured() -> bool:
    """检查全局 LLM 是否已配置"""
    global _global_llm_config
    return _global_llm_config['configured']

class AgentExecutor(BaseAgentExecutor):
    """智能体执行器 - 扩展基础类，添加各个功能模块"""
    
    def __init__(self, session_id: Optional[str] = None):
        super().__init__(session_id)
        
        # 如果全局 LLM 已配置但当前实例未配置，则应用全局配置
        if is_global_llm_configured() and not self.llm_manager.is_configured():
            global_config = get_global_llm_config()
            logger.info(f"[GLOBAL_CONFIG] 应用全局LLM配置到新实例: session_id={session_id}")
            self.configure_llm(
                api_key=global_config['api_key'],
                base_url=global_config['base_url'],
                model=global_config['model'],
                use_ollama=global_config['use_ollama'],
                supports_vision=global_config['supports_vision']
            )
        
        # 初始化各个功能模块
        self.voucher_generator = VoucherGenerator(self)
        self.document_auditor = DocumentAuditor(self)
        self.document_processor = DocumentProcessor(self)
        self.regulation_parser = RegulationParser(self)
        self.financial_consultant = FinancialConsultant(self)
    
    def configure_llm(self, api_key: str, base_url: str, model: str, use_ollama: bool = False, supports_vision: bool = False, **kwargs) -> bool:
        """配置语言模型，并更新全局配置"""
        logger.info(f"[GLOBAL_CONFIG] AgentExecutor.configure_llm 被调用，更新全局配置")
        
        # 调用父类的配置方法
        success = super().configure_llm(api_key, base_url, model, use_ollama=use_ollama, supports_vision=supports_vision, **kwargs)
        
        # 如果配置成功，更新全局配置
        if success:
            set_global_llm_config(api_key, base_url, model, use_ollama=use_ollama, supports_vision=supports_vision, **kwargs)
        
        return success
    
    # 凭证生成相关方法
    async def process_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """处理用户消息"""
        return await self.voucher_generator.process_message(message, user_id, files)
    
    async def stream_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """流式处理用户消息"""
        async for token in self.voucher_generator.stream_message(message, user_id, files):
            yield token
    
    # 文档处理相关方法
    async def process_document(self, document_type: str, document_content: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """处理文档"""
        return await self.document_processor.process_document(document_type, document_content, user_id)
    
    async def stream_document(self, document_type: str, document_content: str, user_id: Optional[str] = None) -> AsyncGenerator[str, None]:
        """流式处理文档"""
        async for token in self.document_processor.stream_document(document_type, document_content, user_id):
            yield token
    
    # 单据审核相关方法
    async def stream_audit_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """流式处理单据审核消息"""
        async for token in self.document_auditor.stream_audit_message(message, user_id, files):
            yield token
    
    async def _search_audit_rag(self, query: str) -> List[Dict]:
        """搜索单据审核相关的规章制度"""
        return await self.document_auditor._search_audit_rag(query)
    
    async def add_audit_rag_data(self, data: List[Dict]) -> Dict:
        """添加单据审核相关的RAG数据"""
        return await self.document_auditor.add_audit_rag_data(data)
    
    async def list_audit_rag_data(self) -> Dict:
        """列出单据审核相关的RAG数据"""
        return await self.document_auditor.list_audit_rag_data()
    
    async def delete_audit_rag_data(self, item_id: str) -> Dict:
        """删除单据审核相关的RAG数据"""
        return await self.document_auditor.delete_audit_rag_data(item_id)
    
    async def update_audit_rag_data(self, item_id: str, data: Dict) -> Dict:
        """更新单据审核相关的RAG数据"""
        return await self.document_auditor.update_audit_rag_data(item_id, data)
    
    async def _parse_audit_response(self, response_text: str) -> Dict[str, Any]:
        """解析AI审核响应，提取结构化信息"""
        return await self.document_auditor._parse_audit_response(response_text)
    
    # 财务咨询相关方法
    async def stream_consultation_message(self, message: str, user_id: Optional[str] = None, files: Optional[List[Dict]] = None) -> AsyncGenerator[str, None]:
        """流式处理财务咨询消息"""
        async for token in self.financial_consultant.stream_consultation_message(message, user_id, files):
            yield token
    
    async def _parse_consultation_response(self, response_text: str) -> Dict[str, Any]:
        """解析AI财务咨询响应，提取结构化信息"""
        return await self.financial_consultant._parse_consultation_response(response_text)
    
    # 规章制度解析相关方法
    async def parse_audit_regulations(self, document_content: str) -> Dict[str, Any]:
        """解析规章制度文档"""
        return await self.regulation_parser.parse_audit_regulations(document_content)
    
    async def save_parsed_regulations(self, parsed_data: Dict[str, Any], original_filename: str = "") -> Dict[str, Any]:
        """保存解析后的规章制度数据"""
        return await self.regulation_parser.save_parsed_regulations(parsed_data, original_filename)


# 更新全局智能体执行器实例字典，使用新的AgentExecutor类
_agent_executors = {}

def get_agent_executor(session_id: Optional[str] = None) -> AgentExecutor:
    """获取智能体执行器实例"""
    logger.info(f"Entering get_agent_executor with session_id={session_id}")
    global _agent_executors
    
    if not session_id:
        import uuid
        session_id = str(uuid.uuid4())
    
    if session_id not in _agent_executors:
        _agent_executors[session_id] = AgentExecutor(session_id)
    
    return _agent_executors[session_id]
