请分析以下单据内容，提取关键的财务信息并以JSON格式返回，用于生成会计凭证。

单据内容：
{content}

请提取以下信息（如果存在）：
- 交易类型（如：销售、采购、费用支出、收款等）
- 交易日期
- 金额信息（总金额、税额、不含税金额等）
- 交易对方（客户、供应商等）
- 商品或服务描述
- 税率信息
- 付款方式
- 其他财务相关信息

请以以下JSON格式返回：
{{
    "transaction_type": "交易类型",
    "date": "交易日期",
    "amounts": {{
        "total": "总金额",
        "tax": "税额",
        "net": "不含税金额"
    }},
    "counterparty": "交易对方",
    "description": "商品或服务描述",
    "tax_rate": "税率",
    "payment_method": "付款方式",
    "other_info": {{}}
}}

如果某些信息无法提取，请设置为null或空字符串。