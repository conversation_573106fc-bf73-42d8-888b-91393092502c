你是一个专业的财务规章制度解析专家，擅长从文档中提取和分类各种报销规定，并能准确判断每条规则是否需要微工作流的支持。

请仔细分析以下规章制度文档内容，提取其中的报销规定，并按照报销类型进行分类整理。对于每一条规则，需要判断是否需要微工作流的支持，如果需要，则返回需要的微工作流信息。

文档内容：
---
{document_content}
---

请按照以下JSON格式返回解析结果：
```json
{{
  "regulation_categories": [
    {{
      "type": "差旅费",
      "description": "与员工出差相关的费用报销规定",
      "rules": [
        {{
          "rule_content": "交通标准：飞机经济舱、高铁二等座、普通列车硬卧",
          "workflows": []
        }},
        {{
          "rule_content": "住宿标准：一线城市不超过600元/天，二线城市不超过500元/天，三线城市不超过400元/天",
          "workflows": []
        }},
        {{
          "rule_content": "伙食补贴：100元/天",
          "workflows": []
        }},
        {{
          "rule_content": "交通补贴：50元/天",
          "workflows": []
        }}
      ]
    }},
    {{
      "type": "办公用品",
      "description": "办公用品采购和报销相关规定",
      "rules": [
        {{
          "rule_content": "单价超过1000元的需部门经理审批",
          "workflows": []
        }},
        {{
          "rule_content": "超过5000元的需分管副总审批",
          "workflows": []
        }},
        {{
          "rule_content": "采购后需在3个工作日内完成报销",
          "workflows": []
        }},
        {{
          "rule_content": "需提供正规发票和采购清单",
          "workflows": []
        }}
      ]
    }},
    {{
      "type": "业务招待费",
      "description": "业务招待相关费用报销规定",
      "rules": [
        {{
          "rule_content": "需提前申请，注明招待对象、人数、预算标准",
          "workflows": []
        }},
        {{
          "rule_content": "一般客户招待标准不超过200元/人",
          "workflows": []
        }},
        {{
          "rule_content": "重要客户不超过500元/人",
          "workflows": []
        }},
        {{
          "rule_content": "需提供招待对象名单、消费清单和发票",
          "workflows": []
        }}
      ]
    }},
    {{
      "type": "部门报销限制",
      "description": "部门年度报销限制相关规定",
      "rules": [
        {{
          "rule_content": "报销金额不能超过本部门年度报销限制",
          "workflows": [
            {{
              "workflow_name": "get_employee_department",
              "description": "获取员工所属部门信息",
              "inputs": [{{"name": "employee_id", "type": "string", "description": "员工ID"}}],
              "outputs": [{{"name": "department", "type": "string", "description": "部门名称"}}]
            }},
            {{
              "workflow_name": "get_department_expenses",
              "description": "获取部门年度报销总金额",
              "inputs": [{{"name": "department", "type": "string", "description": "部门名称"}}, {{"name": "year", "type": "number", "description": "年份"}}],
              "outputs": [{{"name": "total_amount", "type": "number", "description": "总报销金额"}}]
            }},
            {{
              "workflow_name": "get_department_budget",
              "description": "获取部门年度报销额度",
              "inputs": [{{"name": "department", "type": "string", "description": "部门名称"}}, {{"name": "year", "type": "number", "description": "年份"}}],
              "outputs": [{{"name": "budget", "type": "number", "description": "报销额度"}}]
            }}
          ]
        }}
      ]
    }}
  ]
}}
```

要求：
1. 仔细阅读文档内容，识别所有报销类型
2. 为每种类型创建一个分类对象
3. 提取该类型下的具体规定作为rules数组，每条规则现在是一个对象，包含rule_content和workflows字段
4. 对于每一条规则，判断是否需要微工作流的支持：
   a. 如果规则执行时需要获取额外信息（如员工部门、报销金额、部门预算等），则需要添加相应的微工作流
   b. 微工作流应包含workflow_name、description、inputs和outputs字段
   c. inputs和outputs都是数组，包含参数名称、类型和描述
   d. 如果规则不需要微工作流支持，workflows字段应为空数组
5. 举例说明：规则"报销金额不能超过本部门年度报销限制"需要三个微工作流：
   - 获取员工所属部门信息
   - 获取部门年度报销总金额
   - 获取部门年度报销额度
6. 确保返回的是有效的JSON格式
7. 规则内容应该是清晰、完整的句子
8. 如果文档中没有提到某种类型，请勿创建该类型
9. 不要在JSON格式前后添加任何其他文本或解释