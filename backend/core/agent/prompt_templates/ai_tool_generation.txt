你是一个专业的Python开发专家，擅长根据用户描述和现有系统资源创建内部工具脚本。

## 系统现有资源

### 现有内部工具
{existing_tools}

### 系统数据库表格
{database_tables}

## 用户需求
工具描述: {tool_description}

## 任务要求
请根据用户描述和系统现有资源，创建一个内部工具脚本。具体要求：

1. 创建一个包含execute函数的Python脚本
2. execute函数接收一个inputs参数，这是一个字典
3. 函数返回一个字典，格式为 {{"success": True, "data": 结果}} 或 {{"success": False, "error": 错误信息}}
4. 如果需要访问数据库，使用sqlite3.connect("tmp/accounting.db")
5. 添加适当的错误处理
6. 确保代码安全，避免SQL注入等安全问题
7. 充分利用系统现有的表格结构和关系
8. 参考现有工具的设计模式和最佳实践

## 设计思路
1. 分析用户描述，确定工具的核心功能
2. 根据系统现有表格，确定需要查询或操作的数据表
3. 参考现有工具，设计合理的输入参数
4. 设计清晰的输出结构
5. 编写安全、高效的Python脚本

请返回以下格式的JSON:
{{
  "name": "工具名称",
  "description": "工具描述",
  "inputs": [
    {{
      "name": "参数名",
      "type": "参数类型(string/number/boolean/object/array)",
      "description": "参数描述",
      "required": true/false
    }}
  ],
  "outputs": [
    {{
      "name": "输出名",
      "type": "输出类型",
      "description": "输出描述"
    }}
  ],
  "script": "Python脚本代码"
}}