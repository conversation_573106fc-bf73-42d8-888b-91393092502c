请从以下发票OCR文本中提取结构化信息。如果某个字段不存在，请返回"无"。

特别注意：
1. 购买方和销售方信息分离：发票中购买方和销售方都有各自完整的公司信息，包括名称、纳税人识别号、地址电话、开户行账号等，请分别提取到对应的字段中。
2. 地址和电话字段分离：发票中地址和电话通常写在一起（如"地址、电话：北京雍和商贸百货公司15512345678"），请将其分离，地址信息放入对应的"地址"字段，电话信息放入对应的"电话"字段。
3. 开户行及账号字段分离：开户行和账号通常写在一起（如"开户行及账号：中国工商银行北京支行3303011002220134111"），请将其分离，开户行信息放入对应的"开户行"字段，账号信息放入对应的"开户行账号"字段。

OCR文本：
{ocr_content}

请提取以下字段并以JSON格式返回：

通用字段：
- 发票号码
- 发票代码
- 开票日期
- 金额
- 税额
- 价税合计

购买方信息：
- 名称
- 纳税人识别号
- 地址
- 电话
- 开户行
- 开户行账号

销售方信息：
- 名称
- 纳税人识别号
- 地址
- 电话
- 开户行
- 开户行账号

商品信息：
- 商品名称
- 规格型号
- 数量
- 单价
- 税率

其他信息：
- 收款人
- 复核
- 开票人

请以以下JSON格式返回：
{{
    "通用字段": {{
        "发票号码": "",
        "发票代码": "",
        "开票日期": "",
        "金额": "",
        "税额": "",
        "价税合计": ""
    }},
    "购买方信息": {{
        "名称": "",
        "纳税人识别号": "",
        "地址": "",
        "电话": "",
        "开户行": "",
        "开户行账号": ""
    }},
    "销售方信息": {{
        "名称": "",
        "纳税人识别号": "",
        "地址": "",
        "电话": "",
        "开户行": "",
        "开户行账号": ""
    }},
    "商品信息": {{
        "商品名称": "",
        "规格型号": "",
        "数量": "",
        "单价": "",
        "税率": ""
    }},
    "其他信息": {{
        "收款人": "",
        "复核": "",
        "开票人": ""
    }}
}}