请仔细分析这张发票图片，提取以下结构化信息。你可以结合图片内容和OCR识别结果进行分析，利用视觉信息纠正OCR中可能存在的错误或错别字，提供更准确的识别结果。

如果提供了OCR文本结果，请参考但不完全依赖，优先以图片中的实际内容为准。你可以：
1. 使用OCR结果作为参考，快速定位关键信息
2. 通过图片验证OCR结果的准确性
3. 纠正OCR中的错别字、数字识别错误等问题
4. 补充OCR可能遗漏的信息

请按照以下JSON格式返回结果：

```json
{{
  "通用字段": {{
    "发票号码": "从图片中识别的发票号码，如果没有则填写'无'",
    "发票代码": "从图片中识别的发票代码，如果没有则填写'无'",
    "开票日期": "从图片中识别的开票日期，格式为YYYY-MM-DD，如果没有则填写'无'",
    "金额": "从图片中识别的不含税金额，如果没有则填写'无'",
    "税额": "从图片中识别的税额，如果没有则填写'无'",
    "价税合计": "从图片中识别的价税合计金额，如果没有则填写'无'"
  }},
  "购买方信息": {{
    "名称": "从图片中识别的购买方名称，如果没有则填写'无'",
    "纳税人识别号": "从图片中识别的购买方纳税人识别号，如果没有则填写'无'",
    "地址": "从图片中识别的购买方地址，如果没有则填写'无'",
    "电话": "从图片中识别的购买方电话，如果没有则填写'无'",
    "开户行": "从图片中识别的购买方开户行，如果没有则填写'无'",
    "开户行账号": "从图片中识别的购买方银行账号，如果没有则填写'无'"
  }},
  "销售方信息": {{
    "名称": "从图片中识别的销售方名称，如果没有则填写'无'",
    "纳税人识别号": "从图片中识别的销售方纳税人识别号，如果没有则填写'无'",
    "地址": "从图片中识别的销售方地址，如果没有则填写'无'",
    "电话": "从图片中识别的销售方电话，如果没有则填写'无'",
    "开户行": "从图片中识别的销售方开户行，如果没有则填写'无'",
    "开户行账号": "从图片中识别的销售方银行账号，如果没有则填写'无'"
  }},
  "商品信息": {{
    "商品名称": "从图片中识别的主要商品或服务名称，如果没有则填写'无'",
    "规格型号": "从图片中识别的商品规格型号，如果没有则填写'无'",
    "数量": "从图片中识别的商品数量，如果没有则填写'无'",
    "单价": "从图片中识别的商品单价，如果没有则填写'无'",
    "税率": "从图片中识别的税率，如果没有则填写'无'"
  }},
  "其他信息": {{
    "收款人": "从图片中识别的收款人，如果没有则填写'无'",
    "复核": "从图片中识别的复核人，如果没有则填写'无'",
    "开票人": "从图片中识别的开票人，如果没有则填写'无'"
  }}
}}
```

注意事项：
1. 请仔细观察图片中的所有文字信息，结合OCR结果进行交叉验证
2. 如果某个字段在图片中不存在或无法识别，请填写"无"
3. 金额字段请保留数字格式，不要包含货币符号，注意纠正OCR可能的数字识别错误
4. 日期格式请统一为YYYY-MM-DD，注意验证OCR识别的日期是否正确
5. 电话号码请提取完整的号码，包括区号，纠正OCR可能的数字错误
6. 地址信息请完整提取，包括详细地址，注意纠正OCR的文字识别错误
7. 公司名称、纳税人识别号等重要信息请特别仔细核对，确保准确性
8. 请确保返回的是有效的JSON格式