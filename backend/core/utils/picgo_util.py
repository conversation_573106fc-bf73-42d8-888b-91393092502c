#!/usr/bin/env python3
"""
picgo.net 图床工具类
用于上传图片到 picgo.net 图床并获取公网可访问的 URL
"""

import requests
import json
import random
import string
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv
import os

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class PicgoUtil:
    """picgo.net 图床工具类"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化 PicgoUtil
        
        Args:
            api_key: API key，如果不提供则从环境变量获取
        """
        self.api_key = api_key or os.getenv('PICGO_API_KEY')
        self.upload_url = 'https://www.picgo.net/api/1/upload'
        
        if not self.api_key:
            logger.warning("未提供 PICGO_API_KEY，图片上传功能将不可用")
    
    def generate_random_filename(self, original_path: str) -> str:
        """
        生成一个随机文件名，保留原始文件扩展名
        
        Args:
            original_path: 原始文件路径
            
        Returns:
            随机文件名
        """
        ext = Path(original_path).suffix
        random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        return f"{random_str}{ext}"
    
    def upload_image(self, image_path: str) -> Optional[Dict[str, Any]]:
        """
        上传图片到 picgo.net 图床
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            包含图片信息的字典，包括 url 和 delete_url，失败时返回 None
        """
        if not self.api_key:
            logger.error("未配置 PICGO_API_KEY，无法上传图片")
            return None
        
        if not os.path.exists(image_path):
            logger.error(f"文件不存在: {image_path}")
            return None
        
        try:
            # 生成随机文件名
            random_filename = self.generate_random_filename(image_path)
            logger.info(f"上传图片: {image_path} -> {random_filename}")
            
            # 准备文件和请求头
            with open(image_path, 'rb') as f:
                files = {'source': (random_filename, f)}
                data = {'title': random_filename, 'format': 'json'}
                headers = {'X-API-Key': self.api_key}
                
                # 发送请求
                response = requests.post(self.upload_url, files=files, data=data, headers=headers)
                result = response.json()
            
            # 检查是否成功
            if result.get('status_code') == 200 and 'image' in result:
                image_info = result['image']
                upload_result = {
                    'url': image_info.get('url'),
                    'delete_url': image_info.get('delete_url'),
                    'filename': random_filename,
                    'original_path': image_path
                }
                logger.info(f"图片上传成功: {upload_result['url']}")
                return upload_result
            else:
                error_msg = result.get('error', {}).get('message', '未知错误')
                logger.error(f"图片上传失败: {error_msg}")
                return None
                
        except Exception as e:
            logger.error(f"上传图片时发生错误: {str(e)}")
            return None
    
    def delete_image(self, delete_url: str) -> bool:
        """
        删除已上传的图片
        
        Args:
            delete_url: 图片删除 URL
            
        Returns:
            删除是否成功
        """
        if not self.api_key:
            logger.error("未配置 PICGO_API_KEY，无法删除图片")
            return False
        
        if not delete_url:
            logger.error("未提供删除 URL")
            return False
        
        try:
            headers = {'X-API-Key': self.api_key}
            response = requests.delete(delete_url, headers=headers)
            
            # 200 和 404 都表示删除成功
            if response.status_code in [200, 404]:
                logger.info(f"图片删除成功: {delete_url}")
                return True
            else:
                logger.error(f"图片删除失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"删除图片时发生错误: {str(e)}")
            return False


# 全局实例
_picgo_util = None

def get_picgo_util() -> PicgoUtil:
    """获取 PicgoUtil 实例"""
    global _picgo_util
    if _picgo_util is None:
        _picgo_util = PicgoUtil()
    return _picgo_util