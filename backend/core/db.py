from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
import os

# 项目根目录下的 tmp 目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
TMP_DIR = os.path.join(PROJECT_ROOT, "tmp")
os.makedirs(TMP_DIR, exist_ok=True)
DB_PATH = os.path.join(TMP_DIR, "accounting.db")
DATABASE_URL = f"sqlite+aiosqlite:///{DB_PATH}"

engine = create_async_engine(DATABASE_URL, echo=True, future=True)
AsyncSessionLocal = sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False,
)
Base = declarative_base()

async def get_db_session():
    """获取数据库会话"""
    async with AsyncSessionLocal() as session:
        yield session

def get_db_connection():
    """获取数据库连接（同步版本，用于工具脚本）"""
    import sqlite3
    return sqlite3.connect(DB_PATH)