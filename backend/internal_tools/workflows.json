{"9e054747-f061-4afd-a3a3-fe6d8bc5d323": {"id": "9e054747-f061-4afd-a3a3-fe6d8bc5d323", "name": "部门员工统计报告", "description": "统计指定部门的员工数量和基本信息", "inputs": [{"name": "department", "type": "string", "description": "部门名称", "required": true}], "outputs": [{"name": "employees", "type": "array", "description": "员工列表"}, {"name": "employee_count", "type": "number", "description": "员工数量"}, {"name": "dept_stats", "type": "object", "description": "部门统计信息"}], "steps": [{"step_id": "step_1", "tool_id": "query_employee_info", "inputs": {"department": "${department}"}, "output_mapping": {"employee_info": "employees", "count": "employee_count"}}, {"step_id": "step_2", "tool_id": "count_employees", "inputs": {"department": "${department}"}, "output_mapping": {"total_count": "total_employees", "department_breakdown": "dept_stats"}}], "enabled": true, "created_at": "2025-09-12T12:44:09.022275", "updated_at": "2025-09-12T12:44:09.022302", "type": "workflow"}, "e2921a53-4a9b-4704-b863-9df9fba858ef": {"id": "e2921a53-4a9b-4704-b863-9df9fba858ef", "name": "部门年度报销分析", "description": "分析指定部门的年度报销情况，包括总额和月度分布", "inputs": [{"name": "department", "type": "string", "description": "部门名称", "required": true}, {"name": "year", "type": "number", "description": "年份", "required": false}], "outputs": [{"name": "total_expenses", "type": "number", "description": "总报销金额"}, {"name": "expense_types", "type": "object", "description": "按费用类型分组"}, {"name": "monthly_expenses", "type": "object", "description": "按月份分组"}], "steps": [{"step_id": "step_1", "tool_id": "query_department_expenses", "inputs": {"department": "${department}", "year": "${year}"}, "output_mapping": {"total_amount": "total_expenses", "expense_breakdown": "expense_types", "monthly_breakdown": "monthly_expenses"}}], "enabled": true, "created_at": "2025-09-12T12:44:09.024153", "updated_at": "2025-09-12T12:44:09.024163", "type": "workflow"}, "d5578dc2-8420-44c7-98bf-4dd30a0f2bc5": {"id": "d5578dc2-8420-44c7-98bf-4dd30a0f2bc5", "name": "凭证搜索与统计", "description": "根据关键词搜索凭证并计算统计数据", "inputs": [{"name": "keyword", "type": "string", "description": "搜索关键词", "required": false}, {"name": "date_from", "type": "string", "description": "开始日期", "required": false}, {"name": "date_to", "type": "string", "description": "结束日期", "required": false}], "outputs": [{"name": "found_vouchers", "type": "array", "description": "找到的凭证"}, {"name": "voucher_count", "type": "number", "description": "凭证数量"}, {"name": "amount_statistics", "type": "object", "description": "金额统计信息"}], "steps": [{"step_id": "step_1", "tool_id": "search_vouchers", "inputs": {"keyword": "${keyword}", "date_from": "${date_from}", "date_to": "${date_to}", "limit": "50"}, "output_mapping": {"vouchers": "found_vouchers", "total_count": "voucher_count", "total_amount": "voucher_total"}}], "enabled": true, "created_at": "2025-09-12T12:44:09.026622", "updated_at": "2025-09-12T12:44:09.026641", "type": "workflow"}, "a39153cf-f2e0-454e-8450-69b8f28d7b96": {"id": "a39153cf-f2e0-454e-8450-69b8f28d7b96", "name": "高额报销筛选与分析", "description": "筛选出超过指定金额的报销凭证并进行统计分析", "inputs": [{"name": "date_from", "type": "string", "description": "开始日期", "required": false}, {"name": "date_to", "type": "string", "description": "结束日期", "required": false}, {"name": "min_amount", "type": "number", "description": "最小金额阈值", "required": true}], "outputs": [{"name": "high_amount_vouchers", "type": "array", "description": "高额凭证列表"}, {"name": "high_amount_count", "type": "number", "description": "高额凭证数量"}, {"name": "statistics", "type": "object", "description": "统计信息"}], "steps": [{"step_id": "step_1", "tool_id": "search_vouchers", "inputs": {"date_from": "${date_from}", "date_to": "${date_to}", "limit": "100"}, "output_mapping": {}}, {"step_id": "step_2", "tool_id": "filter_data", "inputs": {"data": "@step_1.vouchers", "field": "total_amount", "operator": "gte", "value": "${min_amount}"}, "output_mapping": {"filtered_data": "high_amount_vouchers", "count": "high_amount_count"}}], "enabled": true, "created_at": "2025-09-12T12:44:09.029505", "updated_at": "2025-09-12T12:44:09.029513", "type": "workflow"}}