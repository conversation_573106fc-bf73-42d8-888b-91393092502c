from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional
import csv
from fastapi.responses import StreamingResponse
from io import StringIO
from api.experience import add_experience, ExperienceRecord
from api.role_staff import Role, Staff, Level
from datetime import datetime
from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from core.db import AsyncSessionLocal
from models.role_staff import Role as RoleModel, Level as LevelModel, Staff as StaffModel

router = APIRouter()

# Dependency to get DB session
async def get_db():
    from core.db import AsyncSessionLocal
    
    db = AsyncSessionLocal()
    try:
        yield db
    finally:
        db.close()

@router.get("/roles")
async def get_roles(page: int = 1, size: int = 50, db: AsyncSession = Depends(get_db)):
    # Calculate offset
    offset = (page - 1) * size
    
    # Get total count
    count_result = await db.execute(select(RoleModel))
    total = len(count_result.scalars().all())
    
    # Get paginated roles
    result = await db.execute(select(RoleModel).offset(offset).limit(size))
    roles = result.scalars().all()
    
    return {
        "total": total, 
        "page": page, 
        "size": size, 
        "roles": [
            {
                "code": role.code,
                "name": role.name,
                "description": role.description
            } for role in roles
        ]
    }

@router.post("/roles")
async def add_role(role: Role, db: AsyncSession = Depends(get_db)):
    # Check if role already exists
    existing_role = await db.execute(select(RoleModel).where(RoleModel.code == role.code))
    if existing_role.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="角色代码已存在")
    
    # Create new role
    new_role = RoleModel(
        code=role.code,
        name=role.name,
        description=role.description
    )
    db.add(new_role)
    await db.commit()
    return {"success": True}

@router.put("/roles/{code}")
async def update_role(code: str, role: Role, db: AsyncSession = Depends(get_db)):
    # Find role
    result = await db.execute(select(RoleModel).where(RoleModel.code == code))
    db_role = result.scalar_one_or_none()
    
    if not db_role:
        raise HTTPException(status_code=404, detail="部门未找到")
    
    # Update role
    db_role.name = role.name
    db_role.description = role.description
    await db.commit()
    return {"success": True}

@router.delete("/roles/{code}")
async def delete_role(code: str, db: AsyncSession = Depends(get_db)):
    # Find role
    result = await db.execute(select(RoleModel).where(RoleModel.code == code))
    db_role = result.scalar_one_or_none()
    
    if not db_role:
        raise HTTPException(status_code=404, detail="部门未找到")
    
    # Delete role
    await db.delete(db_role)
    await db.commit()
    return {"success": True}

@router.post("/roles/import")
async def import_roles(file: UploadFile = File(...), db: AsyncSession = Depends(get_db)):
    content = await file.read()
    content_str = content.decode("utf-8")
    reader = csv.DictReader(content_str.splitlines())
    imported = []
    
    for row in reader:
        # Check if role already exists
        existing_role = await db.execute(select(RoleModel).where(RoleModel.code == row['code']))
        if existing_role.scalar_one_or_none():
            continue
            
        # Create new role
        new_role = RoleModel(
            code=row['code'],
            name=row['name'],
            description=row.get('description', '')
        )
        db.add(new_role)
        imported.append(row)
    
    await db.commit()
    return {"imported": imported}

@router.get("/roles/export")
async def export_roles(db: AsyncSession = Depends(get_db)):
    # Get all roles
    result = await db.execute(select(RoleModel))
    roles = result.scalars().all()
    
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=["code", "name", "description"])
    writer.writeheader()
    
    for role in roles:
        writer.writerow({
            "code": role.code,
            "name": role.name,
            "description": role.description
        })
    
    output.seek(0)
    return StreamingResponse(
        output, 
        media_type="text/csv", 
        headers={"Content-Disposition": "attachment; filename=roles.csv"}
    )

@router.get("/staffs")
async def get_staffs(page: int = 1, size: int = 50, db: AsyncSession = Depends(get_db)):
    # Calculate offset
    offset = (page - 1) * size
    
    # Get total count
    count_result = await db.execute(select(StaffModel))
    total = len(count_result.scalars().all())
    
    # Get paginated staffs with role relationship
    result = await db.execute(
        select(StaffModel, RoleModel)
        .join(RoleModel, StaffModel.role_code == RoleModel.code)
        .offset(offset)
        .limit(size)
    )
    
    staffs = []
    for staff, role in result:
        staffs.append({
            "job_no": staff.job_no,
            "name": staff.name,
            "role_code": staff.role_code,
            "role_name": role.name,  # Include role name
            "level": staff.level,
            "phone": staff.phone,
            "status": staff.status,
            "remark": staff.remark
        })
    
    return {
        "total": total, 
        "page": page, 
        "size": size, 
        "staffs": staffs
    }

@router.post("/staffs")
async def add_staff(staff: Staff, user_id: str = "system", db: AsyncSession = Depends(get_db)):
    # Check if staff already exists
    existing_staff = await db.execute(select(StaffModel).where(StaffModel.job_no == staff.job_no))
    if existing_staff.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="员工工号已存在")
    
    # Check if role exists
    existing_role = await db.execute(select(RoleModel).where(RoleModel.code == staff.role_code))
    if not existing_role.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="角色代码不存在")
    
    # Create new staff
    new_staff = StaffModel(
        job_no=staff.job_no,
        name=staff.name,
        role_code=staff.role_code,
        level=staff.level,
        phone=staff.phone,
        status=staff.status,
        remark=staff.remark
    )
    db.add(new_staff)
    await db.commit()
    
    # Record experience
    try:
        add_experience(ExperienceRecord(
            user_id=user_id,
            type="staff",
            original={},
            modified=staff.dict(),
            timestamp=datetime.now().isoformat(),
            remark="新增员工"
        ))
    except Exception as e:
        print(f"记录经验时出错: {e}")
    
    return {"success": True}

@router.put("/staffs/{job_no}")
async def update_staff(job_no: str, staff: Staff, user_id: str = "system", db: AsyncSession = Depends(get_db)):
    # Find staff
    result = await db.execute(select(StaffModel).where(StaffModel.job_no == job_no))
    db_staff = result.scalar_one_or_none()
    
    if not db_staff:
        raise HTTPException(status_code=404, detail="人员未找到")
    
    # Check if role exists if role_code is being changed
    if db_staff.role_code != staff.role_code:
        existing_role = await db.execute(select(RoleModel).where(RoleModel.code == staff.role_code))
        if not existing_role.scalar_one_or_none():
            raise HTTPException(status_code=400, detail="角色代码不存在")
    
    # Store old data for experience recording
    old_data = {
        "job_no": db_staff.job_no,
        "name": db_staff.name,
        "role_code": db_staff.role_code,
        "level": db_staff.level,
        "phone": db_staff.phone,
        "status": db_staff.status,
        "remark": db_staff.remark
    }
    
    # Update staff
    db_staff.name = staff.name
    db_staff.role_code = staff.role_code
    db_staff.level = staff.level
    db_staff.phone = staff.phone
    db_staff.status = staff.status
    db_staff.remark = staff.remark
    await db.commit()
    
    # Record experience
    try:
        add_experience(ExperienceRecord(
            user_id=user_id,
            type="staff",
            original=old_data,
            modified=staff.dict(),
            timestamp=datetime.now().isoformat(),
            remark="修改员工"
        ))
    except Exception as e:
        print(f"记录经验时出错: {e}")
    
    return {"success": True}

@router.delete("/staffs/{job_no}")
async def delete_staff(job_no: str, user_id: str = "system", db: AsyncSession = Depends(get_db)):
    # Find staff
    result = await db.execute(select(StaffModel).where(StaffModel.job_no == job_no))
    db_staff = result.scalar_one_or_none()
    
    if not db_staff:
        raise HTTPException(status_code=404, detail="人员未找到")
    
    # Store old data for experience recording
    old_data = {
        "job_no": db_staff.job_no,
        "name": db_staff.name,
        "role_code": db_staff.role_code,
        "level": db_staff.level,
        "phone": db_staff.phone,
        "status": db_staff.status,
        "remark": db_staff.remark
    }
    
    # Delete staff
    await db.delete(db_staff)
    await db.commit()
    
    # Record experience
    try:
        add_experience(ExperienceRecord(
            user_id=user_id,
            type="staff",
            original=old_data,
            modified={},
            timestamp=datetime.now().isoformat(),
            remark="删除员工"
        ))
    except Exception as e:
        print(f"记录经验时出错: {e}")
    
    return {"success": True}

@router.post("/staffs/import")
async def import_staffs(file: UploadFile = File(...), db: AsyncSession = Depends(get_db)):
    content = await file.read()
    content_str = content.decode("utf-8")
    reader = csv.DictReader(content_str.splitlines())
    imported = []
    
    for row in reader:
        # Check if staff already exists
        existing_staff = await db.execute(select(StaffModel).where(StaffModel.job_no == row['job_no']))
        if existing_staff.scalar_one_or_none():
            continue
            
        # Check if role exists
        existing_role = await db.execute(select(RoleModel).where(RoleModel.code == row['role_code']))
        if not existing_role.scalar_one_or_none():
            continue
            
        # Create new staff
        new_staff = StaffModel(
            job_no=row['job_no'],
            name=row['name'],
            role_code=row['role_code'],
            level=row.get('level', ''),
            phone=row['phone'],
            status=row.get('status', '在职'),
            remark=row.get('remark', '')
        )
        db.add(new_staff)
        imported.append(row)
    
    await db.commit()
    return {"imported": imported}

@router.get("/staffs/export")
async def export_staffs(db: AsyncSession = Depends(get_db)):
    # Get all staffs with role relationship
    result = await db.execute(
        select(StaffModel, RoleModel)
        .join(RoleModel, StaffModel.role_code == RoleModel.code)
    )
    
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=["job_no", "name", "role_code", "level", "phone", "status", "remark"])
    writer.writeheader()
    
    for staff, role in result:
        writer.writerow({
            "job_no": staff.job_no,
            "name": staff.name,
            "role_code": staff.role_code,
            "level": staff.level,
            "phone": staff.phone,
            "status": staff.status,
            "remark": staff.remark
        })
    
    output.seek(0)
    return StreamingResponse(
        output, 
        media_type="text/csv", 
        headers={"Content-Disposition": "attachment; filename=staffs.csv"}
    )

# 级别管理API
@router.get("/levels")
async def get_levels(page: int = 1, size: int = 50, db: AsyncSession = Depends(get_db)):
    # Calculate offset
    offset = (page - 1) * size
    
    # Get total count
    count_result = await db.execute(select(LevelModel))
    total = len(count_result.scalars().all())
    
    # Get paginated levels
    result = await db.execute(select(LevelModel).offset(offset).limit(size))
    levels = result.scalars().all()
    
    return {
        "total": total, 
        "page": page, 
        "size": size, 
        "levels": [
            {
                "id": level.id,
                "name": level.name,
                "description": level.description,
                "tag": level.tag,
                "color": level.color
            } for level in levels
        ]
    }

@router.post("/levels")
async def add_level(level: Level, db: AsyncSession = Depends(get_db)):
    # Check if level already exists
    existing_level = await db.execute(select(LevelModel).where(LevelModel.id == level.id))
    if existing_level.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="级别ID已存在")
    
    # Create new level
    new_level = LevelModel(
        id=level.id,
        name=level.name,
        description=level.description,
        tag=level.tag,
        color=level.color
    )
    db.add(new_level)
    await db.commit()
    return {"success": True, "id": level.id}

@router.put("/levels/{level_id}")
async def update_level(level_id: int, level: Level, db: AsyncSession = Depends(get_db)):
    # Find level
    result = await db.execute(select(LevelModel).where(LevelModel.id == level_id))
    db_level = result.scalar_one_or_none()
    
    if not db_level:
        raise HTTPException(status_code=404, detail="级别未找到")
    
    # Update level
    db_level.name = level.name
    db_level.description = level.description
    db_level.tag = level.tag
    db_level.color = level.color
    await db.commit()
    return {"success": True}

@router.delete("/levels/{level_id}")
async def delete_level(level_id: int, db: AsyncSession = Depends(get_db)):
    # Find level
    result = await db.execute(select(LevelModel).where(LevelModel.id == level_id))
    db_level = result.scalar_one_or_none()
    
    if not db_level:
        raise HTTPException(status_code=404, detail="级别未找到")
    
    # Delete level
    await db.delete(db_level)
    await db.commit()
    return {"success": True}

@router.post("/levels/import")
async def import_levels(file: UploadFile = File(...), db: AsyncSession = Depends(get_db)):
    content = await file.read()
    content_str = content.decode("utf-8")
    reader = csv.DictReader(content_str.splitlines())
    imported = []
    
    for row in reader:
        # Check if level already exists
        level_id = int(row.get('id', 0)) if row.get('id') else 0
        existing_level = await db.execute(select(LevelModel).where(LevelModel.id == level_id))
        if existing_level.scalar_one_or_none():
            continue
            
        # Create new level
        new_level = LevelModel(
            id=level_id,
            name=row.get('name', ''),
            description=row.get('description', ''),
            tag=row.get('tag', ''),
            color=row.get('color', '#3B82F6')
        )
        db.add(new_level)
        imported.append(row)
    
    await db.commit()
    return {"imported": imported}

@router.get("/levels/export")
async def export_levels(db: AsyncSession = Depends(get_db)):
    # Get all levels
    result = await db.execute(select(LevelModel))
    levels = result.scalars().all()
    
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=["id", "name", "description", "tag", "color"])
    writer.writeheader()
    
    for level in levels:
        writer.writerow({
            "id": level.id,
            "name": level.name,
            "description": level.description,
            "tag": level.tag,
            "color": level.color
        })
    
    output.seek(0)
    return StreamingResponse(
        output, 
        media_type="text/csv", 
        headers={"Content-Disposition": "attachment; filename=levels.csv"}
    )