import asyncio
from core.db import engine, Base, AsyncSessionLocal
from models.subject import SubjectAccount
from models.asset import Asset
from models.role_staff import Staff, Role, Level
from models.company import Company
from sqlalchemy.future import select

async def init_models():
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    print("数据库表已初始化")

async def create_sample_companies():
    """Create sample companies for testing"""
    async with AsyncSessionLocal() as db:
        try:
            # Check if sample companies already exist
            stmt = select(Company).where(Company.name.in_([
                "科技创新有限公司"
            ]))
            result = await db.execute(stmt)
            existing_companies = result.scalars().all()
            
            if len(existing_companies) >= 5:
                print("样本公司数据已存在")
                return
            
            # Create sample companies
            sample_companies = [
                Company(
                    name="科技创新有限公司",
                    business_scope="软件开发、技术咨询服务、计算机硬件销售",
                    industry="信息技术服务业",
                    company_size="中小型企业",
                    tax_id="911100001234567890",
                    accounting_standards="企业会计准则"
                )
            ]
            
            # Add companies to session
            for company in sample_companies:
                # Check if company already exists
                stmt = select(Company).where(Company.name == company.name)
                result = await db.execute(stmt)
                existing = result.scalar_one_or_none()
                
                if not existing:
                    db.add(company)
            
            await db.commit()
            print("样本公司数据已创建")
        except Exception as e:
            await db.rollback()
            print(f"创建样本公司数据时出错: {e}")

async def create_sample_roles():
    """Create sample roles for testing"""
    async with AsyncSessionLocal() as db:
        try:
            # Check if sample roles already exist
            stmt = select(Role).where(Role.code.in_([
                "ADM", "FIN", "HR", "IT", "MKT", "SAL", "PUR", "PRO", "RD", "QAL"
            ]))
            result = await db.execute(stmt)
            existing_roles = result.scalars().all()
            
            if len(existing_roles) >= 10:
                print("样本角色数据已存在")
                return
            
            # Create sample roles
            sample_roles = [
                Role(code="ADM", name="行政部", description="负责公司行政事务管理"),
                Role(code="FIN", name="财务部", description="负责公司财务管理和会计核算"),
                Role(code="HR", name="人力资源部", description="负责人力资源管理和员工服务"),
                Role(code="IT", name="信息技术部", description="负责公司信息技术系统开发和维护"),
                Role(code="MKT", name="市场部", description="负责市场推广和品牌建设"),
                Role(code="SAL", name="销售部", description="负责产品销售和客户关系管理"),
                Role(code="PUR", name="采购部", description="负责公司物资采购和供应商管理"),
                Role(code="PRO", name="生产部", description="负责产品生产制造和质量控制"),
                Role(code="RD", name="研发部", description="负责产品研发和技术创新"),
                Role(code="QAL", name="质量部", description="负责产品质量管理和质量保证")
            ]
            
            # Add roles to session
            for role in sample_roles:
                # Check if role already exists
                stmt = select(Role).where(Role.code == role.code)
                result = await db.execute(stmt)
                existing = result.scalar_one_or_none()
                
                if not existing:
                    db.add(role)
            
            await db.commit()
            print("样本角色数据已创建")
        except Exception as e:
            await db.rollback()
            print(f"创建样本角色数据时出错: {e}")

async def create_sample_levels():
    """Create sample levels for testing"""
    async with AsyncSessionLocal() as db:
        try:
            # Check if sample levels already exist
            stmt = select(Level).where(Level.id.in_([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]))
            result = await db.execute(stmt)
            existing_levels = result.scalars().all()
            
            if len(existing_levels) >= 10:
                print("样本级别数据已存在")
                return
            
            # Create sample levels
            sample_levels = [
                Level(id=1, name="总监", description="公司最高管理层", tag="高管,领导层", color="#EF4444"),
                Level(id=2, name="经理", description="部门负责人", tag="管理层,领导层", color="#F59E0B"),
                Level(id=3, name="主管", description="团队负责人", tag="中层管理,领导层", color="#10B981"),
                Level(id=4, name="高级工程师", description="高级技术人员", tag="技术骨干", color="#3B82F6"),
                Level(id=5, name="工程师", description="技术人员", tag="技术人员", color="#6366F1"),
                Level(id=6, name="专员", description="专业岗位人员", tag="专业人员", color="#8B5CF6"),
                Level(id=7, name="助理", description="助理岗位人员", tag="辅助人员", color="#EC4899"),
                Level(id=8, name="技术员", description="技术操作人员", tag="操作人员", color="#14B8A6"),
                Level(id=9, name="工人", description="生产操作人员", tag="一线人员", color="#F97316"),
                Level(id=10, name="实习生", description="实习人员", tag="实习", color="#94A3B8")
            ]
            
            # Add levels to session
            for level in sample_levels:
                # Check if level already exists
                stmt = select(Level).where(Level.id == level.id)
                result = await db.execute(stmt)
                existing = result.scalar_one_or_none()
                
                if not existing:
                    db.add(level)
            
            await db.commit()
            print("样本级别数据已创建")
        except Exception as e:
            await db.rollback()
            print(f"创建样本级别数据时出错: {e}")

async def create_sample_staffs():
    """Create sample staffs for testing"""
    async with AsyncSessionLocal() as db:
        try:
            # Check if sample staffs already exist
            stmt = select(Staff).where(Staff.job_no.in_([
                "ADM001", "FIN001", "HR001", "IT001", "MKT001", "SAL001", "PUR001", "PRO001", "RD001", "QAL001"
            ]))
            result = await db.execute(stmt)
            existing_staffs = result.scalars().all()
            
            if len(existing_staffs) >= 10:
                print("样本员工数据已存在")
                return
            
            # Create sample staffs
            sample_staffs = [
                # 行政部人员
                Staff(job_no="ADM001", name="张伟", role_code="ADM", level="经理", phone="13800138001", status="在职", remark="行政部经理"),
                Staff(job_no="ADM002", name="李娜", role_code="ADM", level="专员", phone="13800138002", status="在职", remark="行政专员"),
                Staff(job_no="ADM003", name="王芳", role_code="ADM", level="助理", phone="13800138003", status="在职", remark="行政助理"),
                
                # 财务部人员
                Staff(job_no="FIN001", name="赵强", role_code="FIN", level="总监", phone="13800138004", status="在职", remark="财务总监"),
                Staff(job_no="FIN002", name="钱明", role_code="FIN", level="高级会计", phone="13800138005", status="在职", remark="高级会计"),
                Staff(job_no="FIN003", name="孙丽", role_code="FIN", level="出纳", phone="13800138006", status="在职", remark="出纳"),
                
                # 人力资源部人员
                Staff(job_no="HR001", name="周敏", role_code="HR", level="经理", phone="13800138007", status="在职", remark="人力资源部经理"),
                Staff(job_no="HR002", name="吴刚", role_code="HR", level="专员", phone="13800138008", status="在职", remark="招聘专员"),
                Staff(job_no="HR003", name="郑雪", role_code="HR", level="助理", phone="13800138009", status="在职", remark="人事助理"),
                
                # 信息技术部人员
                Staff(job_no="IT001", name="王磊", role_code="IT", level="总监", phone="13800138010", status="在职", remark="技术总监"),
                Staff(job_no="IT002", name="李静", role_code="IT", level="高级工程师", phone="13800138011", status="在职", remark="高级工程师"),
                Staff(job_no="IT003", name="张洋", role_code="IT", level="工程师", phone="13800138012", status="在职", remark="软件开发工程师"),
                Staff(job_no="IT004", name="刘畅", role_code="IT", level="测试工程师", phone="13800138013", status="在职", remark="测试工程师"),
                
                # 市场部人员
                Staff(job_no="MKT001", name="陈晨", role_code="MKT", level="经理", phone="13800138014", status="在职", remark="市场部经理"),
                Staff(job_no="MKT002", name="杨光", role_code="MKT", level="专员", phone="13800138015", status="在职", remark="市场专员"),
                Staff(job_no="MKT003", name="黄蓉", role_code="MKT", level="设计师", phone="13800138016", status="在职", remark="设计师"),
                
                # 销售部人员
                Staff(job_no="SAL001", name="赵飞", role_code="SAL", level="经理", phone="13800138017", status="在职", remark="销售部经理"),
                Staff(job_no="SAL002", name="钱程", role_code="SAL", level="高级销售", phone="13800138018", status="在职", remark="高级销售代表"),
                Staff(job_no="SAL003", name="孙悦", role_code="SAL", level="销售代表", phone="13800138019", status="在职", remark="销售代表"),
                Staff(job_no="SAL004", name="周涛", role_code="SAL", level="客户经理", phone="13800138020", status="在职", remark="客户经理"),
                
                # 采购部人员
                Staff(job_no="PUR001", name="吴峰", role_code="PUR", level="经理", phone="13800138021", status="在职", remark="采购部经理"),
                Staff(job_no="PUR002", name="郑华", role_code="PUR", level="专员", phone="13800138022", status="在职", remark="采购专员"),
                Staff(job_no="PUR003", name="王琳", role_code="PUR", level="助理", phone="13800138023", status="在职", remark="采购助理"),
                
                # 生产部人员
                Staff(job_no="PRO001", name="冯军", role_code="PRO", level="总监", phone="13800138024", status="在职", remark="生产总监"),
                Staff(job_no="PRO002", name="陈静", role_code="PRO", level="主管", phone="13800138025", status="在职", remark="生产主管"),
                Staff(job_no="PRO003", name="褚亮", role_code="PRO", level="技术员", phone="13800138026", status="在职", remark="技术员"),
                Staff(job_no="PRO004", name="卫东", role_code="PRO", level="工人", phone="13800138027", status="在职", remark="生产工人"),
                
                # 研发部人员
                Staff(job_no="RD001", name="蒋勇", role_code="RD", level="总监", phone="13800138028", status="在职", remark="研发总监"),
                Staff(job_no="RD002", name="沈婷", role_code="RD", level="高级工程师", phone="13800138029", status="在职", remark="高级研发工程师"),
                Staff(job_no="RD003", name="韩梅", role_code="RD", level="研究员", phone="13800138030", status="在职", remark="研究员"),
                
                # 质量部人员
                Staff(job_no="QAL001", name="杨帆", role_code="QAL", level="经理", phone="13800138031", status="在职", remark="质量部经理"),
                Staff(job_no="QAL002", name="朱明", role_code="QAL", level="工程师", phone="13800138032", status="在职", remark="质量工程师"),
                Staff(job_no="QAL003", name="秦岚", role_code="QAL", level="检验员", phone="13800138033", status="在职", remark="质量检验员")
            ]
            
            # Add staffs to session
            for staff in sample_staffs:
                # Check if staff already exists
                stmt = select(Staff).where(Staff.job_no == staff.job_no)
                result = await db.execute(stmt)
                existing = result.scalar_one_or_none()
                
                if not existing:
                    db.add(staff)
            
            await db.commit()
            print("样本员工数据已创建")
        except Exception as e:
            await db.rollback()
            print(f"创建样本员工数据时出错: {e}")

async def main():
    await init_models()
    await create_sample_companies()
    await create_sample_roles()
    await create_sample_levels()
    await create_sample_staffs()

if __name__ == "__main__":
    asyncio.run(main())