#!/usr/bin/env python3
"""
测试AI服务连接问题的修复脚本
"""

import asyncio
import json
import aiohttp
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_ai_service_connection():
    """测试AI服务连接"""

    # 模拟LMStudio服务配置（本地服务，不需要真实API key）
    test_service = {
        "id": "test_lmstudio_service",
        "name": "Test LMStudio",
        "type": "lmstudio",  # 使用专门的 lmstudio 类型
        "enabled": True,
        "config": {
            "baseUrl": "http://localhost:1234/v1",
            "model": "llama-3.2-3b-instruct"
        }
    }

    backend_url = "http://localhost:8000"
    test_endpoint = f"{backend_url}/ai/services/{test_service['id']}/test"
    configure_endpoint = f"{backend_url}/agent/v2/configure"

    print(f"🧪 测试LMStudio服务修复...")
    print(f"📡 测试端点: {test_endpoint}")
    print(f"🔧 服务配置: {json.dumps(test_service, indent=2, ensure_ascii=False)}")

    # 1. 测试服务连接
    print(f"\n📡 1. 测试服务连接...")
    for i in range(3):
        print(f"\n🔄 第 {i+1} 次连接测试:")

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    test_endpoint,
                    json=test_service,
                    headers={'Content-Type': 'application/json'}
                ) as response:

                    print(f"📊 响应状态: {response.status}")

                    if response.status == 200:
                        result = await response.json()
                        print(f"✅ 连接测试成功: {result}")
                    else:
                        error_data = await response.json()
                        print(f"❌ 连接测试失败: {error_data}")

        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")

        # 等待1秒再进行下一次测试
        if i < 2:
            await asyncio.sleep(1)

    # 2. 测试智能体配置
    print(f"\n🤖 2. 测试智能体配置...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                configure_endpoint,
                json={
                    "session_id": "test_session_123",
                    "supports_vision": False
                },
                headers={
                    'Content-Type': 'application/json',
                    'X-API-Key': 'lmstudio',  # LMStudio 占位符
                    'X-Base-URL': 'http://localhost:1234/v1',
                    'X-Model': 'llama-3.2-3b-instruct',
                    'X-Use-Ollama': 'false'
                }
            ) as response:

                print(f"📊 配置响应状态: {response.status}")

                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 智能体配置成功: {result}")
                else:
                    error_data = await response.json()
                    print(f"❌ 智能体配置失败: {error_data}")

    except Exception as e:
        print(f"❌ 智能体配置异常: {str(e)}")

    # 3. 测试消息发送（验证是否使用正确的API格式）
    print(f"\n💬 3. 测试消息发送...")
    try:
        message_endpoint = f"{backend_url}/agent/v2/message"
        async with aiohttp.ClientSession() as session:
            async with session.post(
                message_endpoint,
                json={
                    "content": "你好，这是一个测试消息",
                    "user_id": "test_user",
                    "session_id": "test_session_123"
                },
                headers={
                    'Content-Type': 'application/json',
                    'X-API-Key': 'lmstudio',
                    'X-Base-URL': 'http://localhost:1234/v1',
                    'X-Model': 'llama-3.2-3b-instruct',
                    'X-Use-Ollama': 'false'
                }
            ) as response:

                print(f"📊 消息响应状态: {response.status}")

                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 消息发送成功: {result}")
                else:
                    error_data = await response.json()
                    print(f"❌ 消息发送失败: {error_data}")

    except Exception as e:
        print(f"❌ 消息发送异常: {str(e)}")

    print(f"\n🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(test_ai_service_connection())
