#!/usr/bin/env python3
"""
测试审核响应解析功能
"""
import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.core.agent.agent import AgentExecutor

async def test_parse_audit_response():
    """测试审核响应解析方法"""
    agent = AgentExecutor()
    
    # 测试用例1: 标准的结构化标记响应
    test_response_1 = """
    这是一份销售发票的审核分析。经过详细检查，发现以下问题：
    
    1. 发票号码格式正确
    2. 金额计算准确
    3. 但是缺少必要的税务信息
    
    <|audit_conclusion|>需要补充税务相关信息才能完成审核<|/audit_conclusion|>
    <|action|>request_more_info<|/action|>
    <|needs_more_info|>true<|/needs_more_info|>
    <|required_info|>税务登记号,增值税税率,税额计算明细<|/required_info|>
    <|is_finished|>false<|/is_finished|>
    """
    
    result_1 = await agent._parse_audit_response(test_response_1)
    print("测试用例1 - 结构化标记响应:")
    print(f"  action: {result_1['action']}")
    print(f"  audit_conclusion: {result_1['audit_conclusion']}")
    print(f"  needs_more_info: {result_1['needs_more_info']}")
    print(f"  required_info: {result_1['required_info']}")
    print(f"  is_finished: {result_1['is_finished']}")
    print()
    
    # 测试用例2: 审核通过的响应
    test_response_2 = """
    经过全面审核，该单据符合所有要求：
    
    <|audit_conclusion|>审核通过，单据完整且符合规范<|/audit_conclusion|>
    <|action|>audit_complete<|/action|>
    <|needs_more_info|>false<|/needs_more_info|>
    <|required_info|><|/required_info|>
    <|is_finished|>true<|/is_finished|>
    """
    
    result_2 = await agent._parse_audit_response(test_response_2)
    print("测试用例2 - 审核通过响应:")
    print(f"  action: {result_2['action']}")
    print(f"  audit_conclusion: {result_2['audit_conclusion']}")
    print(f"  needs_more_info: {result_2['needs_more_info']}")
    print(f"  is_finished: {result_2['is_finished']}")
    print()
    
    # 测试用例3: 审核不通过的响应
    test_response_3 = """
    审核发现严重问题，无法通过：
    
    <|audit_conclusion|>单据存在伪造嫌疑，审核不通过<|/audit_conclusion|>
    <|action|>audit_rejected<|/action|>
    <|needs_more_info|>false<|/needs_more_info|>
    <|required_info|><|/required_info|>
    <|is_finished|>true<|/is_finished|>
    """
    
    result_3 = await agent._parse_audit_response(test_response_3)
    print("测试用例3 - 审核不通过响应:")
    print(f"  action: {result_3['action']}")
    print(f"  audit_conclusion: {result_3['audit_conclusion']}")
    print(f"  needs_more_info: {result_3['needs_more_info']}")
    print(f"  is_finished: {result_3['is_finished']}")
    print()
    
    # 测试用例4: 没有结构化标记的文本响应（备用解析）
    test_response_4 = """
    这是一份采购合同的审核。经过分析发现需要补充以下信息：
    
    1. 供应商营业执照
    2. 合同签署日期
    3. 付款条款详细说明
    
    请提供上述信息以完成审核。
    """
    
    result_4 = await agent._parse_audit_response(test_response_4)
    print("测试用例4 - 文本备用解析:")
    print(f"  action: {result_4['action']}")
    print(f"  audit_conclusion: {result_4['audit_conclusion']}")
    print(f"  needs_more_info: {result_4['needs_more_info']}")
    print(f"  required_info: {result_4['required_info']}")
    print(f"  is_finished: {result_4['is_finished']}")
    print()
    
    print("所有测试用例完成！")

if __name__ == "__main__":
    asyncio.run(test_parse_audit_response())