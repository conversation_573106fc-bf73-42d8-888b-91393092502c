import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__)))

from core.agent.agent import AccountingAgent
from core.db import engine, AsyncSessionLocal
from models.company import Company
from sqlalchemy.future import select

async def test_company_integration():
    """Test the company information integration"""
    print("Testing company information integration...")
    
    # Create an agent instance
    agent = AccountingAgent()
    
    # Test 1: Check if company data can be retrieved from database
    print("\n1. Testing company data retrieval from database:")
    db = AsyncSessionLocal(bind=engine)
    try:
        stmt = select(Company)
        result = await db.execute(stmt)
        companies = result.scalars().all()
        
        if companies:
            company = companies[0]  # Get the first company
            print(f"   Company found: {company.name}")
            print(f"   Business scope: {company.business_scope}")
            print(f"   Industry: {company.industry}")
            print(f"   Tax ID: {company.tax_id}")
            print(f"   Accounting standards: {company.accounting_standards}")
        else:
            print("   No companies found in database")
    finally:
        await db.close()
    
    # Test 2: Check if agent can gather company context
    print("\n2. Testing agent's company context gathering:")
    context_data = await agent._gather_context_data("test_user")
    company_context = context_data.get("company", {})
    
    if company_context:
        print(f"   Company name: {company_context.get('name', '')}")
        print(f"   Business scope: {company_context.get('business_scope', '')}")
        print(f"   Industry: {company_context.get('industry', '')}")
        print(f"   Tax info: {company_context.get('tax_info', '')}")
        print(f"   Accounting standards: {company_context.get('accounting_standards', '')}")
    else:
        print("   No company context found")
    
    # Test 3: Test subject matching with company information
    print("\n3. Testing subject matching with company information:")
    
    # Test case 1: Software company selling software
    test_input_1 = "软件公司销售软件产品获得收入100000元，增值税销项税额13000元"
    print(f"   Test input: {test_input_1}")
    
    result_1 = await agent.process_message(test_input_1, "test_user")
    if result_1.get("success"):
        matched_entries = result_1.get("data", {}).get("matched_entries", [])
        if matched_entries:
            for entry in matched_entries:
                subject = entry.get("subject", {})
                print(f"     Matched subject: {subject.get('name', '')} ({subject.get('code', '')})")
                print(f"     Match score: {entry.get('match_score', 0)}")
                print(f"     Reason: {entry.get('reason', '')}")
        else:
            print("     No matching entries found")
    else:
        print(f"     Error: {result_1.get('error', 'Unknown error')}")
    
    # Test case 2: Restaurant purchasing ingredients
    test_input_2 = "餐厅采购食材支出5000元"
    print(f"\n   Test input: {test_input_2}")
    
    result_2 = await agent.process_message(test_input_2, "test_user")
    if result_2.get("success"):
        matched_entries = result_2.get("data", {}).get("matched_entries", [])
        if matched_entries:
            for entry in matched_entries:
                subject = entry.get("subject", {})
                print(f"     Matched subject: {subject.get('name', '')} ({subject.get('code', '')})")
                print(f"     Match score: {entry.get('match_score', 0)}")
                print(f"     Reason: {entry.get('reason', '')}")
        else:
            print("     No matching entries found")
    else:
        print(f"     Error: {result_2.get('error', 'Unknown error')}")
    
    print("\nTesting completed.")

if __name__ == "__main__":
    asyncio.run(test_company_integration())