#!/usr/bin/env python3
"""
PicGo 配置测试脚本
用于测试 picgo.net API Key 是否配置正确
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from examples.picgo_minimal_example import upload_image
from PIL import Image, ImageDraw, ImageFont

def create_test_image():
    """创建测试图片"""
    print("创建测试图片...")
    
    # 创建一个简单的测试图片
    img = Image.new('RGB', (400, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # 添加一些文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
    except:
        try:
            # Windows 字体
            font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 20)
        except:
            # 使用默认字体
            font = ImageFont.load_default()
    
    draw.text((50, 50), "PicGo Test Image", fill='black', font=font)
    draw.text((50, 80), "Invoice No: 12345678", fill='black', font=font)
    draw.text((50, 110), "Amount: $1000.00", fill='black', font=font)
    draw.text((50, 140), "Date: 2024-01-01", fill='black', font=font)
    
    test_image_path = "test_picgo_image.png"
    img.save(test_image_path)
    print(f"测试图片已创建: {test_image_path}")
    return test_image_path

def main():
    """主函数"""
    print("=== PicGo 配置测试 ===")
    
    # 检查环境变量
    api_key = os.getenv('PICGO_API_KEY')
    if not api_key:
        print("错误：未设置 PICGO_API_KEY 环境变量")
        print("请在 .env 文件中添加：PICGO_API_KEY=your_api_key_here")
        return
    
    print(f"API Key: {api_key[:10]}...")
    
    # 创建测试图片
    test_image_path = create_test_image()
    
    try:
        # 测试上传
        print("\n开始测试上传...")
        image_url = upload_image(test_image_path, api_key)
        
        if image_url:
            print("✅ PicGo 配置测试成功！")
            print(f"图片URL: {image_url}")
        else:
            print("❌ PicGo 配置测试失败！")
            print("请检查API Key是否正确")
    
    finally:
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
            print(f"\n清理测试文件: {test_image_path}")

if __name__ == "__main__":
    main()