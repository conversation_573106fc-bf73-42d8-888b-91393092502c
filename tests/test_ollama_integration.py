#!/usr/bin/env python3
"""
Ollama集成测试脚本
用于验证本地大模型服务集成是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.core.ai_client import get_ai_client
from backend.core.config import config

async def test_ollama_connection():
    """测试Ollama连接"""
    print("=" * 50)
    print("测试Ollama集成功能")
    print("=" * 50)
    
    # 检查Ollama配置
    ollama_config = config.get_ollama_config()
    print(f"Ollama配置: {ollama_config}")
    
    if not config.is_ollama_enabled():
        print("警告: Ollama未启用，请在环境变量中设置OLLAMA_ENABLED=true")
        return False
    
    # 创建AI客户端
    try:
        ai_client = get_ai_client(use_ollama=True)
        print(f"AI客户端配置: base_url={ai_client.base_url}, model={ai_client.model}")
        
        if not ai_client.is_configured():
            print("错误: AI客户端未正确配置")
            return False
        
        # 测试简单对话
        print("\n测试简单对话...")
        result = await ai_client.analyze_receipt_content("你好，请简单介绍一下你自己")
        
        if result.get("success"):
            print("✅ Ollama连接测试成功!")
            print(f"响应: {result.get('raw_response', '')[:100]}...")
            return True
        else:
            print(f"❌ Ollama连接测试失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {str(e)}")
        return False

async def test_ollama_stream():
    """测试Ollama流式响应"""
    print("\n" + "=" * 50)
    print("测试Ollama流式响应")
    print("=" * 50)
    
    try:
        ai_client = get_ai_client(use_ollama=True)
        
        # 收集流式响应
        stream_tokens = []
        def stream_callback(token):
            stream_tokens.append(token)
            print(token, end='', flush=True)
        
        print("\n流式响应内容:")
        result = await ai_client.analyze_receipt_content(
            "请用三句话描述人工智能的发展历程",
            stream_callback=stream_callback
        )
        
        print("\n")
        if result.get("success"):
            print("✅ Ollama流式响应测试成功!")
            print(f"总共收到 {len(stream_tokens)} 个token")
            return True
        else:
            print(f"❌ Ollama流式响应测试失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 流式测试过程中出现异常: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("开始Ollama集成测试...")
    
    # 测试基本连接
    basic_test = await test_ollama_connection()
    
    # 测试流式响应
    stream_test = await test_ollama_stream()
    
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    print(f"基本连接测试: {'✅ 通过' if basic_test else '❌ 失败'}")
    print(f"流式响应测试: {'✅ 通过' if stream_test else '❌ 失败'}")
    
    if basic_test and stream_test:
        print("\n🎉 所有测试通过！Ollama集成功能正常工作。")
        return 0
    else:
        print("\n⚠️  部分测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)