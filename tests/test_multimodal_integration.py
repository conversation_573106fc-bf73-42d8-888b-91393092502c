#!/usr/bin/env python3
"""
多模态集成测试脚本
测试前后端多模态功能的完整集成
"""

import asyncio
import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from core.agent.modules.agent_base import get_agent_executor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_multimodal_detection():
    """测试多模态模型检测功能"""
    print("=== 测试多模态模型检测 ===")
    
    agent = get_agent_executor("test_multimodal")
    
    # 测试1: 配置支持多模态的模型
    print("\n1. 测试支持多模态的模型配置")
    success = agent.configure_llm(
        api_key="test_key",
        base_url="https://api.openai.com/v1",
        model="gpt-4-vision-preview",
        supports_vision=True
    )
    
    if success:
        is_multimodal = agent._is_multimodal_model()
        print(f"✅ 模型配置成功，多模态支持: {is_multimodal}")
        assert is_multimodal, "应该检测到多模态支持"
    else:
        print("❌ 模型配置失败")
        return False
    
    # 测试2: 配置不支持多模态的模型
    print("\n2. 测试不支持多模态的模型配置")
    success = agent.configure_llm(
        api_key="test_key",
        base_url="https://api.openai.com/v1",
        model="gpt-3.5-turbo",
        supports_vision=False
    )
    
    if success:
        is_multimodal = agent._is_multimodal_model()
        print(f"✅ 模型配置成功，多模态支持: {is_multimodal}")
        assert not is_multimodal, "不应该检测到多模态支持"
    else:
        print("❌ 模型配置失败")
        return False
    
    # 测试3: 测试前端标记位优先级
    print("\n3. 测试前端标记位优先级")
    success = agent.configure_llm(
        api_key="test_key",
        base_url="https://api.openai.com/v1",
        model="some-unknown-model",  # 未知模型名
        supports_vision=True  # 但前端标记支持多模态
    )
    
    if success:
        is_multimodal = agent._is_multimodal_model()
        print(f"✅ 未知模型配置成功，多模态支持: {is_multimodal}")
        assert is_multimodal, "应该根据前端标记检测到多模态支持"
    else:
        print("❌ 模型配置失败")
        return False
    
    print("\n✅ 所有多模态检测测试通过！")
    return True

async def test_image_upload_simulation():
    """测试图片上传模拟（不实际上传）"""
    print("\n=== 测试图片上传模拟 ===")
    
    agent = get_agent_executor("test_upload")
    
    # 配置支持多模态的模型
    agent.configure_llm(
        api_key="test_key",
        base_url="https://api.openai.com/v1",
        model="gpt-4-vision-preview",
        supports_vision=True
    )
    
    # 模拟图片文件信息
    test_files = [{
        "file_path": "/fake/path/test_invoice.jpg",
        "file_name": "test_invoice.jpg",
        "file_type": "image"
    }]
    
    print("1. 检查多模态处理流程选择")
    is_multimodal = agent._is_multimodal_model()
    print(f"   模型支持多模态: {is_multimodal}")
    
    if is_multimodal:
        print("   ✅ 将使用多模态处理流程")
    else:
        print("   ✅ 将使用OCR处理流程")
    
    print("\n2. 测试多模态消息创建")
    try:
        test_prompt = "请分析这张发票图片"
        test_url = "https://example.com/test.jpg"
        
        multimodal_message = agent._create_multimodal_message(test_prompt, test_url)
        print(f"   ✅ 多模态消息创建成功")
        print(f"   消息类型: {type(multimodal_message).__name__}")
        print(f"   消息内容类型: {type(multimodal_message.content)}")
        
        # 验证消息结构
        if isinstance(multimodal_message.content, list):
            print(f"   消息包含 {len(multimodal_message.content)} 个部分")
            for i, part in enumerate(multimodal_message.content):
                print(f"     部分 {i+1}: {part.get('type', 'unknown')}")
        
    except Exception as e:
        print(f"   ❌ 多模态消息创建失败: {e}")
        return False
    
    print("\n✅ 图片上传模拟测试通过！")
    return True

async def test_config_persistence():
    """测试配置持久化"""
    print("\n=== 测试配置持久化 ===")
    
    agent1 = get_agent_executor("test_persistence_1")
    agent2 = get_agent_executor("test_persistence_2")
    
    # 配置第一个智能体
    print("1. 配置第一个智能体（支持多模态）")
    agent1.configure_llm(
        api_key="test_key_1",
        base_url="https://api.openai.com/v1",
        model="gpt-4-vision-preview",
        supports_vision=True
    )
    
    # 配置第二个智能体
    print("2. 配置第二个智能体（不支持多模态）")
    agent2.configure_llm(
        api_key="test_key_2",
        base_url="https://api.openai.com/v1",
        model="gpt-3.5-turbo",
        supports_vision=False
    )
    
    # 验证配置独立性
    print("3. 验证配置独立性")
    is_multimodal_1 = agent1._is_multimodal_model()
    is_multimodal_2 = agent2._is_multimodal_model()
    
    print(f"   智能体1多模态支持: {is_multimodal_1}")
    print(f"   智能体2多模态支持: {is_multimodal_2}")
    
    assert is_multimodal_1, "智能体1应该支持多模态"
    assert not is_multimodal_2, "智能体2不应该支持多模态"
    
    print("   ✅ 配置独立性验证通过")
    
    # 验证配置信息
    print("4. 验证配置信息存储")
    config1 = agent1.llm_manager._config
    config2 = agent2.llm_manager._config
    
    print(f"   智能体1配置: model={config1.get('model')}, supports_vision={config1.get('supports_vision')}")
    print(f"   智能体2配置: model={config2.get('model')}, supports_vision={config2.get('supports_vision')}")
    
    assert config1.get('supports_vision') == True, "智能体1配置应该支持多模态"
    assert config2.get('supports_vision') == False, "智能体2配置不应该支持多模态"
    
    print("   ✅ 配置信息存储验证通过")
    
    print("\n✅ 配置持久化测试通过！")
    return True

async def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    agent = get_agent_executor("test_error")
    
    print("1. 测试未配置LLM的情况")
    is_multimodal = agent._is_multimodal_model()
    print(f"   未配置LLM时多模态支持: {is_multimodal}")
    assert not is_multimodal, "未配置LLM时不应该支持多模态"
    print("   ✅ 未配置LLM处理正确")
    
    print("2. 测试无效配置")
    try:
        # 配置无效的模型
        success = agent.configure_llm(
            api_key="",  # 空API Key
            base_url="",  # 空URL
            model="",  # 空模型
            supports_vision=True
        )
        print(f"   无效配置结果: {success}")
        # 即使配置失败，也不应该崩溃
        print("   ✅ 无效配置处理正确")
    except Exception as e:
        print(f"   ❌ 无效配置处理异常: {e}")
        return False
    
    print("\n✅ 错误处理测试通过！")
    return True

async def main():
    """主测试函数"""
    print("🚀 开始多模态集成测试")
    print("=" * 50)
    
    tests = [
        ("多模态检测", test_multimodal_detection),
        ("图片上传模拟", test_image_upload_simulation),
        ("配置持久化", test_config_persistence),
        ("错误处理", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 运行测试: {test_name}")
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！多模态功能集成成功！")
        
        print("\n💡 使用建议：")
        print("1. 在前端AI服务设置中勾选'支持多模态(图片)'")
        print("2. 配置PICGO_API_KEY环境变量以启用图片上传")
        print("3. 使用支持视觉的模型如gpt-4-vision-preview")
        print("4. 上传图片文件时系统会自动选择最佳处理方式")
        
        return True
    else:
        print("❌ 部分测试失败，请检查配置和实现")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)