#!/usr/bin/env python3
"""
测试多模态功能的脚本
"""

import requests
import json

def test_multimodal_support():
    """测试多模态支持"""
    
    # 测试配置
    config_data = {
        "session_id": "test_session_123",
        "supports_vision": True
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "test_key",
        "X-Base-URL": "https://aistudio.baidu.com/llm/lmapi/v3",
        "X-Model": "ernie-4.5-vl-28b-a3b",
        "X-Use-Ollama": "false"
    }
    
    print("测试配置多模态支持...")
    response = requests.post(
        "http://localhost:8000/agent/v2/configure",
        json=config_data,
        headers=headers
    )
    
    print(f"配置响应: {response.status_code}")
    print(f"配置结果: {response.json()}")
    
    # 测试消息发送
    message_data = {
        "content": "测试多模态功能",
        "session_id": "test_session_123",
        "user_id": "test_user",
        "use_enhanced": True,
        "supports_vision": True,
        "files": []
    }
    
    print("\n测试发送消息...")
    response = requests.post(
        "http://localhost:8000/agent/v2/stream",
        json=message_data,
        headers=headers
    )
    
    print(f"消息响应: {response.status_code}")
    if response.status_code == 200:
        print("消息发送成功")
    else:
        print(f"消息发送失败: {response.text}")

if __name__ == "__main__":
    test_multimodal_support()