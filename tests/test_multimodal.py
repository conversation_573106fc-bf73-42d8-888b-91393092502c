#!/usr/bin/env python3
"""
多模态功能测试脚本
测试图片上传和多模态大模型处理功能
"""

import asyncio
import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from backend.core.agent.modules.agent_base import get_agent_executor
from backend.core.utils.picgo_util import get_picgo_util

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_picgo_upload():
    """测试图片上传功能"""
    print("=== 测试 PicGo 图片上传功能 ===")
    
    # 检查环境变量
    api_key = os.getenv('PICGO_API_KEY')
    if not api_key:
        print("错误：未设置 PICGO_API_KEY 环境变量")
        return False
    
    print(f"API Key: {api_key[:10]}...")
    
    # 创建测试图片（如果不存在）
    test_image_path = "test_image.png"
    if not os.path.exists(test_image_path):
        print("创建测试图片...")
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建一个简单的测试图片
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # 添加一些文字
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
        except:
            # 如果没有找到字体，使用默认字体
            font = ImageFont.load_default()
        
        draw.text((50, 50), "测试发票", fill='black', font=font)
        draw.text((50, 80), "发票号码: 12345678", fill='black', font=font)
        draw.text((50, 110), "金额: ¥1000.00", fill='black', font=font)
        draw.text((50, 140), "日期: 2024-01-01", fill='black', font=font)
        
        img.save(test_image_path)
        print(f"测试图片已创建: {test_image_path}")
    
    # 测试上传
    picgo_util = get_picgo_util()
    upload_result = picgo_util.upload_image(test_image_path)
    
    if upload_result:
        print(f"上传成功！")
        print(f"图片URL: {upload_result['url']}")
        print(f"删除URL: {upload_result['delete_url']}")
        
        # 测试删除
        print("\n测试删除图片...")
        delete_success = picgo_util.delete_image(upload_result['delete_url'])
        if delete_success:
            print("删除成功！")
        else:
            print("删除失败！")
        
        return True
    else:
        print("上传失败！")
        return False

async def test_multimodal_processing():
    """测试多模态处理功能"""
    print("\n=== 测试多模态处理功能 ===")
    
    # 检查是否有测试图片
    test_image_path = "test_image.png"
    if not os.path.exists(test_image_path):
        print("错误：测试图片不存在，请先运行图片上传测试")
        return False
    
    # 创建智能体执行器
    agent = get_agent_executor("test_session")
    
    # 配置一个支持多模态的模型（示例）
    # 注意：这里需要实际的API配置
    api_key = os.getenv('OPENAI_API_KEY')  # 或其他支持多模态的API
    if not api_key:
        print("警告：未设置多模态模型API密钥，跳过多模态测试")
        return False
    
    # 配置LLM（这里假设使用支持视觉的模型）
    success = agent.configure_llm(
        api_key=api_key,
        base_url="https://api.openai.com/v1",
        model="gpt-4-vision-preview"  # 或其他支持多模态的模型
    )
    
    if not success:
        print("LLM配置失败")
        return False
    
    # 测试多模态文件处理
    files = [{
        "file_path": test_image_path,
        "file_name": "test_image.png",
        "file_type": "image"
    }]
    
    try:
        result = await agent._process_files_with_ai(files, extraction_type="invoice")
        print("多模态处理结果：")
        print(result)
        return True
    except Exception as e:
        print(f"多模态处理失败: {e}")
        return False
    finally:
        # 清理上传的图片
        agent._cleanup_uploaded_images()

async def main():
    """主函数"""
    print("开始测试多模态功能...")
    
    # 测试图片上传
    upload_success = await test_picgo_upload()
    
    if upload_success:
        # 测试多模态处理
        await test_multimodal_processing()
    
    # 清理测试文件
    test_image_path = "test_image.png"
    if os.path.exists(test_image_path):
        os.remove(test_image_path)
        print(f"\n清理测试文件: {test_image_path}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    asyncio.run(main())