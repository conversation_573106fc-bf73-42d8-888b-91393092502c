#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 LMStudio 修复的脚本
"""

import asyncio
import aiohttp
import json
import sys
import os

# 添加后端路径到 Python 路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from core.agent.llm import CustomChatModel
from langchain_core.messages import HumanMessage, SystemMessage

async def test_lmstudio_connection():
    """测试 LMStudio 连接"""
    print("🧪 测试 LMStudio 连接修复...")
    
    # LMStudio 配置
    api_key = "not-needed"
    base_url = "http://localhost:1234/v1"  # 注意这里有 /v1
    model = "local-model"
    use_ollama = False
    
    print(f"配置信息:")
    print(f"  API Key: {api_key}")
    print(f"  Base URL: {base_url}")
    print(f"  Model: {model}")
    print(f"  Use Ollama: {use_ollama}")
    print()
    
    # 创建自定义聊天模型
    llm = CustomChatModel(
        api_key=api_key,
        base_url=base_url,
        model=model,
        use_ollama=use_ollama
    )
    
    # 测试消息
    messages = [
        SystemMessage(content="你是一个有帮助的AI助手。"),
        HumanMessage(content="你好，请简单介绍一下你自己。")
    ]
    
    try:
        print("📡 测试非流式请求...")
        result = await llm._agenerate(messages)
        print("✅ 非流式请求成功!")
        print(f"响应: {result.generations[0].message.content[:100]}...")
        print()
        
        print("📡 测试流式请求...")
        response_parts = []
        async for token in llm.astream(messages):
            response_parts.append(token)
            if len(response_parts) <= 5:  # 只打印前几个 token
                print(f"Token: {repr(token)}")
        
        full_response = ''.join(response_parts)
        print("✅ 流式请求成功!")
        print(f"完整响应: {full_response[:100]}...")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        
        # 如果是连接错误，提供帮助信息
        if "Connection" in str(e) or "refused" in str(e):
            print("\n💡 可能的解决方案:")
            print("1. 确保 LMStudio 正在运行")
            print("2. 确保 LMStudio 已启动本地服务器")
            print("3. 检查端口是否正确 (默认 1234)")
            print("4. 确保已加载模型")
        elif "Unexpected endpoint" in str(e):
            print("\n💡 API 端点问题:")
            print("1. 检查 LMStudio 是否支持 OpenAI 兼容 API")
            print("2. 确认端点路径是否正确")

async def test_api_endpoint():
    """直接测试 API 端点"""
    print("\n🔍 直接测试 LMStudio API 端点...")
    
    base_url = "http://localhost:1234"
    
    # 测试不同的端点
    endpoints = [
        f"{base_url}/v1/chat/completions",
        f"{base_url}/chat/completions",
        f"{base_url}/v1/models",
        f"{base_url}/models"
    ]
    
    async with aiohttp.ClientSession() as session:
        for endpoint in endpoints:
            try:
                print(f"测试端点: {endpoint}")
                async with session.get(endpoint, timeout=5) as response:
                    print(f"  状态码: {response.status}")
                    if response.status == 200:
                        try:
                            data = await response.json()
                            print(f"  响应: {json.dumps(data, indent=2)[:200]}...")
                        except:
                            text = await response.text()
                            print(f"  响应: {text[:200]}...")
                    else:
                        text = await response.text()
                        print(f"  错误: {text[:200]}...")
            except Exception as e:
                print(f"  连接失败: {str(e)}")
            print()

if __name__ == "__main__":
    print("🚀 LMStudio 修复测试脚本")
    print("=" * 50)
    
    # 运行测试
    asyncio.run(test_api_endpoint())
    asyncio.run(test_lmstudio_connection())
