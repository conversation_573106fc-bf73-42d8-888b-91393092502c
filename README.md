# 财务智能体系统

一个基于大语言模型的智能财务助手系统，支持多模态文档处理、会计凭证生成、单据审核等功能。

## ✨ 主要功能

- 🤖 **智能对话**: 基于大语言模型的财务咨询和问答
- 📄 **多模态文档处理**: 支持图片、PDF等多种格式的文档识别和分析
- 📊 **会计凭证生成**: 自动生成标准的会计凭证
- 🔍 **单据审核**: 智能审核各类财务单据的合规性
- 🎯 **智能纠错**: 结合OCR和视觉信息，提供更准确的文档识别

## 🚀 快速开始

### 1. 环境配置

```bash
# 克隆项目
git clone <repository-url>
cd account_2

# 安装依赖
pip install -r backend/requirements.txt

# 配置环境变量（交互式）
python scripts/setup_env.py

# 或手动复制配置文件
cp .env.development .env
# 编辑 .env 文件，填入实际的API密钥
```

### 2. 验证配置

```bash
# 检查环境变量配置
python scripts/check_env.py

# 测试PicGo配置（多模态功能）
python scripts/test_picgo.py
```

### 3. 启动服务

```bash
# 启动后端服务
cd backend
python main.py

# 启动前端（另一个终端）
cd frontend
npm install
npm start
```

## 🎯 多模态功能

系统支持多模态大模型，可以直接处理图片文件：

### 配置要求

1. **支持多模态的模型**: 如 `gpt-4-vision-preview`, `claude-3-opus` 等
2. **PicGo API Key**: 用于图床服务，提供公网可访问的图片URL

### 使用示例

```python
from backend.core.agent.modules.agent_base import get_agent_executor

# 创建智能体
agent = get_agent_executor()

# 配置多模态模型
agent.configure_llm(
    api_key="your_api_key",
    base_url="https://api.openai.com/v1",
    model="gpt-4-vision-preview"
)

# 处理图片文件
files = [{
    "file_path": "invoice.jpg",
    "file_name": "invoice.jpg",
    "file_type": "image"
}]

result = await agent._process_files_with_ai(files, extraction_type="invoice")
```

## 📁 项目结构

```
account_2/
├── backend/                 # 后端服务
│   ├── core/               # 核心模块
│   │   ├── agent/          # 智能体模块
│   │   ├── utils/          # 工具类
│   │   └── config.py       # 配置管理
│   ├── models/             # 数据模型
│   ├── api/                # API接口
│   └── requirements.txt    # Python依赖
├── frontend/               # 前端应用
├── docs/                   # 文档
├── scripts/                # 工具脚本
├── examples/               # 示例代码
├── .env.example           # 环境变量示例
├── .env.development       # 开发环境配置
├── .env.production        # 生产环境配置
└── .env.testing           # 测试环境配置
```

## 🔧 环境变量配置

### 核心配置

```bash
# 大语言模型配置
LLM_API_KEY=your_api_key_here
LLM_BASE_URL=https://api.openai.com/v1
LLM_MODEL=gpt-4

# 多模态功能（可选）
PICGO_API_KEY=your_picgo_api_key_here

# 数据库配置
DATABASE_URL=sqlite:///./app.db
```

### 配置文件说明

| 文件 | 用途 | 特点 |
|------|------|------|
| `.env.development` | 开发环境 | 使用较便宜的模型，启用调试 |
| `.env.production` | 生产环境 | 高性能模型，安全配置 |
| `.env.testing` | 测试环境 | 模拟服务，内存数据库 |

详细配置说明请参考 [环境配置指南](docs/ENVIRONMENT_SETUP.md)

## 🎨 功能特性

### 智能文档处理

- **OCR + 多模态**: 结合OCR识别和视觉理解，提供更准确的文档分析
- **智能纠错**: 自动纠正OCR识别中的错误和错别字
- **结构化提取**: 从发票、凭证等文档中提取结构化信息

### 会计业务支持

- **科目匹配**: 智能匹配合适的会计科目
- **凭证生成**: 自动生成符合会计准则的凭证
- **合规审核**: 基于财务法规进行单据审核

### 多模型支持

- **云端模型**: OpenAI GPT-4, Claude-3, Gemini等
- **本地模型**: 支持Ollama等本地部署方案
- **混合部署**: 可同时使用多种模型

## 🛠️ 开发指南

### 添加新功能

1. 在 `backend/core/agent/modules/` 下创建新模块
2. 实现相应的提示模板
3. 添加必要的工具和链式调用
4. 编写测试用例

### 自定义提示模板

```python
# 在 backend/core/agent/prompt_templates/ 下添加模板文件
# 在 backend/core/agent/prompts.py 中注册模板

prompt_manager.register_template(
    name="custom_template",
    template="your template content with {variables}",
    variables=["variables"],
    description="Template description"
)
```

### 扩展工具集

```python
from backend.core.agent.tools import register_tool

@register_tool("custom_tool")
async def custom_tool(param1: str, param2: int) -> dict:
    """自定义工具函数"""
    # 实现工具逻辑
    return {"result": "success"}
```

## 🧪 测试

```bash
# 运行单元测试
python -m pytest tests/

# 测试多模态功能
python examples/multimodal_example.py

# 测试环境配置
python scripts/check_env.py
```

## 📚 文档

- [环境配置指南](docs/ENVIRONMENT_SETUP.md)
- [多模态功能文档](docs/MULTIMODAL_SUPPORT.md)
- [实现总结](docs/MULTIMODAL_IMPLEMENTATION_SUMMARY.md)
- [Docker部署指南](DOCKER_GUIDE.md)

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 支持

如果遇到问题：

1. 查看 [文档](docs/) 目录下的相关指南
2. 运行 `python scripts/check_env.py` 检查配置
3. 查看 [Issues](../../issues) 页面
4. 提交新的 Issue 描述问题

## 🔄 更新日志

### v1.1.0 - 多模态支持
- ✨ 新增多模态大模型支持
- 🔧 优化OCR结合视觉信息的处理流程
- 📝 完善环境变量配置管理
- 🛠️ 添加配置验证和测试工具

### v1.0.0 - 初始版本
- 🎉 基础智能体框架
- 📄 文档处理功能
- 💼 会计凭证生成
- 🔍 单据审核系统