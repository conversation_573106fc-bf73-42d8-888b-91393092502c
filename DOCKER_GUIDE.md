# 会计应用 Docker 使用指南

本文档提供了如何使用 Docker 运行会计应用的详细说明。

## 前提条件

在开始之前，请确保您的系统已安装以下软件：

1. [Docker](https://docs.docker.com/get-docker/) - 版本 20.10 或更高
2. [Docker Compose](https://docs.docker.com/compose/install/) - 版本 2.0 或更高

## 快速开始

### 1. 克隆项目

```bash
git clone <项目仓库地址>
cd account_2
```

### 2. 启动服务

使用提供的启动脚本启动所有服务：

```bash
# 给启动脚本添加执行权限
chmod +x docker-start.sh

# 启动所有服务（后台运行）
./docker-start.sh up
```

或者使用 Docker Compose 直接启动：

```bash
docker compose up -d
```

### 3. 访问应用

服务启动后，您可以通过以下方式访问应用：

- **Web 前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **Electron 桌面应用**: 通过 Docker 容器运行，GUI 界面将显示在您的桌面上

## 服务架构

本项目使用 Docker Compose 编排以下服务：

1. **backend**: Python FastAPI 后端服务
2. **frontend**: React 前端应用（使用 Nginx 服务器）
3. **electron**: Electron 桌面应用

项目使用 SQLite 数据库，数据库文件存储在后端容器的 `/app/tmp` 目录中，并通过 Docker 卷持久化。Electron 应用提供了桌面版的用户界面，可以在本地系统上运行。

## 可用命令

### 使用启动脚本

```bash
# 启动 Web 服务（后台运行）
./docker-start.sh up

# 启动开发环境（前台运行，显示日志）
./docker-start.sh up-dev

# 启动 Electron 桌面应用
./docker-start.sh electron

# 启动所有服务包括 Electron 应用
./docker-start.sh all

# 停止所有服务
./docker-start.sh down

# 重启所有服务
./docker-start.sh restart

# 查看服务日志
./docker-start.sh logs

# 查看服务状态
./docker-start.sh status

# 清理所有容器、网络和卷（谨慎使用）
./docker-start.sh clean

# 重新构建并启动所有服务
./docker-start.sh rebuild

# 显示帮助信息
./docker-start.sh help
```

### 使用 Docker Compose

```bash
# 启动所有服务
docker compose up -d

# 停止所有服务
docker compose down

# 查看服务状态
docker compose ps

# 查看服务日志
docker compose logs -f

# 重启服务
docker compose restart

# 重新构建并启动服务
docker compose down
docker compose build --no-cache
docker compose up -d
```

## Electron 桌面应用

### 启动 Electron 应用

要启动 Electron 桌面应用，请使用以下命令：

```bash
# 启动 Electron 桌面应用
./docker-start.sh electron
```

或者启动所有服务包括 Electron 应用：

```bash
# 启动所有服务包括 Electron 应用
./docker-start.sh all
```

### Linux 系统特殊配置

在 Linux 系统上运行 Electron 应用需要额外的配置：

1. **允许容器访问 X11 服务器**：
   ```bash
   xhost +local:docker
   ```

2. **安装必要的依赖**：
   ```bash
   # Ubuntu/Debian
   sudo apt-get install \
       libx11-xcb1 \
       libxss1 \
       libnss3 \
       libasound2 \
       libxrandr2 \
       libpangocairo-1.0-0 \
       libatk1.0-0 \
       libgtk-3-0 \
       libgdk-pixbuf-2.0-0
   ```

### Electron 应用特点

- **桌面界面**: Electron 应用提供原生桌面体验，包括窗口控制、系统菜单等
- **离线运行**: 应用可以在没有网络连接的情况下运行
- **系统集成**: 应用可以与操作系统进行深度集成，如通知、文件访问等
- **自动更新**: 支持自动更新机制（需要配置）

### 故障排除

1. **无法显示 GUI**：
   - 确保 X11 服务器配置正确
   - 检查 DISPLAY 环境变量设置
   - 在 Linux 上运行 `xhost +local:docker`

2. **字体显示问题**：
   - 容器中可能缺少必要的字体文件
   - 可以挂载本地字体目录到容器中

3. **性能问题**：
   - Electron 应用在容器中运行可能会有性能损失
   - 考虑分配更多资源给 Docker 容器

## 开发环境设置

### 后端开发

如果您需要修改后端代码，可以直接编辑 `backend` 目录中的文件。由于使用了卷挂载，代码更改会自动反映到容器中。

```bash
# 查看后端日志
docker compose logs -f backend

# 进入后端容器
docker compose exec backend bash
```

### 前端开发

对于前端开发，建议在本地环境中运行开发服务器，而不是使用 Docker：

```bash
# 安装前端依赖
cd frontend
npm install

# 启动开发服务器
npm run dev
```

这将启动前端开发服务器，通常在 http://localhost:5173 上运行。

## 数据持久化

### 数据库数据

SQLite 数据库文件存储在 Docker 卷 `sqlite_data` 中，挂载到后端容器的 `/app/tmp` 目录。即使容器被删除，数据也会保留。

### 上传文件

用户上传的文件存储在 `backend/uploaded_files` 目录中，该目录已挂载到容器中。

### 日志文件

应用日志存储在 `backend/logs` 目录中，该目录已挂载到容器中。

## 环境变量配置

您可以通过创建 `.env` 文件来自定义环境变量：

```bash
# 创建 .env 文件
cp .env.example .env  # 如果存在示例文件
```

然后在 `.env` 文件中添加您的配置：

```env
# 后端配置
PYTHONPATH=/app
LOG_LEVEL=INFO
```

## 故障排除

### 常见问题

1. **端口冲突**
   - 如果端口 3000 或 8000 已被占用，您可以修改 `docker-compose.yml` 文件中的端口映射。

2. **权限问题**
   - 确保 `docker-start.sh` 脚本有执行权限：`chmod +x docker-start.sh`
   - 如果遇到文件权限问题，可能需要调整文件或目录的权限。

3. **构建失败**
   - 如果构建过程中出现错误，可以尝试重新构建：
     ```bash
     ./docker-start.sh rebuild
     ```

4. **服务启动失败**
   - 查看服务日志以获取更多信息：
     ```bash
     ./docker-start.sh logs
     ```

5. **Electron 应用无法显示 GUI**
   - 在 Linux 系统上，确保已运行 `xhost +local:docker` 命令
   - 检查 DISPLAY 环境变量是否正确设置
   - 确保系统已安装必要的图形库依赖

6. **Electron 应用性能问题**
   - Electron 应用在容器中运行可能会有性能损失
   - 尝试分配更多资源给 Docker 容器
   - 考虑在本地环境直接运行 Electron 应用而不是在容器中

### 查看日志

```bash
# 查看所有服务日志
docker compose logs

# 查看特定服务日志
docker compose logs backend
docker compose logs frontend
docker compose logs electron
```

### 重置环境

如果需要完全重置环境（**注意：这将删除所有数据**）：

```bash
./docker-start.sh clean
```

## 生产环境部署

对于生产环境部署，建议进行以下调整：

1. **安全配置**
   - 使用环境变量存储敏感信息
   - 限制 SQLite 数据库文件的外部访问

2. **性能优化**
   - 调整资源限制
   - 使用多阶段构建减小镜像大小
   - 配置适当的缓存策略

3. **监控和日志**
   - 集成日志收集系统
   - 设置健康检查
   - 配置监控指标

## 贡献

如果您对 Docker 配置有任何改进建议，请提交 Issue 或 Pull Request。

## 许可证

本项目遵循与主项目相同的许可证。