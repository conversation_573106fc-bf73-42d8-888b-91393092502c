services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: accounting-backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./backend/uploaded_files:/app/uploaded_files
      - ./backend/logs:/app/logs
      - sqlite_data:/app/tmp
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
    networks:
      - accounting-network
    restart: unless-stopped

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: accounting-frontend
    ports:
      - "3000:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - accounting-network
    restart: unless-stopped
    environment:
      - CHOKIDAR_USEPOLLING=true

  # Electron 应用服务
  electron:
    build:
      context: .
      dockerfile: electron/Dockerfile
    container_name: accounting-electron
    depends_on:
      - backend
      - frontend
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    environment:
      - DISPLAY=${DISPLAY}
    networks:
      - accounting-network
    restart: unless-stopped
    # 注意：Electron 应用在 Docker 中需要特殊的配置才能显示 GUI
    # 在 Linux 上，您可能需要运行 xhost +local:docker 来允许容器访问 X11 服务器

volumes:
  sqlite_data:

networks:
  accounting-network:
    driver: bridge