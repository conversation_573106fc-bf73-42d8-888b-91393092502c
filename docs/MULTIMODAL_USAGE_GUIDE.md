# 多模态功能使用指南

## 快速开始

### 1. 前端配置

在前端AI服务设置中：

1. 打开设置页面
2. 选择或添加AI服务
3. ✅ 勾选 **"支持多模态（图片）"** 选项
4. 选择支持视觉的模型（如 `gpt-4-vision-preview`）
5. 保存配置

### 2. 环境变量配置

在 `.env` 文件中添加：

```bash
# PicGo图床服务配置（必需）
PICGO_API_KEY=your_picgo_api_key_here
```

### 3. 使用多模态功能

1. **上传图片**：在聊天界面直接拖拽或点击上传图片
2. **自动处理**：系统会自动检测模型能力并选择最佳处理方式
3. **智能纠错**：多模态模型会结合OCR结果和视觉信息提供更准确的识别

## 支持的模型

### OpenAI 系列
- `gpt-4-vision-preview` ✅
- `gpt-4v` ✅
- `gpt-4-vision` ✅

### Claude 系列
- `claude-3-opus` ✅
- `claude-3-sonnet` ✅
- `claude-3-haiku` ✅

### Gemini 系列
- `gemini-pro-vision` ✅
- `gemini-1.5-pro` ✅
- `gemini-1.5-flash` ✅

## 处理流程对比

### 传统OCR流程
```
图片 → OCR识别 → 文本分析 → AI处理 → 结果
```

### 多模态流程
```
图片 → OCR预处理 → 上传图床 → 多模态AI(图片+OCR) → 智能纠错 → 结果
```

## 功能特性

### ✅ 智能纠错
- 纠正OCR识别的错别字
- 修正数字识别错误
- 补充遗漏的信息

### ✅ 结构理解
- 识别表格结构
- 理解文档布局
- 处理复杂格式

### ✅ 视觉元素
- 识别印章和签名
- 处理图表和图形
- 理解视觉关系

## 使用示例

### 发票处理
```javascript
// 前端配置
const aiService = {
  name: "GPT-4 Vision",
  type: "openai_compatible",
  supportsMultimodal: true,  // 关键配置
  config: {
    model: "gpt-4-vision-preview",
    apiKey: "your-api-key",
    baseUrl: "https://api.openai.com/v1"
  }
}
```

### 后端处理
```python
# 系统会自动检测并使用多模态处理
files = [{
    "file_path": "invoice.jpg",
    "file_name": "invoice.jpg", 
    "file_type": "image"
}]

result = await agent._process_files_with_ai(files, extraction_type="invoice")
```

## 故障排除

### 问题1：多模态功能未启用
**症状**：上传图片后仍使用OCR处理

**解决方案**：
1. 检查前端是否勾选"支持多模态（图片）"
2. 确认模型名称包含支持多模态的关键词
3. 查看后端日志确认 `supports_vision=True`

### 问题2：图片上传失败
**症状**：提示"图片上传失败，回退到OCR处理"

**解决方案**：
1. 检查 `PICGO_API_KEY` 是否正确配置
2. 确认网络连接正常
3. 验证PicGo服务可用性

### 问题3：处理结果不准确
**症状**：多模态处理结果不如预期

**解决方案**：
1. 确保图片清晰度足够
2. 检查提示模板是否合适
3. 尝试不同的多模态模型

## 调试技巧

### 1. 查看日志
```bash
# 后端日志会显示处理流程
tail -f logs/app.log | grep -E "(多模态|multimodal|vision)"
```

### 2. 测试配置
```bash
# 运行集成测试
python backend/test_multimodal_integration.py

# 测试PicGo配置
python scripts/test_picgo.py
```

### 3. 检查配置
```bash
# 验证环境变量
python scripts/check_env.py
```

## 性能优化

### 1. 图片优化
- 推荐分辨率：1024x1024 以下
- 支持格式：JPG, PNG, GIF, BMP
- 文件大小：建议 < 5MB

### 2. 成本控制
- 多模态模型调用成本较高
- 建议对重要文档使用多模态
- 普通文档可使用OCR处理

### 3. 缓存策略
- 系统会自动清理临时图片
- 相同图片会复用处理结果
- 定期清理上传目录

## 最佳实践

### 1. 模型选择
- **高精度需求**：使用 `gpt-4-vision-preview`
- **成本敏感**：使用 `claude-3-haiku`
- **速度优先**：使用 `gemini-1.5-flash`

### 2. 图片准备
- 确保图片清晰可读
- 避免过度压缩
- 保持正确方向

### 3. 提示优化
- 明确指定提取需求
- 提供上下文信息
- 使用结构化输出格式

## 安全注意事项

### 1. 数据隐私
- 图片会临时上传到第三方图床
- 处理完成后自动删除
- 敏感文档请谨慎使用

### 2. API安全
- 妥善保管API密钥
- 定期轮换密钥
- 监控使用量

### 3. 网络安全
- 使用HTTPS传输
- 验证图床服务安全性
- 定期更新依赖包

## 更新日志

### v1.1.0
- ✨ 新增多模态大模型支持
- 🔧 优化OCR结合视觉信息处理
- 📝 完善前端配置界面
- 🛠️ 添加集成测试工具

### 未来计划
- 🔄 支持更多多模态模型
- 📊 添加处理效果对比
- 🎯 优化成本和性能
- 🔒 增强安全和隐私保护