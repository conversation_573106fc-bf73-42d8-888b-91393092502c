# 多模态大模型支持文档

## 概述

本系统现已支持多模态大模型，可以直接处理图片文件而无需依赖OCR。当系统检测到配置的模型支持多模态时，会自动使用图片URL而非OCR文本进行处理。

## 功能特性

### 1. 自动模型检测
系统会自动检测当前配置的模型是否支持多模态：
- 支持的模型关键词：`vision`, `gpt-4v`, `gpt-4-vision`, `claude-3`, `gemini-pro-vision`
- 如果检测到支持多模态，会优先使用图片URL处理
- 如果不支持或处理失败，会自动回退到OCR处理

### 2. 图床服务集成
使用 picgo.net 图床服务提供公网可访问的图片URL：
- 自动上传本地图片到图床
- 获取公网可访问的URL
- 处理完成后自动清理图片

### 3. 多模态提示模板
针对不同的提取任务提供专门的多模态提示模板，支持OCR结果参考：
- `extract_invoice_fields_multimodal.txt` - 发票字段提取（结合OCR纠错）
- `extract_voucher_fields_multimodal.txt` - 凭证字段提取（结合OCR纠错）
- `extract_document_fields_multimodal.txt` - 通用文档字段提取（结合OCR纠错）

这些模板指导多模态模型：
- 参考OCR结果快速定位关键信息
- 使用视觉信息验证和纠正OCR错误
- 补充OCR可能遗漏的重要信息

## 配置说明

### 环境变量配置
在 `.env` 文件中添加以下配置：

```bash
# 图床服务配置（用于多模态大模型）
PICGO_API_KEY=your_picgo_api_key_here
```

### API Key 获取
1. 访问 [picgo.net](https://www.picgo.net)
2. 注册账号并获取API Key
3. 将API Key添加到环境变量中

## 使用方式

### 1. 自动处理
系统会自动检测模型能力并选择最佳处理方式：

```python
# 处理图片文件
files = [{
    "file_path": "path/to/image.jpg",
    "file_name": "invoice.jpg", 
    "file_type": "image"
}]

# 系统会自动选择多模态或OCR处理
result = await agent._process_files_with_ai(files, extraction_type="invoice")
```

### 2. 手动上传图片
也可以手动使用图床工具：

```python
from backend.core.utils.picgo_util import get_picgo_util

picgo_util = get_picgo_util()
upload_result = picgo_util.upload_image("path/to/image.jpg")

if upload_result:
    image_url = upload_result['url']
    delete_url = upload_result['delete_url']
    
    # 使用完成后删除
    picgo_util.delete_image(delete_url)
```

## 处理流程

### 多模态处理流程
1. **模型检测** - 检查当前模型是否支持多模态
2. **OCR预处理** - 首先使用OCR提取图片中的文本作为参考
3. **图片上传** - 将本地图片上传到图床获取URL
4. **多模态分析** - 使用图片URL和OCR结果调用多模态大模型
5. **智能纠错** - 多模态模型利用视觉信息纠正OCR错误
6. **结果解析** - 解析AI返回的结构化数据
7. **资源清理** - 删除图床中的临时图片

### 智能纠错机制
多模态模型结合OCR结果和视觉信息进行智能纠错：
1. **OCR参考** - 使用OCR结果快速定位文本区域
2. **视觉验证** - 通过图片内容验证OCR结果的准确性
3. **错误纠正** - 纠正OCR中的错别字、数字识别错误等
4. **信息补充** - 识别OCR可能遗漏的表格、印章、签名等

### 回退机制
如果多模态处理失败，系统会自动回退到纯OCR处理：
1. **OCR提取** - 使用OCR工具提取图片中的文本
2. **文本分析** - 使用文本内容调用AI模型
3. **结果返回** - 返回处理结果

## 支持的文件类型

### 图片文件
- JPG/JPEG
- PNG  
- GIF
- BMP
- TIFF

### PDF文件
PDF文件仍使用文本提取方式处理，不使用多模态功能。

## 错误处理

### 常见错误及解决方案

1. **API Key未配置**
   ```
   错误：未配置 PICGO_API_KEY，无法上传图片
   解决：在.env文件中添加PICGO_API_KEY配置
   ```

2. **图片上传失败**
   ```
   错误：图片上传失败
   解决：检查网络连接和API Key是否有效
   ```

3. **多模态模型调用失败**
   ```
   错误：调用多模态AI服务失败
   解决：系统会自动回退到OCR处理
   ```

## 性能优化

### 图片处理优化
- 自动生成随机文件名避免冲突
- 处理完成后立即清理临时图片
- 支持批量处理多个图片文件

### 内存管理
- 及时释放上传的图片资源
- 在会话结束时自动清理所有临时图片
- 析构函数确保资源清理

## 测试

### 运行测试脚本
```bash
cd backend
python test_multimodal.py
```

测试脚本会：
1. 测试图片上传功能
2. 测试多模态处理功能
3. 验证资源清理机制

### 手动测试
1. 配置支持多模态的模型（如gpt-4-vision-preview）
2. 设置PICGO_API_KEY环境变量
3. 上传包含图片的文件进行处理
4. 检查处理结果和资源清理

## 注意事项

1. **API Key安全**：确保API Key不会提交到版本控制系统
2. **网络依赖**：多模态功能需要稳定的网络连接
3. **成本控制**：多模态模型调用成本较高，建议合理使用
4. **隐私保护**：敏感图片会临时上传到第三方图床，请注意数据安全
5. **资源清理**：系统会自动清理临时图片，但建议定期检查

## 未来改进

1. **本地图床支持** - 支持本地部署的图床服务
2. **更多模型支持** - 扩展支持更多多模态大模型
3. **图片预处理** - 添加图片压缩和格式转换功能
4. **缓存机制** - 对相同图片的处理结果进行缓存