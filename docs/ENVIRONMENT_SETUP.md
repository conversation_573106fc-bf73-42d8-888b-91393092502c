# 环境变量配置指南

## 概述

本系统使用环境变量来管理各种配置，包括API密钥、数据库连接、服务配置等。我们提供了多个环境变量模板文件，适用于不同的部署场景。

## 环境变量文件

### 📁 可用的环境变量文件

| 文件名 | 用途 | 说明 |
|--------|------|------|
| `.env.example` | 通用示例 | 包含所有配置项的示例文件 |
| `.env.development` | 开发环境 | 适用于本地开发，使用较便宜的模型 |
| `.env.production` | 生产环境 | 适用于生产部署，使用高性能模型 |
| `.env.testing` | 测试环境 | 适用于自动化测试，使用模拟服务 |
| `.env.audit.example` | 审核系统 | 专门用于单据审核系统的配置 |

## 快速开始

### 1. 自动设置（推荐）

使用我们提供的交互式设置脚本：

```bash
# 运行交互式设置
python scripts/setup_env.py

# 查看帮助
python scripts/setup_env.py --help

# 列出所有模板
python scripts/setup_env.py --list

# 验证配置
python scripts/setup_env.py --validate
```

### 2. 手动设置

```bash
# 复制适合的模板文件
cp .env.development .env

# 编辑配置文件
nano .env

# 验证配置
python scripts/check_env.py
```

## 核心配置项

### 🔧 必需配置

这些配置项是系统运行的必需项：

```bash
# 大语言模型配置
LLM_API_KEY=your_api_key_here
LLM_BASE_URL=https://api.openai.com/v1
LLM_MODEL=gpt-4

# 数据库配置
DATABASE_URL=sqlite:///./app.db

# 日志配置
LOG_LEVEL=INFO
```

### 🎯 多模态功能配置

启用多模态大模型功能需要配置：

```bash
# PicGo图床服务（必需）
PICGO_API_KEY=your_picgo_api_key_here

# 使用支持多模态的模型
LLM_MODEL=gpt-4-vision-preview
```

### 🔌 可选配置

这些配置项可以增强系统功能：

```bash
# OpenAI服务
OPENAI_API_KEY=your_openai_key_here

# 搜索服务
SERPER_API_KEY=your_serper_key_here

# 天气服务
OPENWEATHER_API_KEY=your_weather_key_here

# 本地模型（Ollama）
OLLAMA_ENABLED=true
OLLAMA_MODEL=llama3.1:8b
```

## 环境特定配置

### 开发环境 (.env.development)

- 使用较便宜的模型（如 gpt-3.5-turbo）
- 启用本地Ollama模型
- 使用DEBUG日志级别
- 较小的文件上传限制

```bash
LLM_MODEL=gpt-3.5-turbo
OLLAMA_ENABLED=true
LOG_LEVEL=DEBUG
MAX_FILE_SIZE=5
```

### 生产环境 (.env.production)

- 使用高性能模型（如 gpt-4）
- 禁用本地模型
- 使用INFO日志级别
- 较大的文件上传限制
- 安全配置

```bash
LLM_MODEL=gpt-4
OLLAMA_ENABLED=false
LOG_LEVEL=INFO
MAX_FILE_SIZE=20
SECURE_SSL_REDIRECT=true
```

### 测试环境 (.env.testing)

- 使用内存数据库
- 最小化的配置
- 使用模拟服务

```bash
DATABASE_URL=sqlite:///:memory:
LLM_MODEL=gpt-3.5-turbo
PICGO_API_KEY=test_picgo_api_key
```

## API密钥获取

### 🔑 主要服务API密钥获取方法

| 服务 | 获取地址 | 用途 |
|------|----------|------|
| OpenAI | https://platform.openai.com/api-keys | 大语言模型、多模态模型 |
| PicGo | https://www.picgo.net | 图床服务（多模态功能必需） |
| Serper | https://serper.dev | 网络搜索服务 |
| OpenWeather | https://openweathermap.org/api | 天气信息服务 |

### 🎯 多模态功能专用

要启用多模态功能，需要：

1. **PicGo API Key**: 访问 [picgo.net](https://www.picgo.net) 注册获取
2. **支持视觉的模型**: 如 `gpt-4-vision-preview`, `claude-3-opus` 等

## 配置验证

### 使用检查脚本

```bash
# 检查默认的 .env 文件
python scripts/check_env.py

# 检查特定的环境文件
python scripts/check_env.py .env.development

# 检查所有配置项
python scripts/check_env.py .env.production
```

### 验证输出示例

```
🔍 财务智能体系统环境变量检查
========================================
✅ 已加载环境变量文件: .env

🔧 核心配置
==========
✅ 大语言模型API密钥: sk-proj-...
✅ LLM API地址: https://api.openai.com/v1
✅ LLM模型名称: gpt-4
✅ 数据库连接: sqlite:///./app.db
✅ 日志级别: INFO

🎯 多模态配置
============
✅ PicGo图床服务密钥: 12345678...

🤖 模型兼容性检查
================
当前模型: gpt-4-vision-preview
多模态支持: ✅ 支持
图床服务: ✅ 已配置
```

## 安全注意事项

### 🔒 保护敏感信息

1. **永远不要提交 .env 文件到版本控制**
   ```bash
   # .gitignore 已包含
   .env
   .env.*
   ```

2. **使用环境特定的密钥**
   - 开发环境使用测试密钥
   - 生产环境使用正式密钥

3. **定期轮换API密钥**

4. **限制API密钥权限**

### 🛡️ 生产环境安全配置

```bash
# 启用HTTPS重定向
SECURE_SSL_REDIRECT=true

# 启用HSTS
SECURE_HSTS_SECONDS=31536000

# 启用内容类型嗅探保护
SECURE_CONTENT_TYPE_NOSNIFF=true

# 启用XSS过滤器
SECURE_BROWSER_XSS_FILTER=true
```

## 故障排除

### 常见问题

1. **环境变量未加载**
   ```bash
   # 确保文件名正确
   ls -la .env*
   
   # 检查文件权限
   chmod 644 .env
   ```

2. **API密钥无效**
   ```bash
   # 验证密钥格式
   python scripts/check_env.py
   
   # 测试API连接
   curl -H "Authorization: Bearer $LLM_API_KEY" $LLM_BASE_URL/models
   ```

3. **多模态功能不工作**
   ```bash
   # 检查模型是否支持多模态
   python scripts/check_env.py
   
   # 验证PicGo配置
   python scripts/test_picgo.py
   ```

### 调试技巧

```bash
# 显示当前环境变量（隐藏敏感信息）
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
for key in sorted(os.environ.keys()):
    if any(x in key for x in ['KEY', 'PASSWORD', 'SECRET']):
        print(f'{key}=***')
    else:
        print(f'{key}={os.environ[key]}')
"
```

## 最佳实践

### 📋 配置管理

1. **使用版本控制管理模板文件**
2. **为不同环境使用不同的配置文件**
3. **定期验证配置的有效性**
4. **文档化自定义配置项**

### 🔄 部署流程

```bash
# 1. 选择合适的模板
cp .env.production .env

# 2. 填入实际配置
nano .env

# 3. 验证配置
python scripts/check_env.py

# 4. 启动服务
python main.py
```

### 📊 监控和维护

- 定期检查API配额使用情况
- 监控模型性能和成本
- 及时更新过期的API密钥
- 备份重要的配置文件

## 支持

如果在配置过程中遇到问题：

1. 查看 [故障排除](#故障排除) 部分
2. 运行 `python scripts/check_env.py` 进行诊断
3. 查看系统日志获取详细错误信息
4. 参考各服务的官方文档