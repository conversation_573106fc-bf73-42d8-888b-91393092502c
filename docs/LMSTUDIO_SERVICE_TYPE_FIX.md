# LMStudio 服务类型识别问题修复

## 问题描述

用户在前端配置使用 LMStudio 服务时，后台没有正确识别出服务类型，导致多模态功能无法正常工作。

从日志中可以看到：
- 前端配置时正确设置了 `service_type: lmstudio`
- 但在流式请求时，后台识别成了 `service_type: openai_compatible`

## 根本原因

前端代码中存在多个地方需要设置 AI 服务请求头，但不是所有地方都正确设置了 `X-Service-Type` 请求头：

1. **配置请求** (`/agent/v2/configure`) - ✅ 已正确设置
2. **流式请求** (`/agent/v2/stream`) - ❌ 缺少 `X-Service-Type`
3. **审核请求** (`/agent/v2/audit/stream`) - ❌ 缺少 `X-Service-Type`
4. **咨询请求** (`/agent/v2/consultation/stream`) - ❌ 缺少 `X-Service-Type`

## 修复方案

### 1. 创建统一的请求头工具函数

创建了 `frontend/src/utils/aiServiceHeaders.js` 文件，提供统一的请求头构建功能：

```javascript
export const buildAIServiceHeaders = (activeService, baseHeaders = {}) => {
  // 根据服务类型自动设置正确的请求头，包括 X-Service-Type
}
```

### 2. 更新前端组件

更新了以下组件来使用新的工具函数：

- `frontend/src/components/Agent.jsx`
  - `configureAgent()` 函数使用 `buildConfigureHeaders()`
  - `sendMessageWithContext()` 函数使用 `buildStreamHeaders()`

- `frontend/src/components/agent/invoice-audit/InvoiceAudit.jsx`
  - 使用 `buildAuditHeaders()`

- `frontend/src/components/agent/financial-consultation/FinancialConsultation.jsx`
  - 使用 `buildConsultationHeaders()`

### 3. 更新后端接口

更新了后端接口以正确处理 `X-Service-Type` 请求头：

- `backend/route/agent.py`
  - `/agent/v2/audit/stream` 接口添加 `x_service_type` 参数
  - `/agent/v2/consultation/stream` 接口添加 `x_service_type` 参数
  - 添加服务类型识别逻辑

## 修复效果

修复后，LMStudio 服务的请求头将正确包含：

```
X-API-Key: not-needed
X-Base-URL: http://192.168.2.102:1234
X-Model: local-model
X-Use-Ollama: false
X-Service-Type: lmstudio  // 关键修复点
```

后端将能够正确识别服务类型为 `lmstudio`，从而启用相应的多模态处理逻辑。

## 代码改进

### 优点
1. **统一管理**: 所有请求头构建逻辑集中在一个工具文件中
2. **减少重复**: 消除了大量重复的请求头设置代码
3. **易于维护**: 未来修改请求头逻辑只需要修改一个地方
4. **类型安全**: 提供了配置验证功能

### 工具函数特性
- `buildAIServiceHeaders()` - 核心请求头构建函数
- `buildConfigureHeaders()` - 配置请求专用
- `buildStreamHeaders()` - 流式请求专用
- `buildAuditHeaders()` - 审核请求专用
- `buildConsultationHeaders()` - 咨询请求专用
- `validateAIServiceForHeaders()` - 配置验证
- `buildAIServiceHeadersSafe()` - 安全构建（包含验证）

## 测试验证

修复后应该能看到正确的日志输出：

```
[LMSTUDIO_DEBUG] 从请求头获取服务类型: lmstudio
[LMSTUDIO_DEBUG] 检查是否为LMStudio服务: service_type=lmstudio, 结果=True
[LMSTUDIO_DEBUG] 是否为LMStudio服务: True
```

## 相关文件

### 新增文件
- `frontend/src/utils/aiServiceHeaders.js` - 请求头工具函数

### 修改文件
- `frontend/src/components/Agent.jsx`
- `frontend/src/components/agent/invoice-audit/InvoiceAudit.jsx`
- `frontend/src/components/agent/financial-consultation/FinancialConsultation.jsx`
- `backend/route/agent.py`

这个修复不仅解决了 LMStudio 服务类型识别问题，还大大改善了代码的可维护性和一致性。
