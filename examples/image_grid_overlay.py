#!/usr/bin/env python3
"""
图片网格覆盖脚本
在图片上覆盖16*16的网格，并在每个网格中添加编号
"""

import sys
import os
from PIL import Image, ImageDraw, ImageFont
import argparse


def add_grid_with_numbers(image_path, output_path=None, grid_size=None, font_size=None):
    """
    在图片上添加网格和编号
    
    Args:
        image_path: 输入图片路径
        output_path: 输出图片路径（可选，默认为在原文件名后添加'_grid'）
        grid_size: 网格大小（默认16x16）
        font_size: 字体大小（默认12）
    
    Returns:
        处理后的图片对象
    """
    # 检查文件是否存在
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"图片文件不存在: {image_path}")
    
    # 打开图片
    try:
        image = Image.open(image_path)
    except Exception as e:
        raise Exception(f"无法打开图片: {e}")
    
    # 转换为RGB模式（如果是RGBA或其他模式）
    if image.mode != 'RGB':
        image = image.convert('RGB')
    
    # 创建绘图对象
    draw = ImageDraw.Draw(image)
    
    # 获取图片尺寸
    width, height = image.size
    
    # 自适应网格大小：根据图片尺寸计算合适的网格数量
    if grid_size is None:
        # 基于图片的较小维度计算网格数量
        min_dimension = min(width, height)
        # 网格数量基于图片大小，但限制在4-32之间
        # 对于小图片（<500px）使用较少网格，大图片（>2000px）使用较多网格
        if min_dimension < 500:
            grid_size = 8
        elif min_dimension < 1000:
            grid_size = 12
        elif min_dimension < 2000:
            grid_size = 16
        else:
            grid_size = 20
        
        # 确保网格数量在合理范围内
        grid_size = max(4, min(32, grid_size))
        print(f"自适应网格数量: {grid_size}x{grid_size}")
    
    # 计算每个网格的宽度和高度
    cell_width = width / grid_size
    cell_height = height / grid_size
    
    # 自适应字体大小：根据网格大小计算合适的字体大小
    if font_size is None:
        # 基于网格的较小维度计算字体大小
        min_cell_size = min(cell_width, cell_height)
        # 字体大小约为网格大小的1/4，但限制在8-40之间
        font_size = max(8, min(40, int(min_cell_size / 4)))
        print(f"自适应字体大小: {font_size}")
    
    # 尝试加载字体，如果失败则使用默认字体
    try:
        font = ImageFont.truetype("arial.ttf", font_size)
        print(f"使用字体大小: {font_size}")
    except:
        try:
            # 尝试使用其他常见字体
            alternative_fonts = ["DejaVuSans.ttf", "Verdana.ttf", "Tahoma.ttf", "Arial.ttf"]
            font = None
            for font_name in alternative_fonts:
                try:
                    font = ImageFont.truetype(font_name, font_size)
                    print(f"使用替代字体: {font_name}, 大小: {font_size}")
                    break
                except:
                    continue
            
            # 如果所有字体都失败，使用默认字体
            if font is None:
                font = ImageFont.load_default()
                print("使用系统默认字体")
        except:
            font = None
            print("无法加载任何字体")
    
    # 绘制网格线（更浅的半透明）
    # 垂直线
    for i in range(1, grid_size):
        x = i * cell_width
        draw.line([(x, 0), (x, height)], fill=(200, 200, 200, 100), width=1)
    
    # 水平线
    for i in range(1, grid_size):
        y = i * cell_height
        draw.line([(0, y), (width, y)], fill=(200, 200, 200, 100), width=1)
    
    # 在每个网格中添加编号
    for row in range(grid_size):
        for col in range(grid_size):
            # 计算编号（从1开始）
            number = row * grid_size + col + 1
            
            # 计算文本位置（网格中心）
            x = col * cell_width + cell_width / 2
            y = row * cell_height + cell_height / 2
            
            # 准备文本
            text = str(number)
            
            # 获取文本边界框
            if font:
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
            else:
                # 如果没有字体，使用估算值
                if font_size is None:
                    font_size = 12  # 默认字体大小
                text_width = len(text) * font_size * 0.8
                text_height = font_size
            
            # 计算文本左上角坐标（居中）
            text_x = x - text_width / 2
            text_y = y - text_height / 2
            
            # 绘制文本背景（半透明，无边框）
            padding = 4
            # 创建一个新的透明图层用于背景
            overlay = Image.new('RGBA', image.size, (255, 255, 255, 0))
            overlay_draw = ImageDraw.Draw(overlay)
            
            # 在透明图层上绘制半透明背景
            overlay_draw.rectangle(
                [text_x - padding, text_y - padding,
                 text_x + text_width + padding, text_y + text_height + padding],
                fill=(255, 255, 255, 50)  # 白色半透明
            )
            
            # 将透明图层合并到原图上
            image = Image.alpha_composite(image.convert('RGBA'), overlay).convert('RGB')
            draw = ImageDraw.Draw(image)  # 重新创建绘图对象
            
            # 绘制文本
            draw.text((text_x, text_y), text, fill="black", font=font)
    
    # 设置输出路径
    if output_path is None:
        path_parts = os.path.splitext(image_path)
        output_path = f"{path_parts[0]}_grid{path_parts[1]}"
    
    # 保存图片
    try:
        image.save(output_path)
        print(f"处理后的图片已保存到: {output_path}")
    except Exception as e:
        raise Exception(f"保存图片失败: {e}")
    
    return image


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="在图片上添加16x16网格和编号")
    parser.add_argument("image_path", help="输入图片路径")
    parser.add_argument("-o", "--output", help="输出图片路径（可选）")
    parser.add_argument("-s", "--size", type=int, default=None,
                       help="网格大小（默认None）")
    parser.add_argument("-f", "--font-size", type=int, default=None,
                       help="字体大小（默认自适应，基于图片大小）")
    
    args = parser.parse_args()
    
    print("=== 图片网格覆盖工具 ===")
    print(f"输入图片: {args.image_path}")
    print(f"网格大小: {args.size}x{args.size}")
    print(f"字体大小: {args.font_size}")
    
    try:
        # 处理图片
        result_image = add_grid_with_numbers(
            args.image_path, 
            args.output, 
            args.size, 
            args.font_size
        )
        
        print("✅ 处理完成！")
        
        # 显示图片信息
        width, height = result_image.size
        print(f"图片尺寸: {width}x{height}")
        if args.size is not None:
            print(f"总网格数: {args.size * args.size}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 检查是否安装了Pillow库
    try:
        import PIL
    except ImportError:
        print("❌ 错误：未安装Pillow库")
        print("请使用以下命令安装：")
        print("pip install Pillow")
        sys.exit(1)
    
    main()