#!/usr/bin/env python3
"""
picgo.net 图床最小化示例
最简单的 picgo.net 图床使用方法，直接使用 requests 库上传图片
"""

import requests
import json
import sys
import os
import random
import string
from pathlib import Path

def generate_random_filename(original_path: str) -> str:
    """
    生成一个随机文件名，保留原始文件扩展名
    
    Args:
        original_path: 原始文件路径
        
    Returns:
        随机文件名
    """
    # 获取原始文件的扩展名
    ext = Path(original_path).suffix
    # 生成10位随机字符串
    random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
    # 组合成新的文件名
    return f"{random_str}{ext}"

def upload_image(image_path: str, api_key: str):
    """
    使用 API key 上传图片到 picgo.net 图床
    
    Args:
        image_path: 图片文件路径
        api_key: API key
        
    Returns:
        图片 URL
    """
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误：文件不存在 - {image_path}")
        return None
    
    # 检查是否提供了 API key
    if not api_key:
        print("错误：未提供 API key")
        return None
    
    # API 端点
    url = 'https://www.picgo.net/api/1/upload'
    
    try:
        # 生成随机文件名
        random_filename = generate_random_filename(image_path)
        print(f"使用随机文件名上传: {random_filename}")
        
        # 准备文件和请求头
        files = {'source': (random_filename, open(image_path, 'rb'))}
        data = {'title': random_filename, 'format': 'json'}
        headers = {'X-API-Key': api_key}
        
        # 发送请求
        r = requests.post(url, files=files, data=data, headers=headers)
        
        # 解析响应
        result = r.json()
        
        # 打印所有返回的字段
        print("所有返回的字段：")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 关闭文件
        files['source'][1].close()
        
        # 检查是否成功
        if 'status_code' in result and result['status_code'] == 200:
            if 'image' in result and 'url' in result['image']:
                image_url = result['image']['url']
                print(f'上传成功！')
                print(f'图片 URL => {image_url}')
                return image_url
            else:
                print(f'上传失败：响应中未包含图片 URL')
                return None
        else:
            error_msg = result.get('error', {}).get('message', '未知错误')
            print(f'上传失败：{error_msg}')
            return None
            
    except Exception as e:
        print(f'上传出错：{str(e)}')
        return None

def main():
    """主函数"""
    print("=== picgo.net 图床最小化示例 ===")
    print("注意：picgo.net 要求必须使用 API key 上传图片")
    
    # 获取图片路径
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
    else:
        image_path = input("请输入图片路径：").strip()
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误：文件不存在 - {image_path}")
        return
    
    # 获取 API key
    api_key = input("请输入 API key：").strip()
    
    if not api_key:
        print("错误：必须提供 API key")
        return
    
    # 上传图片
    upload_image(image_path, api_key)

if __name__ == "__main__":
    main()