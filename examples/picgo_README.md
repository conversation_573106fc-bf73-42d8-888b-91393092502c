# picgo.net 图床使用示例

本目录包含了使用 picgo.net 图床的示例代码，展示了如何使用 API key 上传图片。

## 文件说明

### picgo_minimal_example.py
最小化的 picgo.net 图床使用示例，直接使用 requests 库上传图片：
- 使用 API key 上传本地图片
- picgo.net 要求必须使用 API key 才能上传图片

#### 使用方法
```bash
# 交互式使用
python examples/picgo_minimal_example.py

# 直接指定图片路径
python examples/picgo_minimal_example.py /path/to/image.jpg
```

#### 代码示例
```python
import requests

url = 'https://www.picgo.net/api/1/upload'
files = {'source': open('demo.jpg', 'rb')}
headers = {'X-API-Key': 'YOUR_API_KEY'}
r = requests.post(url, files=files, headers=headers).json()
print('image_url =>', r['image']['url'])
```

## picgo.net 图床简介

picgo.net 是一个图片托管服务，提供简单的 API 接口用于图片上传和管理。

### API 端点
- 基础 URL: `https://www.picgo.net/api/1/upload`
- 上传图片: `/upload`

### 使用要求
- 必须使用 API key 才能上传图片
- 支持的图片格式：jpg, jpeg, png, gif, bmp, webp

## 依赖项

所有示例都需要以下 Python 包：
- `requests`

安装依赖：
```bash
pip install requests
```

## 注意事项

1. picgo.net 要求必须使用 API key 上传图片，无法以游客身份上传。
2. 上传的图片会被公开访问，请勿上传敏感或私密图片。
3. 请遵守 picgo.net 的服务条款和使用政策。
4. 示例中的临时文件会在上传完成后自动删除。

## 更多信息

- picgo.net 官方文档: https://www.picgo.net/