#!/usr/bin/env python3
"""
picgo.net 图床删除图片示例
使用 delete_url 删除已上传的图片
"""

import requests
import json
import sys

def delete_image(delete_url: str, api_key: str):
    """
    使用 delete_url 和 API key 删除图片
    
    Args:
        delete_url: 图片删除 URL
        api_key: API key
        
    Returns:
        删除是否成功
    """
    # 检查是否提供了 delete_url
    if not delete_url:
        print("错误：未提供删除 URL")
        return False
    
    # 检查是否提供了 API key
    if not api_key:
        print("错误：未提供 API key")
        return False
    
    try:
        # 准备请求头
        headers = {'X-API-Key': api_key}
        
        print(f"正在删除图片: {delete_url}")
        
        # 发送 DELETE 请求
        r = requests.delete(delete_url, headers=headers)
        
        # 检查 HTTP 状态码
        # 对于 DELETE 操作，200 和 404 都可能表示成功
        # 200 = 成功删除并返回响应
        # 404 = 成功删除，资源不再存在（常见模式）
        if r.status_code in [200, 404]:
            if r.status_code == 200:
                print('删除成功！')
            else:  # 404
                print('删除成功！图片已不存在（404）')
            
            # 尝试解析响应内容（如果有的话）
            if r.text and r.text.strip():
                try:
                    result = r.json()
                    # 打印所有返回的字段
                    print("所有返回的字段：")
                    print(json.dumps(result, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    # 如果不是 JSON 格式，直接打印原始响应
                    print("响应内容（非 JSON 格式）：")
                    print(r.text)
            else:
                print("服务器返回空响应")
            
            return True
        else:
            # 尝试解析错误响应
            try:
                result = r.json()
                error_msg = result.get('error', {}).get('message', '未知错误')
                print(f'删除失败：{error_msg}')
            except json.JSONDecodeError:
                # 如果错误响应不是 JSON 格式
                print(f'删除失败：HTTP {r.status_code} - {r.text}')
            return False
            
    except Exception as e:
        print(f'删除出错：{str(e)}')
        return False

def main():
    """主函数"""
    print("=== picgo.net 图床删除图片示例 ===")
    print("注意：picgo.net 要求必须使用 API key 删除图片")
    
    # 获取删除 URL
    if len(sys.argv) > 1:
        delete_url = sys.argv[1]
    else:
        delete_url = input("请输入图片删除 URL：").strip()
    
    if not delete_url:
        print("错误：必须提供删除 URL")
        return
    
    # 获取 API key
    api_key = input("请输入 API key：").strip()
    
    if not api_key:
        print("错误：必须提供 API key")
        return
    
    # 删除图片
    delete_image(delete_url, api_key)

if __name__ == "__main__":
    main()