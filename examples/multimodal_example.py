#!/usr/bin/env python3
"""
多模态大模型使用示例
展示如何使用多模态功能处理发票图片
"""

import asyncio
import sys
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from backend.core.agent.modules.agent_base import get_agent_executor

async def process_invoice_with_multimodal():
    """使用多模态大模型处理发票图片的示例"""
    
    print("=== 多模态发票处理示例 ===")
    
    # 创建智能体执行器
    agent = get_agent_executor("multimodal_example")
    
    # 配置支持多模态的模型
    # 注意：需要配置实际的API密钥和支持视觉的模型
    success = agent.configure_llm(
        api_key="your_api_key_here",  # 替换为实际的API密钥
        base_url="https://api.openai.com/v1",
        model="gpt-4-vision-preview"  # 或其他支持多模态的模型
    )
    
    if not success:
        print("❌ LLM配置失败，请检查API密钥和模型配置")
        return
    
    # 准备测试文件信息
    files = [{
        "file_path": "path/to/your/invoice.jpg",  # 替换为实际的发票图片路径
        "file_name": "invoice.jpg",
        "file_type": "image"
    }]
    
    try:
        print("🔍 开始处理发票图片...")
        print("📋 处理流程：")
        print("   1. 检测模型是否支持多模态")
        print("   2. 使用OCR提取文本作为参考")
        print("   3. 上传图片到图床获取URL")
        print("   4. 调用多模态大模型进行智能分析")
        print("   5. 结合OCR结果和视觉信息纠错")
        print("   6. 返回结构化的发票信息")
        
        # 使用多模态AI处理文件
        result = await agent._process_files_with_ai(files, extraction_type="invoice")
        
        print("\n✅ 处理完成！")
        print("📊 提取结果：")
        print(result)
        
        print("\n🎯 多模态优势：")
        print("   • 结合OCR结果快速定位信息")
        print("   • 使用视觉信息纠正OCR错误")
        print("   • 识别OCR难以处理的表格结构")
        print("   • 补充遗漏的重要信息")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
    
    finally:
        # 清理资源
        agent._cleanup_uploaded_images()
        print("\n🧹 已清理临时图片资源")

async def compare_ocr_vs_multimodal():
    """对比OCR和多模态处理的差异"""
    
    print("\n=== OCR vs 多模态对比示例 ===")
    
    agent = get_agent_executor("comparison_example")
    
    # 配置普通模型（不支持多模态）
    agent.configure_llm(
        api_key="your_api_key_here",
        base_url="https://api.openai.com/v1", 
        model="gpt-3.5-turbo"  # 不支持多模态
    )
    
    files = [{
        "file_path": "path/to/your/invoice.jpg",
        "file_name": "invoice.jpg",
        "file_type": "image"
    }]
    
    print("🔄 使用纯OCR处理...")
    ocr_result = await agent._process_files_with_ai(files, extraction_type="invoice")
    
    # 配置多模态模型
    agent.configure_llm(
        api_key="your_api_key_here",
        base_url="https://api.openai.com/v1",
        model="gpt-4-vision-preview"
    )
    
    print("🔄 使用多模态处理...")
    multimodal_result = await agent._process_files_with_ai(files, extraction_type="invoice")
    
    print("\n📊 对比结果：")
    print("OCR处理结果：")
    print(ocr_result)
    print("\n多模态处理结果：")
    print(multimodal_result)
    
    print("\n💡 预期差异：")
    print("   • 多模态可能纠正OCR的数字识别错误")
    print("   • 多模态能更好地理解表格结构")
    print("   • 多模态可以识别印章、签名等视觉元素")
    print("   • 多模态对模糊文字有更好的识别能力")

def main():
    """主函数"""
    print("🚀 多模态大模型功能演示")
    print("📝 注意：运行前请确保：")
    print("   1. 配置了 PICGO_API_KEY 环境变量")
    print("   2. 配置了支持多模态的大模型API")
    print("   3. 准备了测试用的发票图片")
    print("   4. 网络连接正常")
    
    # 运行示例
    asyncio.run(process_invoice_with_multimodal())
    # asyncio.run(compare_ocr_vs_multimodal())  # 取消注释以运行对比示例

if __name__ == "__main__":
    main()