#!/usr/bin/env python3
"""
图片网格覆盖使用示例
演示如何使用 image_grid_overlay.py 脚本
"""

import os
import sys
from pathlib import Path

def create_sample_image():
    """创建一个示例图片用于测试"""
    try:
        from PIL import Image, ImageDraw
        
        # 创建一个400x400的示例图片
        width, height = 400, 400
        image = Image.new('RGB', (width, height), color='lightblue')
        draw = ImageDraw.Draw(image)
        
        # 在图片上绘制一些内容
        draw.rectangle([50, 50, 350, 350], outline='darkblue', width=3)
        draw.ellipse([100, 100, 300, 300], outline='red', width=2)
        draw.text((150, 180), "Sample Image", fill='black')
        
        # 保存示例图片
        sample_path = "sample_image.jpg"
        image.save(sample_path)
        print(f"✅ 示例图片已创建: {sample_path}")
        return sample_path
        
    except ImportError:
        print("❌ 错误：未安装Pillow库")
        print("请使用以下命令安装：")
        print("pip install Pillow")
        return None
    except Exception as e:
        print(f"❌ 创建示例图片失败: {e}")
        return None

def demonstrate_usage():
    """演示如何使用网格覆盖工具"""
    print("=== 图片网格覆盖使用示例 ===\n")
    
    # 检查主脚本是否存在
    main_script = "image_grid_overlay.py"
    if not os.path.exists(main_script):
        print(f"❌ 错误：找不到主脚本 {main_script}")
        print("请确保 image_grid_overlay.py 在同一目录下")
        return
    
    # 创建示例图片
    print("1. 创建示例图片...")
    sample_image = create_sample_image()
    if not sample_image:
        return
    
    print("\n2. 使用方法演示：")
    
    # 方法1：命令行参数方式
    print("\n方法1：使用命令行参数")
    print("```bash")
    print(f"# 基本用法（16x16网格，默认字体大小）")
    print(f"python {main_script} {sample_image}")
    print(f"\n# 指定输出路径")
    print(f"python {main_script} {sample_image} -o output_image.jpg")
    print(f"\n# 自定义网格大小（8x8）")
    print(f"python {main_script} {sample_image} -s 8")
    print(f"\n# 自定义字体大小")
    print(f"python {main_script} {sample_image} -f 16")
    print("```")
    
    # 方法2：Python模块导入方式
    print("\n方法2：作为Python模块导入")
    print("```python")
    print("from image_grid_overlay import add_grid_with_numbers")
    print("")
    print("# 基本用法")
    print('result = add_grid_with_numbers("input.jpg")')
    print("")
    print("# 自定义参数")
    print('result = add_grid_with_numbers(')
    print('    "input.jpg",')
    print('    output_path="output.jpg",')
    print('    grid_size=8,')
    print('    font_size=14')
    print(')')
    print("```")
    
    # 实际演示
    print("\n3. 实际演示：")
    print("正在处理示例图片...")
    
    # 导入并使用主脚本中的函数
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    try:
        from image_grid_overlay import add_grid_with_numbers
        
        # 使用默认参数处理
        result = add_grid_with_numbers(sample_image, "demo_output_16x16.jpg")
        print("✅ 16x16网格处理完成")
        
        # 使用自定义参数处理
        result = add_grid_with_numbers(
            sample_image,
            "demo_output_custom.jpg",
            grid_size=8,
            font_size=30
        )
        print("✅ 8x8网格处理完成")
        
        print("\n📁 生成的文件：")
        print("   - demo_output_16x16.jpg (16x16网格)")
        print("   - demo_output_8x8.jpg (8x8网格)")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")

def show_advanced_examples():
    """显示高级用法示例"""
    print("\n=== 高级用法示例 ===\n")
    
    print("1. 批量处理多张图片：")
    print("```python")
    print("import os")
    print("from image_grid_overlay import add_grid_with_numbers")
    print("")
    print("# 处理目录中的所有图片")
    print("input_dir = 'images/'")
    print("output_dir = 'output/'")
    print("")
    print("for filename in os.listdir(input_dir):")
    print("    if filename.lower().endswith(('.png', '.jpg', '.jpeg')):")
    print("        input_path = os.path.join(input_dir, filename)")
    print("        output_path = os.path.join(output_dir, f'grid_{filename}')")
    print("        ")
    print("        try:")
    print("            add_grid_with_numbers(input_path, output_path)")
    print("            print(f'处理完成: {filename}')")
    print("        except Exception as e:")
    print("            print(f'处理失败 {filename}: {e}')")
    print("```")
    
    print("\n2. 不同网格大小的效果对比：")
    print("```python")
    print("from image_grid_overlay import add_grid_with_numbers")
    print("")
    print("# 同一张图片生成不同网格大小的版本")
    print("image_path = 'sample.jpg'")
    print("")
    print("# 4x4 网格")
    print("add_grid_with_numbers(image_path, 'grid_4x4.jpg', grid_size=4)")
    print("")
    print("# 8x8 网格")
    print("add_grid_with_numbers(image_path, 'grid_8x8.jpg', grid_size=8)")
    print("")
    print("# 16x16 网格")
    print("add_grid_with_numbers(image_path, 'grid_16x16.jpg', grid_size=16)")
    print("")
    print("# 32x32 网格")
    print("add_grid_with_numbers(image_path, 'grid_32x32.jpg', grid_size=32)")
    print("```")

def main():
    """主函数"""
    print("🚀 图片网格覆盖工具使用示例")
    print("=" * 50)
    
    # 检查依赖
    try:
        import PIL
        print("✅ Pillow库已安装")
    except ImportError:
        print("❌ 错误：未安装Pillow库")
        print("请使用以下命令安装：")
        print("pip install Pillow")
        return
    
    # 演示基本用法
    demonstrate_usage()
    
    # 显示高级示例
    show_advanced_examples()
    
    print("\n" + "=" * 50)
    print("📚 更多信息：")
    print("   - 查看 image_grid_overlay.py 了解完整功能")
    print("   - 使用 --help 参数查看所有可用选项")
    print("   - 支持的图片格式：JPG, PNG, BMP, GIF等")

if __name__ == "__main__":
    main()