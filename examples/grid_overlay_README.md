# 图片网格覆盖工具

这个工具可以在图片上添加网格线和编号，将图片分割成指定大小的网格，并在每个网格中显示编号。

## 功能特点

- 📐 在图片上绘制自定义大小的网格（默认16x16）
- 🔢 在每个网格中添加编号（从1开始）
- 🎨 自适应字体大小和网格数量（基于图片大小），半透明网格线和背景
- 🖼️ 支持多种图片格式（JPG, PNG, BMP, GIF等）
- ⚙️ 可自定义网格大小、字体大小等参数
- 💻 支持命令行和Python模块两种使用方式

## 文件说明

### `image_grid_overlay.py`
主要的网格覆盖脚本，包含核心功能：
- `add_grid_with_numbers()` 函数：在图片上添加网格和编号
- 命令行接口：支持多种参数选项

### `grid_overlay_example.py`
使用示例脚本，演示如何使用网格覆盖工具：
- 创建示例图片
- 演示命令行使用方法
- 演示Python模块导入使用方法
- 提供高级用法示例

## 安装依赖

在使用前，请确保安装了Pillow库：

```bash
pip install Pillow
```

## 使用方法

### 方法1：命令行使用

基本语法：
```bash
python image_grid_overlay.py <图片路径> [选项]
```

#### 基本用法
```bash
# 使用默认参数（16x16网格）
python image_grid_overlay.py input.jpg

# 指定输出路径
python image_grid_overlay.py input.jpg -o output.jpg

# 自定义网格大小（可选，默认会自适应）
python image_grid_overlay.py input.jpg -s 10

# 自定义字体大小（可选，默认会自适应）
python image_grid_overlay.py input.jpg -f 30

# 组合使用多个参数
python image_grid_overlay.py input.jpg -o custom_output.jpg -s 10 -f 14
```

#### 命令行参数
- `image_path`: 输入图片路径（必需）
- `-o, --output`: 输出图片路径（可选，默认在原文件名后添加'_grid'）
- `-s, --size`: 网格大小（可选，默认自适应，基于图片大小）
- `-f, --font-size`: 字体大小（可选，默认自适应，基于图片大小）
- `-h, --help`: 显示帮助信息

### 方法2：Python模块导入

```python
from image_grid_overlay import add_grid_with_numbers

# 基本用法
result = add_grid_with_numbers("input.jpg")

# 自定义参数
result = add_grid_with_numbers(
    "input.jpg",
    output_path="output.jpg",
    grid_size=10,  # 可选，默认会自适应
    font_size=30  # 可选，默认会自适应
)
```

#### 函数参数
- `image_path`: 输入图片路径（必需）
- `output_path`: 输出图片路径（可选）
- `grid_size`: 网格大小（可选，默认16）
- `font_size`: 字体大小（可选，默认12）

## 运行示例

### 运行示例脚本
```bash
# 运行完整示例（会创建示例图片并演示各种用法）
python grid_overlay_example.py
```

### 快速测试
```bash
# 创建示例图片并处理
python grid_overlay_example.py

# 或者使用你自己的图片
python image_grid_overlay.py your_image.jpg
```

## 高级用法

### 批量处理多张图片
```python
import os
from image_grid_overlay import add_grid_with_numbers

# 处理目录中的所有图片
input_dir = 'images/'
output_dir = 'output/'

for filename in os.listdir(input_dir):
    if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
        input_path = os.path.join(input_dir, filename)
        output_path = os.path.join(output_dir, f'grid_{filename}')
        
        try:
            add_grid_with_numbers(input_path, output_path)
            print(f'处理完成: {filename}')
        except Exception as e:
            print(f'处理失败 {filename}: {e}')
```

### 不同网格大小的效果对比
```python
from image_grid_overlay import add_grid_with_numbers

# 同一张图片生成不同网格大小的版本
image_path = 'sample.jpg'

# 4x4 网格
add_grid_with_numbers(image_path, 'grid_4x4.jpg', grid_size=4)

# 8x8 网格
add_grid_with_numbers(image_path, 'grid_8x8.jpg', grid_size=8)

# 16x16 网格
add_grid_with_numbers(image_path, 'grid_16x16.jpg', grid_size=16)

# 32x32 网格
add_grid_with_numbers(image_path, 'grid_32x32.jpg', grid_size=32)
```

## 输出示例

处理后的图片将包含：
- 黑色网格线将图片分割成指定大小的网格
- 每个网格中心有一个编号（从1开始，从左到右、从上到下递增）
- 编号有白色背景框，提高可读性

## 支持的图片格式

- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- GIF (.gif)
- 其他Pillow库支持的格式

## 注意事项

1. **字体支持**：脚本会尝试使用系统字体，如果失败则使用默认字体
2. **图片大小**：对于非常大的图片，处理时间可能会增加
3. **网格密度**：网格数量过多时，编号可能会重叠，建议适当调整字体大小
4. **输出格式**：输出图片格式与输入格式相同，除非明确指定不同的扩展名

## 故障排除

### 常见错误

1. **"未安装Pillow库"**
   ```bash
   pip install Pillow
   ```

2. **"图片文件不存在"**
   - 检查图片路径是否正确
   - 确保图片文件存在

3. **"无法打开图片"**
   - 确保图片格式受支持
   - 检查图片文件是否损坏

4. **"保存图片失败"**
   - 检查输出目录是否有写入权限
   - 确保磁盘空间充足

### 性能优化建议

1. 对于大图片，可以先调整大小再添加网格
2. 批量处理时，建议使用较小的网格大小以提高处理速度
3. 如果不需要高精度，可以适当降低输出图片质量

## 示例输出

运行示例脚本后，你将看到类似以下的输出：

```
=== 图片网格覆盖使用示例 ===

1. 创建示例图片...
✅ 示例图片已创建: sample_image.jpg

2. 使用方法演示：

方法1：使用命令行参数
```bash
# 基本用法（16x16网格，默认字体大小）
python image_grid_overlay.py sample_image.jpg

# 指定输出路径
python image_grid_overlay.py sample_image.jpg -o output_image.jpg

# 自定义网格大小（8x8）
python image_grid_overlay.py sample_image.jpg -s 8

# 自定义字体大小
python image_grid_overlay.py sample_image.jpg -f 16
```

3. 实际演示：
正在处理示例图片...
✅ 16x16网格处理完成
✅ 8x8网格处理完成

📁 生成的文件：
   - demo_output_16x16.jpg (16x16网格)
   - demo_output_8x8.jpg (8x8网格)
```

## 贡献

如果你有任何改进建议或发现了bug，欢迎提交issue或pull request。