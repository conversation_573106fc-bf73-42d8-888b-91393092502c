# 财务智能体系统 Makefile

.PHONY: help install setup check test clean start-backend start-frontend dev

# 默认目标
help:
	@echo "财务智能体系统 - 可用命令："
	@echo ""
	@echo "🚀 快速开始："
	@echo "  make quick-start    - 快速启动和配置系统"
	@echo "  make install        - 安装所有依赖"
	@echo "  make setup          - 设置环境变量"
	@echo ""
	@echo "🔧 配置管理："
	@echo "  make check          - 检查环境变量配置"
	@echo "  make check-dev      - 检查开发环境配置"
	@echo "  make check-prod     - 检查生产环境配置"
	@echo ""
	@echo "🧪 测试："
	@echo "  make test           - 运行所有测试"
	@echo "  make test-env       - 测试环境配置"
	@echo "  make test-picgo     - 测试PicGo配置"
	@echo "  make test-multimodal - 测试多模态功能"
	@echo ""
	@echo "🏃 运行服务："
	@echo "  make dev            - 启动开发环境"
	@echo "  make start-backend  - 启动后端服务"
	@echo "  make start-frontend - 启动前端服务"
	@echo ""
	@echo "🧹 清理："
	@echo "  make clean          - 清理临时文件"
	@echo "  make clean-all      - 清理所有生成文件"

# 快速启动
quick-start:
	@echo "🚀 运行快速启动脚本..."
	python scripts/quick_start.py

# 安装依赖
install:
	@echo "📦 安装Python依赖..."
	pip install -r backend/requirements.txt
	@echo "📦 安装前端依赖..."
	cd frontend && npm install

# 设置环境变量
setup:
	@echo "🔧 设置环境变量..."
	python scripts/setup_env.py

# 检查配置
check:
	@echo "🔍 检查环境变量配置..."
	python scripts/check_env.py

check-dev:
	@echo "🔍 检查开发环境配置..."
	python scripts/check_env.py .env.development

check-prod:
	@echo "🔍 检查生产环境配置..."
	python scripts/check_env.py .env.production

# 测试
test:
	@echo "🧪 运行测试套件..."
	python -m pytest tests/ -v

test-env:
	@echo "🧪 测试环境配置..."
	python scripts/check_env.py

test-picgo:
	@echo "🧪 测试PicGo配置..."
	python scripts/test_picgo.py

test-multimodal:
	@echo "🧪 测试多模态功能..."
	python examples/multimodal_example.py

# 启动服务
dev: setup-dev-env
	@echo "🏃 启动开发环境..."
	@echo "后端服务将在 http://localhost:8000 启动"
	@echo "前端服务将在 http://localhost:3000 启动"
	@echo "按 Ctrl+C 停止服务"
	@make -j2 start-backend start-frontend

start-backend:
	@echo "🏃 启动后端服务..."
	cd backend && python main.py

start-frontend:
	@echo "🏃 启动前端服务..."
	cd frontend && npm start

# 环境设置
setup-dev-env:
	@if [ ! -f .env ]; then \
		echo "🔧 复制开发环境配置..."; \
		cp .env.development .env; \
		echo "⚠️  请编辑 .env 文件填入实际的API密钥"; \
	fi

setup-prod-env:
	@if [ ! -f .env ]; then \
		echo "🔧 复制生产环境配置..."; \
		cp .env.production .env; \
		echo "⚠️  请编辑 .env 文件填入实际的API密钥"; \
	fi

# 创建必要目录
create-dirs:
	@echo "📁 创建必要目录..."
	mkdir -p uploaded_files
	mkdir -p chroma_db
	mkdir -p logs
	mkdir -p backend/tmp

# 清理
clean:
	@echo "🧹 清理临时文件..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	rm -rf backend/tmp/*
	rm -rf test_uploads
	rm -rf test_chroma_db

clean-all: clean
	@echo "🧹 清理所有生成文件..."
	rm -rf uploaded_files
	rm -rf chroma_db
	rm -rf logs
	rm -rf node_modules
	rm -rf frontend/node_modules
	rm -rf frontend/build
	rm -rf frontend/dist

# 数据库操作
init-db:
	@echo "🗄️ 初始化数据库..."
	cd backend && python -c "from core.db import init_db; init_db()"

reset-db:
	@echo "🗄️ 重置数据库..."
	rm -f backend/app.db
	rm -f backend/dev.db
	@make init-db

# Docker操作
docker-build:
	@echo "🐳 构建Docker镜像..."
	docker-compose build

docker-up:
	@echo "🐳 启动Docker服务..."
	docker-compose up -d

docker-down:
	@echo "🐳 停止Docker服务..."
	docker-compose down

docker-logs:
	@echo "🐳 查看Docker日志..."
	docker-compose logs -f

# 代码质量
lint:
	@echo "🔍 代码质量检查..."
	flake8 backend/ --max-line-length=120
	black backend/ --check

format:
	@echo "🎨 格式化代码..."
	black backend/
	isort backend/

# 文档
docs:
	@echo "📚 生成文档..."
	@echo "可用文档："
	@echo "  • 环境配置指南: docs/ENVIRONMENT_SETUP.md"
	@echo "  • 多模态功能: docs/MULTIMODAL_SUPPORT.md"
	@echo "  • 实现总结: docs/MULTIMODAL_IMPLEMENTATION_SUMMARY.md"
	@echo "  • Docker指南: DOCKER_GUIDE.md"

# 部署
deploy-dev: setup-dev-env install create-dirs
	@echo "🚀 部署开发环境..."
	@make check-dev
	@echo "✅ 开发环境部署完成"

deploy-prod: setup-prod-env install create-dirs
	@echo "🚀 部署生产环境..."
	@make check-prod
	@echo "✅ 生产环境部署完成"

# 备份
backup:
	@echo "💾 备份数据..."
	mkdir -p backups
	cp -r chroma_db backups/chroma_db_$(shell date +%Y%m%d_%H%M%S)
	cp -r uploaded_files backups/uploaded_files_$(shell date +%Y%m%d_%H%M%S)
	@echo "✅ 备份完成"

# 监控
status:
	@echo "📊 系统状态检查..."
	@echo "Python版本: $(shell python --version)"
	@echo "环境变量文件: $(shell ls -la .env* 2>/dev/null || echo '无')"
	@echo "数据库文件: $(shell ls -la backend/*.db 2>/dev/null || echo '无')"
	@echo "上传目录: $(shell ls -la uploaded_files 2>/dev/null | wc -l) 个文件"
	@echo "向量数据库: $(shell ls -la chroma_db 2>/dev/null | wc -l) 个文件"

# 更新
update:
	@echo "🔄 更新依赖..."
	pip install -r backend/requirements.txt --upgrade
	cd frontend && npm update

# 安全检查
security:
	@echo "🔒 安全检查..."
	@echo "检查敏感文件..."
	@if [ -f .env ]; then echo "⚠️  .env 文件存在，确保不会提交到版本控制"; fi
	@echo "检查API密钥..."
	@python scripts/check_env.py | grep -E "(API|KEY)" || echo "未发现API密钥配置问题"