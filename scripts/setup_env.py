#!/usr/bin/env python3
"""
环境变量设置脚本
帮助用户快速设置和管理环境变量文件
"""

import os
import sys
import shutil
from pathlib import Path
from typing import Dict, List

def get_project_root() -> Path:
    """获取项目根目录"""
    return Path(__file__).parent.parent

def list_env_templates() -> Dict[str, str]:
    """列出所有可用的环境变量模板"""
    root = get_project_root()
    templates = {}
    
    env_files = [
        (".env.example", "通用示例配置"),
        (".env.development", "开发环境配置"),
        (".env.production", "生产环境配置"),
        (".env.testing", "测试环境配置"),
        (".env.audit.example", "审核系统示例配置")
    ]
    
    for filename, description in env_files:
        file_path = root / filename
        if file_path.exists():
            templates[filename] = description
    
    return templates

def copy_env_template(template_name: str, target_name: str = ".env") -> bool:
    """复制环境变量模板"""
    root = get_project_root()
    template_path = root / template_name
    target_path = root / target_name
    
    if not template_path.exists():
        print(f"❌ 模板文件不存在: {template_name}")
        return False
    
    if target_path.exists():
        response = input(f"⚠️  目标文件 {target_name} 已存在，是否覆盖？(y/N): ")
        if response.lower() != 'y':
            print("❌ 操作已取消")
            return False
    
    try:
        shutil.copy2(template_path, target_path)
        print(f"✅ 已复制 {template_name} 到 {target_name}")
        return True
    except Exception as e:
        print(f"❌ 复制失败: {e}")
        return False

def validate_env_file(env_file: str = ".env") -> List[str]:
    """验证环境变量文件"""
    root = get_project_root()
    env_path = root / env_file
    
    if not env_path.exists():
        return [f"环境变量文件 {env_file} 不存在"]
    
    issues = []
    required_vars = [
        "LLM_API_KEY",
        "LLM_BASE_URL", 
        "LLM_MODEL",
        "DATABASE_URL",
        "LOG_LEVEL"
    ]
    
    optional_vars = [
        "PICGO_API_KEY",
        "OPENAI_API_KEY",
        "SERPER_API_KEY",
        "OPENWEATHER_API_KEY"
    ]
    
    try:
        with open(env_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必需变量
        for var in required_vars:
            if f"{var}=" not in content:
                issues.append(f"缺少必需变量: {var}")
            elif f"{var}=your_" in content or f"{var}=test_" in content:
                issues.append(f"变量 {var} 使用默认值，需要设置实际值")
        
        # 检查可选变量
        missing_optional = []
        for var in optional_vars:
            if f"{var}=" not in content:
                missing_optional.append(var)
        
        if missing_optional:
            issues.append(f"缺少可选变量（可能影响某些功能）: {', '.join(missing_optional)}")
    
    except Exception as e:
        issues.append(f"读取文件失败: {e}")
    
    return issues

def show_env_help():
    """显示环境变量配置帮助"""
    help_text = """
🔧 环境变量配置指南

📋 必需配置：
  LLM_API_KEY        - 大语言模型API密钥
  LLM_BASE_URL       - API服务地址
  LLM_MODEL          - 使用的模型名称
  DATABASE_URL       - 数据库连接字符串
  LOG_LEVEL          - 日志级别

🎯 多模态功能：
  PICGO_API_KEY      - PicGo图床服务密钥（支持多模态大模型）

🔌 外部服务（可选）：
  OPENAI_API_KEY     - OpenAI API密钥
  SERPER_API_KEY     - 搜索服务密钥
  OPENWEATHER_API_KEY - 天气服务密钥

📁 环境类型：
  .env.development   - 开发环境（推荐本地开发使用）
  .env.production    - 生产环境（部署时使用）
  .env.testing       - 测试环境（自动化测试使用）
  .env.example       - 通用示例（包含所有配置项）

💡 使用建议：
  1. 开发环境：使用本地Ollama模型节省成本
  2. 生产环境：使用高性能云端模型
  3. 测试环境：使用最便宜的模型或模拟服务
  4. 多模态功能：需要配置PICGO_API_KEY
"""
    print(help_text)

def interactive_setup():
    """交互式环境设置"""
    print("🚀 财务智能体系统环境变量设置")
    print("=" * 50)
    
    # 显示可用模板
    templates = list_env_templates()
    if not templates:
        print("❌ 未找到环境变量模板文件")
        return
    
    print("📋 可用的环境变量模板：")
    template_list = list(templates.items())
    for i, (filename, description) in enumerate(template_list, 1):
        print(f"  {i}. {filename} - {description}")
    
    # 选择模板
    while True:
        try:
            choice = input(f"\n请选择模板 (1-{len(template_list)}): ")
            if choice.lower() == 'q':
                print("👋 退出设置")
                return
            
            index = int(choice) - 1
            if 0 <= index < len(template_list):
                selected_template = template_list[index][0]
                break
            else:
                print("❌ 无效选择，请重新输入")
        except ValueError:
            print("❌ 请输入数字")
    
    # 选择目标文件名
    target_name = input("\n目标文件名 (默认: .env): ").strip()
    if not target_name:
        target_name = ".env"
    
    # 复制模板
    if copy_env_template(selected_template, target_name):
        print(f"\n✅ 环境变量文件已创建: {target_name}")
        print("\n📝 下一步：")
        print(f"  1. 编辑 {target_name} 文件")
        print("  2. 填入实际的API密钥和配置值")
        print("  3. 运行验证: python scripts/setup_env.py --validate")
        
        # 询问是否验证
        if input("\n是否现在验证配置？(y/N): ").lower() == 'y':
            validate_and_show_issues(target_name)

def validate_and_show_issues(env_file: str = ".env"):
    """验证并显示配置问题"""
    print(f"\n🔍 验证环境变量文件: {env_file}")
    issues = validate_env_file(env_file)
    
    if not issues:
        print("✅ 环境变量配置验证通过！")
    else:
        print("⚠️  发现以下问题：")
        for issue in issues:
            print(f"  • {issue}")
        print(f"\n💡 请编辑 {env_file} 文件解决这些问题")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help" or sys.argv[1] == "-h":
            show_env_help()
        elif sys.argv[1] == "--validate":
            env_file = sys.argv[2] if len(sys.argv) > 2 else ".env"
            validate_and_show_issues(env_file)
        elif sys.argv[1] == "--list":
            templates = list_env_templates()
            print("📋 可用的环境变量模板：")
            for filename, description in templates.items():
                print(f"  • {filename} - {description}")
        else:
            print("❌ 未知参数，使用 --help 查看帮助")
    else:
        interactive_setup()

if __name__ == "__main__":
    main()