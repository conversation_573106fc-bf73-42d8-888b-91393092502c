#!/usr/bin/env python3
"""
环境变量检查脚本
检查当前环境变量配置是否正确
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from typing import Dict, List, Tu<PERSON>

def load_env_file(env_file: str = ".env") -> bool:
    """加载环境变量文件"""
    root = Path(__file__).parent.parent
    env_path = root / env_file
    
    if not env_path.exists():
        print(f"❌ 环境变量文件不存在: {env_file}")
        return False
    
    load_dotenv(env_path)
    print(f"✅ 已加载环境变量文件: {env_file}")
    return True

def check_core_config() -> List[Tuple[str, str, bool]]:
    """检查核心配置"""
    checks = []
    
    # LLM配置
    llm_api_key = os.getenv("LLM_API_KEY")
    checks.append(("LLM_API_KEY", "大语言模型API密钥", bool(llm_api_key and not llm_api_key.startswith("your_"))))
    
    llm_base_url = os.getenv("LLM_BASE_URL")
    checks.append(("LLM_BASE_URL", "LLM API地址", bool(llm_base_url)))
    
    llm_model = os.getenv("LLM_MODEL")
    checks.append(("LLM_MODEL", "LLM模型名称", bool(llm_model)))
    
    # 数据库配置
    database_url = os.getenv("DATABASE_URL")
    checks.append(("DATABASE_URL", "数据库连接", bool(database_url)))
    
    # 日志配置
    log_level = os.getenv("LOG_LEVEL")
    checks.append(("LOG_LEVEL", "日志级别", bool(log_level)))
    
    return checks

def check_multimodal_config() -> List[Tuple[str, str, bool]]:
    """检查多模态配置"""
    checks = []
    
    # PicGo配置
    picgo_key = os.getenv("PICGO_API_KEY")
    checks.append(("PICGO_API_KEY", "PicGo图床服务密钥", bool(picgo_key and not picgo_key.startswith("your_"))))
    
    return checks

def check_optional_config() -> List[Tuple[str, str, bool]]:
    """检查可选配置"""
    checks = []
    
    # OpenAI配置
    openai_key = os.getenv("OPENAI_API_KEY")
    checks.append(("OPENAI_API_KEY", "OpenAI API密钥", bool(openai_key and not openai_key.startswith("your_"))))
    
    # 搜索服务
    serper_key = os.getenv("SERPER_API_KEY")
    checks.append(("SERPER_API_KEY", "搜索服务密钥", bool(serper_key and not serper_key.startswith("your_"))))
    
    # 天气服务
    weather_key = os.getenv("OPENWEATHER_API_KEY")
    checks.append(("OPENWEATHER_API_KEY", "天气服务密钥", bool(weather_key and not weather_key.startswith("your_"))))
    
    # Ollama配置
    ollama_enabled = os.getenv("OLLAMA_ENABLED", "false").lower() == "true"
    checks.append(("OLLAMA_ENABLED", "本地Ollama模型", ollama_enabled))
    
    return checks

def check_audit_config() -> List[Tuple[str, str, bool]]:
    """检查审核系统配置"""
    checks = []
    
    # 审核AI配置
    audit_ai_key = os.getenv("AUDIT_AI_API_KEY")
    checks.append(("AUDIT_AI_API_KEY", "审核AI密钥", bool(audit_ai_key and not audit_ai_key.startswith("your_"))))
    
    # 嵌入模型配置
    embedding_key = os.getenv("EMBEDDING_API_KEY")
    checks.append(("EMBEDDING_API_KEY", "嵌入模型密钥", bool(embedding_key and not embedding_key.startswith("your_"))))
    
    # 向量数据库配置
    vector_db_dir = os.getenv("VECTOR_DB_PERSIST_DIR")
    checks.append(("VECTOR_DB_PERSIST_DIR", "向量数据库目录", bool(vector_db_dir)))
    
    return checks

def print_check_results(title: str, checks: List[Tuple[str, str, bool]], required: bool = True):
    """打印检查结果"""
    print(f"\n{title}")
    print("=" * len(title))
    
    passed = 0
    total = len(checks)
    
    for var_name, description, status in checks:
        status_icon = "✅" if status else ("❌" if required else "⚠️")
        value = os.getenv(var_name, "未设置")
        
        # 隐藏敏感信息
        if "KEY" in var_name or "PASSWORD" in var_name:
            if value and not value.startswith("your_") and not value.startswith("test_"):
                display_value = f"{value[:8]}..." if len(value) > 8 else "已设置"
            else:
                display_value = value
        else:
            display_value = value
        
        print(f"{status_icon} {description}: {display_value}")
        
        if status:
            passed += 1
    
    if required:
        print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    else:
        print(f"\n配置数: {passed}/{total}")

def check_model_compatibility():
    """检查模型兼容性"""
    print("\n🤖 模型兼容性检查")
    print("=" * 20)
    
    llm_model = os.getenv("LLM_MODEL", "").lower()
    
    # 检查是否支持多模态
    multimodal_keywords = ['vision', 'gpt-4v', 'gpt-4-vision', 'claude-3', 'gemini-pro-vision']
    is_multimodal = any(keyword in llm_model for keyword in multimodal_keywords)
    
    print(f"当前模型: {os.getenv('LLM_MODEL', '未设置')}")
    print(f"多模态支持: {'✅ 支持' if is_multimodal else '❌ 不支持'}")
    
    if is_multimodal:
        picgo_key = os.getenv("PICGO_API_KEY")
        if picgo_key and not picgo_key.startswith("your_"):
            print("图床服务: ✅ 已配置")
        else:
            print("图床服务: ⚠️  未配置（多模态功能将受限）")
    
    # 检查Ollama配置
    ollama_enabled = os.getenv("OLLAMA_ENABLED", "false").lower() == "true"
    if ollama_enabled:
        print(f"本地模型: ✅ 启用 ({os.getenv('OLLAMA_MODEL', '未设置')})")
    else:
        print("本地模型: ❌ 未启用")

def check_file_permissions():
    """检查文件权限"""
    print("\n📁 文件权限检查")
    print("=" * 16)
    
    root = Path(__file__).parent.parent
    
    # 检查上传目录
    upload_dir = os.getenv("UPLOAD_DIR", "./uploaded_files")
    upload_path = root / upload_dir
    
    if upload_path.exists():
        if os.access(upload_path, os.W_OK):
            print(f"✅ 上传目录可写: {upload_dir}")
        else:
            print(f"❌ 上传目录不可写: {upload_dir}")
    else:
        print(f"⚠️  上传目录不存在: {upload_dir}")
    
    # 检查向量数据库目录
    chroma_dir = os.getenv("CHROMA_PERSIST_DIRECTORY", "./chroma_db")
    chroma_path = root / chroma_dir
    
    if chroma_path.exists():
        if os.access(chroma_path, os.W_OK):
            print(f"✅ 向量数据库目录可写: {chroma_dir}")
        else:
            print(f"❌ 向量数据库目录不可写: {chroma_dir}")
    else:
        print(f"⚠️  向量数据库目录不存在: {chroma_dir}")

def generate_summary():
    """生成配置摘要"""
    print("\n📊 配置摘要")
    print("=" * 10)
    
    # 统计配置状态
    core_checks = check_core_config()
    multimodal_checks = check_multimodal_config()
    optional_checks = check_optional_config()
    audit_checks = check_audit_config()
    
    core_passed = sum(1 for _, _, status in core_checks if status)
    multimodal_passed = sum(1 for _, _, status in multimodal_checks if status)
    optional_passed = sum(1 for _, _, status in optional_checks if status)
    audit_passed = sum(1 for _, _, status in audit_checks if status)
    
    print(f"核心配置: {core_passed}/{len(core_checks)} ({'✅' if core_passed == len(core_checks) else '❌'})")
    print(f"多模态配置: {multimodal_passed}/{len(multimodal_checks)} ({'✅' if multimodal_passed > 0 else '❌'})")
    print(f"可选配置: {optional_passed}/{len(optional_checks)}")
    print(f"审核配置: {audit_passed}/{len(audit_checks)}")
    
    # 给出建议
    print("\n💡 建议:")
    if core_passed < len(core_checks):
        print("  • 请完善核心配置，这些是系统运行的必需项")
    
    if multimodal_passed == 0:
        print("  • 配置PICGO_API_KEY以启用多模态功能")
    
    if optional_passed < 2:
        print("  • 考虑配置更多可选服务以获得完整功能")

def main():
    """主函数"""
    print("🔍 财务智能体系统环境变量检查")
    print("=" * 40)
    
    # 加载环境变量
    env_file = sys.argv[1] if len(sys.argv) > 1 else ".env"
    if not load_env_file(env_file):
        return
    
    # 执行各项检查
    print_check_results("🔧 核心配置", check_core_config(), required=True)
    print_check_results("🎯 多模态配置", check_multimodal_config(), required=False)
    print_check_results("🔌 可选配置", check_optional_config(), required=False)
    print_check_results("📋 审核系统配置", check_audit_config(), required=False)
    
    # 模型兼容性检查
    check_model_compatibility()
    
    # 文件权限检查
    check_file_permissions()
    
    # 生成摘要
    generate_summary()

if __name__ == "__main__":
    main()