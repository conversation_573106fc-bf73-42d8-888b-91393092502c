#!/usr/bin/env python3
"""
快速启动脚本
帮助用户快速配置和启动财务智能体系统
"""

import os
import sys
import subprocess
from pathlib import Path
from typing import List, Tuple

def get_project_root() -> Path:
    """获取项目根目录"""
    return Path(__file__).parent.parent

def check_python_version() -> bool:
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_dependencies() -> List[str]:
    """检查依赖包"""
    required_packages = [
        "fastapi",
        "uvicorn", 
        "langchain",
        "python-dotenv",
        "requests",
        "pillow"
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing.append(package)
    
    return missing

def install_dependencies() -> bool:
    """安装依赖包"""
    root = get_project_root()
    requirements_file = root / "backend" / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    try:
        print("📦 安装依赖包...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功")
            return True
        else:
            print(f"❌ 依赖包安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def setup_environment() -> bool:
    """设置环境变量"""
    root = get_project_root()
    env_file = root / ".env"
    
    if env_file.exists():
        print("✅ 环境变量文件已存在")
        return True
    
    print("🔧 设置环境变量...")
    
    # 显示可用模板
    templates = {
        "1": (".env.development", "开发环境（推荐新手）"),
        "2": (".env.production", "生产环境"),
        "3": (".env.example", "通用示例")
    }
    
    print("选择环境模板：")
    for key, (filename, desc) in templates.items():
        print(f"  {key}. {desc}")
    
    while True:
        choice = input("请选择 (1-3): ").strip()
        if choice in templates:
            template_name, _ = templates[choice]
            break
        print("❌ 无效选择，请重新输入")
    
    # 复制模板
    template_path = root / template_name
    if not template_path.exists():
        print(f"❌ 模板文件不存在: {template_name}")
        return False
    
    try:
        import shutil
        shutil.copy2(template_path, env_file)
        print(f"✅ 已复制 {template_name} 到 .env")
        
        print("\n📝 下一步：")
        print("  1. 编辑 .env 文件")
        print("  2. 填入实际的API密钥")
        print("  3. 运行: python scripts/check_env.py")
        
        return True
    except Exception as e:
        print(f"❌ 复制失败: {e}")
        return False

def check_api_keys() -> List[str]:
    """检查API密钥配置"""
    from dotenv import load_dotenv
    
    root = get_project_root()
    env_file = root / ".env"
    
    if not env_file.exists():
        return ["环境变量文件不存在"]
    
    load_dotenv(env_file)
    
    issues = []
    
    # 检查必需的API密钥
    llm_key = os.getenv("LLM_API_KEY")
    if not llm_key or llm_key.startswith("your_"):
        issues.append("LLM_API_KEY 未配置")
    
    # 检查多模态功能
    picgo_key = os.getenv("PICGO_API_KEY")
    if not picgo_key or picgo_key.startswith("your_"):
        issues.append("PICGO_API_KEY 未配置（多模态功能将不可用）")
    
    return issues

def create_directories() -> bool:
    """创建必要的目录"""
    root = get_project_root()
    
    directories = [
        "uploaded_files",
        "chroma_db", 
        "logs",
        "backend/tmp"
    ]
    
    for dir_name in directories:
        dir_path = root / dir_name
        try:
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"✅ 目录: {dir_name}")
        except Exception as e:
            print(f"❌ 创建目录失败 {dir_name}: {e}")
            return False
    
    return True

def test_basic_functionality() -> bool:
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    try:
        # 测试导入核心模块
        sys.path.append(str(get_project_root()))
        from backend.core.agent.modules.agent_base import get_agent_executor
        
        agent = get_agent_executor("test_session")
        print("✅ 智能体模块加载成功")
        
        # 测试配置加载
        from backend.core.config import Config
        config = Config()
        print("✅ 配置模块加载成功")
        
        return True
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n🎉 快速启动完成！")
    print("\n📋 后续步骤：")
    print("  1. 配置API密钥:")
    print("     nano .env")
    print("  2. 验证配置:")
    print("     python scripts/check_env.py")
    print("  3. 启动后端服务:")
    print("     cd backend && python main.py")
    print("  4. 启动前端服务:")
    print("     cd frontend && npm install && npm start")
    
    print("\n🔗 有用的命令：")
    print("  • 测试多模态功能: python examples/multimodal_example.py")
    print("  • 测试PicGo配置: python scripts/test_picgo.py")
    print("  • 查看文档: docs/ENVIRONMENT_SETUP.md")

def main():
    """主函数"""
    print("🚀 财务智能体系统快速启动")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        return
    
    print("\n📦 检查依赖包...")
    missing = check_dependencies()
    
    if missing:
        print(f"\n❌ 缺少 {len(missing)} 个依赖包")
        install = input("是否自动安装？(y/N): ").lower() == 'y'
        if install:
            if not install_dependencies():
                return
        else:
            print("请手动安装依赖包:")
            print(f"pip install -r backend/requirements.txt")
            return
    
    print("\n🔧 检查环境配置...")
    if not setup_environment():
        return
    
    print("\n📁 创建必要目录...")
    if not create_directories():
        return
    
    print("\n🔑 检查API密钥配置...")
    issues = check_api_keys()
    if issues:
        print("⚠️  发现配置问题：")
        for issue in issues:
            print(f"  • {issue}")
        print("\n请编辑 .env 文件解决这些问题")
    else:
        print("✅ API密钥配置正常")
    
    print("\n🧪 测试基本功能...")
    if test_basic_functionality():
        print("✅ 系统基本功能正常")
    else:
        print("❌ 系统功能测试失败，请检查配置")
        return
    
    show_next_steps()

if __name__ == "__main__":
    main()