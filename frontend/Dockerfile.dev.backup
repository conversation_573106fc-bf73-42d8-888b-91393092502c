# 使用 Node.js 20 作为基础镜像（备用版本，使用官方源）
FROM node:20-alpine

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk update && apk add --no-cache \
    python3 \
    make \
    g++ \
    linux-headers \
    git

# 使用官方源安装 yarn（如果国内源有问题）
RUN npm install -g yarn

# 复制 package.json 和 yarn.lock
COPY package.json yarn.lock ./

# 安装所有依赖（包括开发依赖）
RUN yarn install

# 暴露端口
EXPOSE 5173

# 启动开发服务器
CMD ["yarn", "dev", "--", "--host", "0.0.0.0"]
