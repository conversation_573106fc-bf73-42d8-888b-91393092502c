/**
 * AI服务请求头工具函数
 * 统一处理不同AI服务类型的请求头设置
 */

import { AI_SERVICE_TYPES } from '../types/aiConfig';

/**
 * 根据AI服务配置生成请求头
 * @param {Object} activeService - 当前活动的AI服务配置
 * @param {Object} baseHeaders - 基础请求头（可选）
 * @returns {Object} 完整的请求头对象
 */
export const buildAIServiceHeaders = (activeService, baseHeaders = {}) => {
  const headers = {
    'Content-Type': 'application/json',
    ...baseHeaders
  };

  if (!activeService) {
    console.warn('[AI Service Headers] 没有提供活动的AI服务配置');
    return headers;
  }

  console.log('[AI Service Headers] 构建请求头，服务类型:', activeService.type);

  switch (activeService.type) {
    case AI_SERVICE_TYPES.OLLAMA:
      headers['X-API-Key'] = '';
      headers['X-Base-URL'] = activeService.config.baseUrl;
      headers['X-Model'] = activeService.config.model;
      headers['X-Use-Ollama'] = 'true';
      break;

    case AI_SERVICE_TYPES.LMSTUDIO:
      // LMStudio 作为本地服务，不需要 API key
      headers['X-API-Key'] = 'not-needed';  // LMStudio 推荐的占位符
      headers['X-Base-URL'] = activeService.config.baseUrl;
      headers['X-Model'] = activeService.config.model;
      headers['X-Use-Ollama'] = 'false';
      headers['X-Service-Type'] = 'lmstudio';  // 明确指定服务类型
      break;

    case AI_SERVICE_TYPES.OPENAI_COMPATIBLE:
    case AI_SERVICE_TYPES.CUSTOM:
    default:
      // OpenAI 兼容或其他服务
      headers['X-API-Key'] = activeService.config.apiKey || '';
      headers['X-Base-URL'] = activeService.config.baseUrl;
      headers['X-Model'] = activeService.config.model;
      headers['X-Use-Ollama'] = 'false';
      break;
  }

  console.log('[AI Service Headers] 生成的请求头:', headers);
  return headers;
};

/**
 * 为配置请求生成请求头
 * @param {Object} activeService - 当前活动的AI服务配置
 * @returns {Object} 配置请求的请求头
 */
export const buildConfigureHeaders = (activeService) => {
  return buildAIServiceHeaders(activeService);
};

/**
 * 为流式请求生成请求头
 * @param {Object} activeService - 当前活动的AI服务配置
 * @returns {Object} 流式请求的请求头
 */
export const buildStreamHeaders = (activeService) => {
  return buildAIServiceHeaders(activeService);
};

/**
 * 为审核请求生成请求头
 * @param {Object} activeService - 当前活动的AI服务配置
 * @returns {Object} 审核请求的请求头
 */
export const buildAuditHeaders = (activeService) => {
  return buildAIServiceHeaders(activeService);
};

/**
 * 为咨询请求生成请求头
 * @param {Object} activeService - 当前活动的AI服务配置
 * @returns {Object} 咨询请求的请求头
 */
export const buildConsultationHeaders = (activeService) => {
  return buildAIServiceHeaders(activeService);
};

/**
 * 验证AI服务配置是否完整
 * @param {Object} activeService - AI服务配置
 * @returns {Object} 验证结果 { isValid: boolean, errors: string[] }
 */
export const validateAIServiceForHeaders = (activeService) => {
  const errors = [];

  if (!activeService) {
    errors.push('没有提供AI服务配置');
    return { isValid: false, errors };
  }

  if (!activeService.config) {
    errors.push('AI服务配置缺少config字段');
    return { isValid: false, errors };
  }

  // 根据服务类型验证必需字段
  switch (activeService.type) {
    case AI_SERVICE_TYPES.OLLAMA:
      if (!activeService.config.baseUrl) {
        errors.push('Ollama服务缺少baseUrl配置');
      }
      if (!activeService.config.model) {
        errors.push('Ollama服务缺少model配置');
      }
      break;

    case AI_SERVICE_TYPES.LMSTUDIO:
      if (!activeService.config.baseUrl) {
        errors.push('LMStudio服务缺少baseUrl配置');
      }
      if (!activeService.config.model) {
        errors.push('LMStudio服务缺少model配置');
      }
      break;

    case AI_SERVICE_TYPES.OPENAI_COMPATIBLE:
    case AI_SERVICE_TYPES.CUSTOM:
    default:
      if (!activeService.config.baseUrl) {
        errors.push('AI服务缺少baseUrl配置');
      }
      if (!activeService.config.model) {
        errors.push('AI服务缺少model配置');
      }
      if (!activeService.config.apiKey && activeService.type === AI_SERVICE_TYPES.OPENAI_COMPATIBLE) {
        errors.push('OpenAI兼容服务缺少apiKey配置');
      }
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 安全地构建AI服务请求头，包含验证
 * @param {Object} activeService - 当前活动的AI服务配置
 * @param {Object} baseHeaders - 基础请求头（可选）
 * @returns {Object} { headers: Object, isValid: boolean, errors: string[] }
 */
export const buildAIServiceHeadersSafe = (activeService, baseHeaders = {}) => {
  const validation = validateAIServiceForHeaders(activeService);
  
  if (!validation.isValid) {
    console.error('[AI Service Headers] 配置验证失败:', validation.errors);
    return {
      headers: { 'Content-Type': 'application/json', ...baseHeaders },
      isValid: false,
      errors: validation.errors
    };
  }

  const headers = buildAIServiceHeaders(activeService, baseHeaders);
  
  return {
    headers,
    isValid: true,
    errors: []
  };
};
