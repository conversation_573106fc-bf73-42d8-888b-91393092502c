import React, { useState, useEffect } from 'react'
import { History, Plus } from 'lucide-react'
import FunctionBar from './components/FunctionBar'
import Sidebar from './components/Sidebar'
import Workspace from './components/Workspace'
import Agent from './components/Agent'
import Resizer from './components/Resizer'
import LeftResizer from './components/LeftResizer'
import ConversationList from './components/ConversationList'
import Voucher from './components/Voucher'
import Bookkeeping from './components/Bookkeeping'
import Report from './components/Report'
import Settlement from './components/Settlement'
import Asset from './components/Asset'
import Invoice from './components/Invoice'
import Cashier from './components/Cashier'
import Salary from './components/Salary'
import Tax from './components/Tax'
import Settings from './components/Settings'
import SettingsManager from './components/settings/SettingsManager'
import HistoryDialog from './components/HistoryDialog'
import SubjectTable from './components/SubjectTable';
import RoleStaffConfig from './components/RoleStaffConfig';
import CompanyInfo from './components/CompanyInfo';
import Approval from './components/Approval';
import Supplier from './components/Supplier';
import ElectronMainLayout from './components/electron/ElectronMainLayout';
import { backendBase } from './api';
import WorkspaceProvider from './components/workspace/WorkspaceProvider';
import { createAIConfig, migrateFromLegacyConfig } from './types/aiConfig';

const App = () => {
  const [activeFeature, setActiveFeature] = useState('agent')
  const [vouchers, setVouchers] = useState([])
  const [subjects, setSubjects] = useState([])
  const [assets, setAssets] = useState([])
  const [staffs, setStaffs] = useState([])
  const [showHistoryDialog, setShowHistoryDialog] = useState(false)
  const [activeSetting, setActiveSetting] = useState('settings')
  const [isWorkspaceVisible, setIsWorkspaceVisible] = useState(true) // 工作区初始状态为打开

  // Agent侧边栏宽度和会话管理
  const [agentWidth, setAgentWidth] = useState(720)
  const [isResizing, setIsResizing] = useState(false)
  
  // 工作区宽度管理
  const [workspaceWidth, setWorkspaceWidth] = useState(384) // 默认宽度为384px (w-96)
  const [isWorkspaceResizing, setIsWorkspaceResizing] = useState(false)
  const [sessions, setSessions] = useState(() => [
    { id: Date.now().toString(), name: '新会话', messages: [], isLoading: false }
  ])
  const [activeSessionId, setActiveSessionId] = useState(sessions[0].id)

  // 会话管理函数
  const createSession = () => {
    const newSession = { id: Date.now().toString(), name: '新会话', messages: [], isLoading: false }
    setSessions(prev => [...prev, newSession])
    setActiveSessionId(newSession.id)
  }

  const updateSession = (id, updates) => {
    setSessions(prev => prev.map(s => s.id === id ? { ...s, ...updates } : s))
  }

  const deleteSession = (id) => {
    setSessions(prev => {
      const newSessions = prev.filter(s => s.id !== id)
      // 如果删除的是当前活跃会话，切换到第一个会话
      if (id === activeSessionId && newSessions.length > 0) {
        setActiveSessionId(newSessions[0].id)
      }
      // 如果没有会话了，创建一个新的
      if (newSessions.length === 0) {
        const newSession = { id: Date.now().toString(), name: '新会话', messages: [], isLoading: false }
        setActiveSessionId(newSession.id)
        return [newSession]
      }
      return newSessions
    })
  }

  // 处理Agent区域大小调整
  const handleAgentResize = (clientX) => {
    setIsResizing(true)
    // 获取左侧边栏的实际宽度
    const leftSidebar = document.querySelector('.w-64.bg-gray-50.border-r')
    const leftSidebarWidth = leftSidebar ? leftSidebar.offsetWidth : 256
    const newWidth = clientX - leftSidebarWidth
    const constrainedWidth = Math.max(320, Math.min(newWidth, window.innerWidth * 0.7))
    setAgentWidth(constrainedWidth)
  }

  // 处理工作区大小调整（从左侧拖动）
  const handleWorkspaceResize = (clientX) => {
    setIsWorkspaceResizing(true)
    // 获取左侧边栏的实际宽度
    const leftSidebar = document.querySelector('.w-64.bg-gray-50.border-r')
    const leftSidebarWidth = leftSidebar ? leftSidebar.offsetWidth : 256
    
    // 计算新的工作区宽度（从窗口右侧减去鼠标位置）
    const newWorkspaceWidth = window.innerWidth - clientX
    // 限制最小宽度和最大宽度
    const constrainedWorkspaceWidth = Math.max(300, Math.min(newWorkspaceWidth, window.innerWidth * 0.5))
    
    // 同时更新智能体区域的宽度，确保总宽度协调
    const newAgentWidth = window.innerWidth - leftSidebarWidth - constrainedWorkspaceWidth
    const constrainedAgentWidth = Math.max(320, Math.min(newAgentWidth, window.innerWidth * 0.7))
    
    // 同时更新两个宽度，避免视觉闪烁
    setWorkspaceWidth(constrainedWorkspaceWidth)
    setAgentWidth(constrainedAgentWidth)
  }

  const [aiConfig, setAiConfig] = useState(() => {
    const savedConfig = localStorage.getItem('aiConfig')
    if (savedConfig) {
      const parsedConfig = JSON.parse(savedConfig)

      // 检查是否是新的配置格式
      if (parsedConfig.version === '2.0' && parsedConfig.services) {
        return parsedConfig
      } else {
        // 从旧配置迁移到新配置
        const migratedConfig = migrateFromLegacyConfig(parsedConfig)
        // 保存迁移后的配置
        localStorage.setItem('aiConfig', JSON.stringify(migratedConfig))
        return migratedConfig
      }
    } else {
      // 创建默认配置
      const defaultConfig = createAIConfig({
        services: [],
        activeServiceId: null
      })
      return defaultConfig
    }
  })

  // 监听窗口大小变化，自动调整智能助手和工作区宽度
  useEffect(() => {
    const handleResize = () => {
      if (isWorkspaceVisible && window.innerWidth > 0) {
        const leftSidebarWidth = 256; // 左侧边栏宽度
        const totalAvailableWidth = window.innerWidth - leftSidebarWidth;
        
        // 计算新的宽度比例，智能助手占70%，工作区占30%
        const newAgentWidth = Math.max(320, Math.floor(totalAvailableWidth * 0.7));
        const newWorkspaceWidth = Math.max(300, totalAvailableWidth - newAgentWidth);
        
        setAgentWidth(newAgentWidth);
        setWorkspaceWidth(newWorkspaceWidth);
      }
    };

    // 初始调整一次
    handleResize();
    
    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
    
    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isWorkspaceVisible]); // 当工作区可见性变化时重新计算

  // 保存配置到本地存储和状态
  const saveConfig = (config) => {
    // 确保配置包含use_ollama字段
    const finalConfig = {
      ...config,
      use_ollama: config.use_ollama || (config.local_enabled && !config.remote_enabled)
    }
    setAiConfig(finalConfig)
    localStorage.setItem('aiConfig', JSON.stringify(finalConfig))
  }

  // 监听工作区项目添加事件
  useEffect(() => {
    const handleWorkspaceItemAdded = (event) => {
      console.log('workspace-item-added event received:', event.detail);
      setIsWorkspaceVisible(true);
    };

    window.addEventListener('workspace-item-added', handleWorkspaceItemAdded);
    
    return () => {
      window.removeEventListener('workspace-item-added', handleWorkspaceItemAdded);
    };
  }, []);

  // 监听工作区显示/隐藏切换事件
  useEffect(() => {
    const handleToggleWorkspaceVisibility = () => {
      console.log('toggle-workspace-visibility event received');
      setIsWorkspaceVisible(prev => !prev);
    };

    window.addEventListener('toggle-workspace-visibility', handleToggleWorkspaceVisibility);
    
    return () => {
      window.removeEventListener('toggle-workspace-visibility', handleToggleWorkspaceVisibility);
    };
  }, []);

  // 监听从RAG管理跳转到微工作流的事件
  useEffect(() => {
    const handleNavigateToWorkflow = (event) => {
      console.log('navigate-to-workflow event received:', event.detail);
      // 切换到设置功能
      setActiveFeature('settings');
      // 设置活动设置为内部工具（微工作流）
      if (typeof window !== 'undefined' && window.setActiveSetting) {
        window.setActiveSetting('internal-tools');
      }
    };

    window.addEventListener('navigate-to-workflow', handleNavigateToWorkflow);
    
    return () => {
      window.removeEventListener('navigate-to-workflow', handleNavigateToWorkflow);
    };
  }, []);

  // 监听Electron窗口大小变化事件
  useEffect(() => {
    const isElectron = typeof window !== 'undefined' && window.electronAPI;
    
    if (isElectron) {
      const handleWindowResized = (data) => {
        console.log('Window resized:', data);
        // 当窗口大小变化时，重新计算智能助手和工作区的宽度
        if (isWorkspaceVisible && window.innerWidth > 0) {
          const leftSidebarWidth = 256; // 左侧边栏宽度
          const totalAvailableWidth = window.innerWidth - leftSidebarWidth;
          
          // 计算新的宽度比例，智能助手占70%，工作区占30%
          const newAgentWidth = Math.max(320, Math.floor(totalAvailableWidth * 0.7));
          const newWorkspaceWidth = Math.max(300, totalAvailableWidth - newAgentWidth);
          
          setAgentWidth(newAgentWidth);
          setWorkspaceWidth(newWorkspaceWidth);
        }
      };

      // 添加监听器
      window.electronAPI.onWindowResized(handleWindowResized);
      
      // 清理函数
      return () => {
        if (window.electronAPI && window.electronAPI.removeWindowResizedListener) {
          window.electronAPI.removeWindowResizedListener();
        }
      };
    }
  }, [isWorkspaceVisible]); // 当工作区可见性变化时重新设置监听器

  // 测试AI服务器连接
  const testAiConnection = async (config) => {
    try {
      // 先保存配置
      saveConfig(config)
      
      const response = await fetch('http://localhost:8000/ai/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || '连接测试失败')
      }
      
      return await response.json()
    } catch (error) {
      throw new Error(error.message)
    }
  }

  const features = {
    agent: { name: '智能体', component: Agent },
    approval: { name: '审批', component: Approval },
    voucher: { name: '凭证', component: Voucher },
    bookkeeping: { name: '账簿', component: Bookkeeping },
    report: { name: '报表', component: Report },
    settlement: { name: '结算', component: Settlement },
    asset: { name: '资产', component: Asset },
    invoice: { name: '发票', component: Invoice },
    cashier: { name: '出纳', component: Cashier },
    salary: { name: '工资', component: Salary },
    tax: { name: '税务', component: Tax },
    supplier: { name: '供应商', component: Supplier },
    subject: { name: '科目表', component: SubjectTable },
    roleStaff: { name: '部门人员', component: RoleStaffConfig },
    company: { name: '公司信息', component: CompanyInfo },
    settings: { name: '设置', component: Settings },
  }

  const ActiveComponent = features[activeFeature].component
  const isElectron = typeof window !== 'undefined' && window.electronAPI;

  // Electron 布局 - Cherry Studio 风格
  if (isElectron) {
    return (
      <WorkspaceProvider>
        <ElectronMainLayout
        activeFeature={activeFeature}
        onFeatureSelect={setActiveFeature}
        features={features}
        sessions={sessions}
        activeSessionId={activeSessionId}
        onSessionSelect={setActiveSessionId}
        onNewSession={createSession}
        aiConfig={aiConfig}
        setAiConfig={setAiConfig}
        backendBase={backendBase}
        isWorkspaceVisible={isWorkspaceVisible}
      >
        {activeFeature === 'agent' ? (
          <div className="flex h-full justify-center">
            {/* 智能体聊天区域 */}
            <div
              className="flex flex-col h-full relative"
              style={{
                width: isWorkspaceVisible ? `${agentWidth}px` : '50%'
              }}
              onMouseUp={() => {
                setIsResizing(false)
                setIsWorkspaceResizing(false)
              }}
            >
              {sessions.map(session => (
                <div key={session.id} className={`${session.id === activeSessionId ? 'h-full' : 'hidden'}`}>
                  <Agent
                    aiConfig={aiConfig}
                    setVouchers={setVouchers}
                    setSubjects={setSubjects}
                    setAssets={setAssets}
                    setStaffs={setStaffs}
                    inSidebar={false}
                    session={session}
                    updateSession={updates => updateSession(session.id, updates)}
                  />
                </div>
              ))}
            </div>
            
            {/* 右侧工作区 */}
            {isWorkspaceVisible && (
              <div
                className="border-l border-gray-200 bg-gray-50 relative flex-shrink-0"
                style={{
                  width: `${workspaceWidth}px`,
                  transition: 'none'
                }}
                onMouseUp={() => {
                  setIsResizing(false)
                  setIsWorkspaceResizing(false)
                }}
              >
                <LeftResizer onResize={handleWorkspaceResize} />
                <Workspace
                  vouchers={vouchers}
                  subjects={subjects}
                  assets={assets}
                  staffs={staffs}
                  isVisible={isWorkspaceVisible}
                  onVisibilityChange={setIsWorkspaceVisible}
                />
              </div>
            )}
          </div>
        ) : activeFeature === 'settings' ? (
          <SettingsManager
            activeSetting={activeSetting}
            setActiveSetting={setActiveSetting}
            aiConfig={aiConfig}
            setAiConfig={setAiConfig}
            backendBase={backendBase}
          />
        ) : (
          <div className="flex h-full">
            {/* 左侧功能区域 */}
            <div className="flex-1 overflow-auto p-6">
              <div className="max-w-6xl mx-auto">
                <ActiveComponent
                  vouchers={vouchers}
                  setVouchers={setVouchers}
                  aiConfig={aiConfig}
                  setAiConfig={setAiConfig}
                  backendBase={backendBase}
                />
              </div>
            </div>
            
            {/* 右侧工作区 */}
            {isWorkspaceVisible && (
              <div
                className="border-l border-gray-200 bg-gray-50 relative flex-shrink-0"
                style={{
                  width: `${workspaceWidth}px`,
                  transition: 'none'
                }}
                onMouseUp={() => {
                  setIsResizing(false)
                  setIsWorkspaceResizing(false)
                }}
              >
                <LeftResizer onResize={handleWorkspaceResize} />
                <Workspace
                  vouchers={vouchers}
                  subjects={subjects}
                  assets={assets}
                  staffs={staffs}
                  isVisible={isWorkspaceVisible}
                  onVisibilityChange={setIsWorkspaceVisible}
                />
              </div>
            )}
          </div>
        )}
        
        <HistoryDialog
          isOpen={showHistoryDialog}
          onClose={() => setShowHistoryDialog(false)}
          sessions={sessions}
          onDeleteSession={deleteSession}
          onSelectSession={setActiveSessionId}
          onNewSession={createSession}
          activeSessionId={activeSessionId}
        />
        </ElectronMainLayout>
      </WorkspaceProvider>
    );
  }

  // Web 布局（原有布局）
  return (
    <WorkspaceProvider>
      <div className="flex h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-400/20 to-pink-600/20 rounded-full blur-3xl"></div>
      </div>
      
      <FunctionBar
        activeFeature={activeFeature}
        onFeatureSelect={setActiveFeature}
        features={features}
      />
      
      {activeFeature === 'agent' ? (
        <>
          <div
            className="relative glass rounded-r-2xl border-r border-white/20 flex flex-col h-full shadow-glass"
            style={{ width: `${agentWidth}px`, transition: 'none' }}
            onMouseUp={() => setIsResizing(false)}
          >
            <div className="border-b border-white/10 backdrop-blur-sm">
              <div className="flex items-center justify-between p-6">
                <h2 className="text-xl font-semibold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
                  智能助手
                </h2>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={createSession}
                    className="p-2.5 text-gray-600 hover:text-green-500 hover:bg-green-50/50 rounded-xl transition-all duration-200 focus-ring"
                    title="新建对话"
                  >
                    <Plus size={18} />
                  </button>
                  <button
                    onClick={() => setShowHistoryDialog(true)}
                    className="p-2.5 text-gray-600 hover:text-blue-500 hover:bg-blue-50/50 rounded-xl transition-all duration-200 focus-ring"
                    title="历史对话管理"
                  >
                    <History size={18} />
                  </button>
                </div>
              </div>
              <ConversationList
                sessions={sessions}
                activeId={activeSessionId}
              />
            </div>
            <div className="flex-1 overflow-hidden">
              <div className="h-full">
                {sessions.map(session => (
                  <div key={session.id} className={`${session.id === activeSessionId ? 'h-full' : 'hidden'}`} style={{height:'100%'}}>
                    <Agent
                      aiConfig={aiConfig}
                      setVouchers={setVouchers}
                      setSubjects={setSubjects}
                      setAssets={setAssets}
                      setStaffs={setStaffs}
                      inSidebar={true}
                      session={session}
                      updateSession={updates => updateSession(session.id, updates)}
                    />
                  </div>
                ))}
              </div>
            </div>
            <Resizer onResize={handleAgentResize} />
          </div>
          {isWorkspaceVisible && (
            <div
              className="border-l border-gray-200 bg-gray-50 relative flex-shrink-0"
              style={{
                width: `${workspaceWidth}px`,
                transition: 'none'
              }}
              onMouseUp={() => {
                setIsResizing(false)
                setIsWorkspaceResizing(false)
              }}
            >
              <LeftResizer onResize={handleWorkspaceResize} />
              <Workspace
                vouchers={vouchers}
                subjects={subjects}
                assets={assets}
                staffs={staffs}
                isVisible={isWorkspaceVisible}
                onVisibilityChange={setIsWorkspaceVisible}
              />
            </div>
          )}
        </>
      ) : (
        <>
          <Sidebar
            activeFeature={activeFeature}
            features={features}
            aiConfig={aiConfig}
            setVouchers={setVouchers}
            setSubjects={setSubjects}
            setAssets={setAssets}
            setStaffs={setStaffs}
            activeSetting={activeSetting}
            setActiveSetting={setActiveSetting}
          />
          <div className={`${isWorkspaceVisible ? 'flex-1' : 'w-full'} flex flex-col h-full overflow-hidden`}>
            {activeFeature === 'settings' ? (
              <SettingsManager
                activeSetting={activeSetting}
                setActiveSetting={setActiveSetting}
                aiConfig={aiConfig}
                setAiConfig={setAiConfig}
                backendBase={backendBase}
              />
            ) : (
              <div className="flex-1 overflow-auto p-8">
                <div className="max-w-7xl mx-auto">
                  <ActiveComponent
                    vouchers={vouchers}
                    setVouchers={setVouchers}
                    aiConfig={aiConfig}
                    setAiConfig={setAiConfig}
                    backendBase={backendBase}
                  />
                </div>
              </div>
            )}
          </div>
          
          {/* 右侧工作区 */}
          {isWorkspaceVisible && (
            <div
              className="border-l border-gray-200 bg-gray-50 relative flex-shrink-0"
              style={{
                width: `${workspaceWidth}px`,
                transition: (isResizing || isWorkspaceResizing) ? 'none' : 'width 0.3s ease'
              }}
              onMouseUp={() => {
                setIsResizing(false)
                setIsWorkspaceResizing(false)
              }}
            >
              <LeftResizer onResize={handleWorkspaceResize} />
              <Workspace
                vouchers={vouchers}
                subjects={subjects}
                assets={assets}
                staffs={staffs}
                isVisible={isWorkspaceVisible}
                onVisibilityChange={setIsWorkspaceVisible}
              />
            </div>
          )}
        </>
      )}
      
      
      <HistoryDialog
        isOpen={showHistoryDialog}
        onClose={() => setShowHistoryDialog(false)}
        sessions={sessions}
        onDeleteSession={deleteSession}
        onSelectSession={setActiveSessionId}
        onNewSession={createSession}
        activeSessionId={activeSessionId}
      />
      </div>
    </WorkspaceProvider>
  )
}

export default App
