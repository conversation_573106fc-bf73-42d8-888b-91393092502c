/**
 * AI服务配置类型定义
 */

// AI服务类型枚举
export const AI_SERVICE_TYPES = {
  OPENAI_COMPATIBLE: 'openai_compatible', // OpenAI兼容接口
  OLLAMA: 'ollama', // Ollama本地服务
  LMSTUDIO: 'lmstudio', // LM Studio本地服务
  CUSTOM: 'custom' // 自定义服务类型
};

// AI服务配置接口
export const createAIServiceConfig = ({
  id = null,
  name = '',
  type = AI_SERVICE_TYPES.OPENAI_COMPATIBLE,
  enabled = false,
  isDefault = false,
  supportsMultimodal = false,
  priority = 0,
  config = {},
  connectionStatus = 'none', // 'none', 'testing', 'success', 'error'
  lastTested = null,
  createdAt = new Date().toISOString(),
  updatedAt = new Date().toISOString()
}) => ({
  id: id || generateServiceId(),
  name,
  type,
  enabled,
  isDefault,
  supportsMultimodal,
  priority,
  config: {
    ...getDefaultConfigForType(type),
    ...config
  },
  connectionStatus,
  lastTested,
  createdAt,
  updatedAt
});

// 为不同服务类型生成默认配置
export const getDefaultConfigForType = (type) => {
  switch (type) {
    case AI_SERVICE_TYPES.OPENAI_COMPATIBLE:
      return {
        apiKey: '',
        baseUrl: 'https://api.openai.com/v1',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 2000,
        timeout: 30000
      };
    case AI_SERVICE_TYPES.OLLAMA:
      return {
        baseUrl: 'http://localhost:11434',
        model: 'llama3.1:8b',
        temperature: 0.7,
        timeout: 60000
      };
    case AI_SERVICE_TYPES.LMSTUDIO:
      return {
        baseUrl: 'http://localhost:1234/v1',
        model: 'local-model',
        temperature: 0.7,
        timeout: 60000
      };
    case AI_SERVICE_TYPES.CUSTOM:
      return {
        apiKey: '',
        baseUrl: '',
        model: '',
        headers: {},
        temperature: 0.7,
        maxTokens: 2000,
        timeout: 30000
      };
    default:
      return {};
  }
};

// 生成唯一的服务ID
export const generateServiceId = () => {
  return 'ai_service_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

// 新的AI配置结构
export const createAIConfig = ({
  services = [],
  activeServiceId = null,
  globalSettings = {}
}) => ({
  services,
  activeServiceId,
  globalSettings: {
    defaultTimeout: 30000,
    retryAttempts: 3,
    retryDelay: 1000,
    ...globalSettings
  },
  version: '2.0' // 版本标识，用于数据迁移
});

// 从旧配置迁移到新配置
export const migrateFromLegacyConfig = (legacyConfig) => {
  const services = [];
  
  // 迁移远程服务
  if (legacyConfig.remote_enabled || legacyConfig.api_key) {
    services.push(createAIServiceConfig({
      name: '远程AI服务',
      type: AI_SERVICE_TYPES.OPENAI_COMPATIBLE,
      enabled: legacyConfig.remote_enabled || false,
      isDefault: !legacyConfig.use_ollama,
      supportsMultimodal: legacyConfig.model?.includes('vision') || legacyConfig.model?.includes('vl') || false,
      priority: legacyConfig.use_ollama ? 1 : 0,
      config: {
        apiKey: legacyConfig.api_key || '',
        baseUrl: legacyConfig.base_url || 'https://api.openai.com/v1',
        model: legacyConfig.model || 'gpt-3.5-turbo'
      },
      connectionStatus: legacyConfig.remote_connection_status || 'none'
    }));
  }
  
  // 迁移本地服务
  if (legacyConfig.local_enabled || legacyConfig.ollama_base_url) {
    services.push(createAIServiceConfig({
      name: '本地AI服务 (Ollama)',
      type: AI_SERVICE_TYPES.OLLAMA,
      enabled: legacyConfig.local_enabled || false,
      isDefault: legacyConfig.use_ollama || false,
      supportsMultimodal: false, // Ollama默认不支持多模态，可以后续配置
      priority: legacyConfig.use_ollama ? 0 : 1,
      config: {
        baseUrl: legacyConfig.ollama_base_url || 'http://localhost:11434',
        model: legacyConfig.ollama_model || 'llama3.1:8b'
      },
      connectionStatus: legacyConfig.local_connection_status || 'none'
    }));
  }
  
  // 确定活动服务
  let activeServiceId = null;
  if (services.length > 0) {
    const defaultService = services.find(s => s.isDefault) || services[0];
    activeServiceId = defaultService.id;
  }
  
  return createAIConfig({
    services,
    activeServiceId
  });
};

// 验证AI服务配置
export const validateAIServiceConfig = (serviceConfig) => {
  const errors = [];

  if (!serviceConfig.name?.trim()) {
    errors.push('服务名称不能为空');
  }

  if (!serviceConfig.type) {
    errors.push('必须选择服务类型');
  }

  // 根据服务类型验证配置
  switch (serviceConfig.type) {
    case AI_SERVICE_TYPES.OPENAI_COMPATIBLE:
      if (!serviceConfig.config.apiKey?.trim()) {
        errors.push('API Key不能为空');
      }
      if (!serviceConfig.config.baseUrl?.trim()) {
        errors.push('基础URL不能为空');
      }
      if (!serviceConfig.config.model?.trim()) {
        errors.push('模型名称不能为空');
      }
      break;
    case AI_SERVICE_TYPES.OLLAMA:
      if (!serviceConfig.config.baseUrl?.trim()) {
        errors.push('Ollama服务地址不能为空');
      }
      if (!serviceConfig.config.model?.trim()) {
        errors.push('模型名称不能为空');
      }
      break;
    case AI_SERVICE_TYPES.LMSTUDIO:
      if (!serviceConfig.config.baseUrl?.trim()) {
        errors.push('LM Studio服务地址不能为空');
      }
      if (!serviceConfig.config.model?.trim()) {
        errors.push('模型名称不能为空');
      }
      break;
    case AI_SERVICE_TYPES.CUSTOM:
      if (!serviceConfig.config.baseUrl?.trim()) {
        errors.push('服务地址不能为空');
      }
      if (!serviceConfig.config.model?.trim()) {
        errors.push('模型名称不能为空');
      }
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// 获取活动的AI服务
export const getActiveService = (aiConfig) => {
  if (!aiConfig.activeServiceId || !aiConfig.services) {
    return null;
  }
  return aiConfig.services.find(service => service.id === aiConfig.activeServiceId) || null;
};

// 获取所有启用的服务
export const getEnabledServices = (aiConfig) => {
  return aiConfig.services?.filter(service => service.enabled) || [];
};

// 设置默认服务
export const setDefaultService = (aiConfig, serviceId) => {
  const updatedServices = aiConfig.services.map(service => ({
    ...service,
    isDefault: service.id === serviceId
  }));

  return {
    ...aiConfig,
    services: updatedServices,
    activeServiceId: serviceId
  };
};
