import React, { useState } from 'react';
import { X, Save, AlertCircle } from 'lucide-react';
import { AI_SERVICE_TYPES, getDefaultConfigForType, validateAIServiceConfig } from '../../types/aiConfig';

// 添加服务对话框
export const AddServiceDialog = ({ service, onServiceChange, onSave, onCancel }) => {
  const [errors, setErrors] = useState([]);

  const handleSave = () => {
    const validation = validateAIServiceConfig(service);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }
    setErrors([]);
    onSave();
  };

  const handleTypeChange = (newType) => {
    const defaultConfig = getDefaultConfigForType(newType);
    onServiceChange({
      ...service,
      type: newType,
      config: defaultConfig
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">添加AI服务</h2>
          <button
            onClick={onCancel}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-md"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* 错误提示 */}
          {errors.length > 0 && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <AlertCircle size={16} className="text-red-600" />
                <span className="text-sm font-medium text-red-800">配置验证失败</span>
              </div>
              <ul className="text-sm text-red-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* 基本信息 */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                服务名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={service.name}
                onChange={(e) => onServiceChange({ ...service, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="例如：GPT-4 服务"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                服务类型 <span className="text-red-500">*</span>
              </label>
              <select
                value={service.type}
                onChange={(e) => handleTypeChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={AI_SERVICE_TYPES.OPENAI_COMPATIBLE}>OpenAI兼容接口</option>
                <option value={AI_SERVICE_TYPES.OLLAMA}>Ollama本地服务</option>
                <option value={AI_SERVICE_TYPES.LMSTUDIO}>LM Studio本地服务</option>
                <option value={AI_SERVICE_TYPES.CUSTOM}>自定义服务</option>
              </select>
            </div>

            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={service.enabled}
                  onChange={(e) => onServiceChange({ ...service, enabled: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">启用此服务</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={service.supportsMultimodal}
                  onChange={(e) => onServiceChange({ ...service, supportsMultimodal: e.target.checked })}
                  className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
                <span className="text-sm text-gray-700">支持多模态（图片）</span>
              </label>
            </div>
          </div>

          {/* 服务配置 */}
          <ServiceConfigForm
            type={service.type}
            config={service.config}
            onChange={(config) => onServiceChange({ ...service, config })}
          />
        </div>

        <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Save size={16} />
            <span>保存</span>
          </button>
        </div>
      </div>
    </div>
  );
};

// 编辑服务对话框
export const EditServiceDialog = ({ service, onSave, onCancel }) => {
  const [editedService, setEditedService] = useState({ ...service });
  const [errors, setErrors] = useState([]);

  const handleSave = () => {
    const validation = validateAIServiceConfig(editedService);
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }
    setErrors([]);
    onSave(editedService);
  };

  const handleTypeChange = (newType) => {
    const defaultConfig = getDefaultConfigForType(newType);
    setEditedService({
      ...editedService,
      type: newType,
      config: defaultConfig
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">编辑AI服务</h2>
          <button
            onClick={onCancel}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-md"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* 错误提示 */}
          {errors.length > 0 && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <AlertCircle size={16} className="text-red-600" />
                <span className="text-sm font-medium text-red-800">配置验证失败</span>
              </div>
              <ul className="text-sm text-red-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* 基本信息 */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                服务名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={editedService.name}
                onChange={(e) => setEditedService({ ...editedService, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="例如：GPT-4 服务"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                服务类型 <span className="text-red-500">*</span>
              </label>
              <select
                value={editedService.type}
                onChange={(e) => handleTypeChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={AI_SERVICE_TYPES.OPENAI_COMPATIBLE}>OpenAI兼容接口</option>
                <option value={AI_SERVICE_TYPES.OLLAMA}>Ollama本地服务</option>
                <option value={AI_SERVICE_TYPES.LMSTUDIO}>LM Studio本地服务</option>
                <option value={AI_SERVICE_TYPES.CUSTOM}>自定义服务</option>
              </select>
            </div>

            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={editedService.enabled}
                  onChange={(e) => setEditedService({ ...editedService, enabled: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">启用此服务</span>
              </label>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={editedService.supportsMultimodal}
                  onChange={(e) => setEditedService({ ...editedService, supportsMultimodal: e.target.checked })}
                  className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
                <span className="text-sm text-gray-700">支持多模态（图片）</span>
              </label>
            </div>
          </div>

          {/* 服务配置 */}
          <ServiceConfigForm
            type={editedService.type}
            config={editedService.config}
            onChange={(config) => setEditedService({ ...editedService, config })}
          />
        </div>

        <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Save size={16} />
            <span>保存</span>
          </button>
        </div>
      </div>
    </div>
  );
};

// 服务配置表单组件
const ServiceConfigForm = ({ type, config, onChange }) => {
  const updateConfig = (key, value) => {
    onChange({ ...config, [key]: value });
  };

  switch (type) {
    case AI_SERVICE_TYPES.OPENAI_COMPATIBLE:
      return (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">OpenAI兼容接口配置</h3>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              API Key <span className="text-red-500">*</span>
            </label>
            <input
              type="password"
              value={config.apiKey || ''}
              onChange={(e) => updateConfig('apiKey', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="sk-..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              基础URL <span className="text-red-500">*</span>
            </label>
            <input
              type="url"
              value={config.baseUrl || ''}
              onChange={(e) => updateConfig('baseUrl', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://api.openai.com/v1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              模型名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={config.model || ''}
              onChange={(e) => updateConfig('model', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="gpt-3.5-turbo"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                温度 (Temperature)
              </label>
              <input
                type="number"
                min="0"
                max="2"
                step="0.1"
                value={config.temperature || 0.7}
                onChange={(e) => updateConfig('temperature', parseFloat(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                最大令牌数
              </label>
              <input
                type="number"
                min="1"
                max="32000"
                value={config.maxTokens || 2000}
                onChange={(e) => updateConfig('maxTokens', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      );

    case AI_SERVICE_TYPES.OLLAMA:
      return (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Ollama本地服务配置</h3>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              服务地址 <span className="text-red-500">*</span>
            </label>
            <input
              type="url"
              value={config.baseUrl || ''}
              onChange={(e) => updateConfig('baseUrl', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="http://localhost:11434"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              模型名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={config.model || ''}
              onChange={(e) => updateConfig('model', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="llama3.1:8b"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              温度 (Temperature)
            </label>
            <input
              type="number"
              min="0"
              max="2"
              step="0.1"
              value={config.temperature || 0.7}
              onChange={(e) => updateConfig('temperature', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>提示：</strong>请确保Ollama服务已启动并已下载相应的模型。
              可以使用命令 <code className="bg-blue-200 px-1 rounded">ollama pull {config.model || 'model-name'}</code> 下载模型。
            </p>
          </div>
        </div>
      );

    case AI_SERVICE_TYPES.LMSTUDIO:
      return (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">LM Studio本地服务配置</h3>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              服务地址 <span className="text-red-500">*</span>
            </label>
            <input
              type="url"
              value={config.baseUrl || ''}
              onChange={(e) => updateConfig('baseUrl', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="http://localhost:1234"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              模型名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={config.model || ''}
              onChange={(e) => updateConfig('model', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="llama3.1:8b"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              温度 (Temperature)
            </label>
            <input
              type="number"
              min="0"
              max="2"
              step="0.1"
              value={config.temperature || 0.7}
              onChange={(e) => updateConfig('temperature', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>提示：</strong>请确保LM Studio服务已启动并已加载相应的模型。
              LM Studio默认端口为1234，如需修改请在LM Studio设置中更改。
            </p>
          </div>
        </div>
      );

    case AI_SERVICE_TYPES.CUSTOM:
      return (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">自定义服务配置</h3>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              API Key
            </label>
            <input
              type="password"
              value={config.apiKey || ''}
              onChange={(e) => updateConfig('apiKey', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="可选，根据服务要求填写"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              服务地址 <span className="text-red-500">*</span>
            </label>
            <input
              type="url"
              value={config.baseUrl || ''}
              onChange={(e) => updateConfig('baseUrl', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://your-api-endpoint.com/v1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              模型名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={config.model || ''}
              onChange={(e) => updateConfig('model', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="your-model-name"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                温度 (Temperature)
              </label>
              <input
                type="number"
                min="0"
                max="2"
                step="0.1"
                value={config.temperature || 0.7}
                onChange={(e) => updateConfig('temperature', parseFloat(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                最大令牌数
              </label>
              <input
                type="number"
                min="1"
                max="32000"
                value={config.maxTokens || 2000}
                onChange={(e) => updateConfig('maxTokens', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              自定义请求头 (JSON格式)
            </label>
            <textarea
              value={JSON.stringify(config.headers || {}, null, 2)}
              onChange={(e) => {
                try {
                  const headers = JSON.parse(e.target.value);
                  updateConfig('headers', headers);
                } catch (error) {
                  // 忽略JSON解析错误，让用户继续编辑
                }
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows="3"
              placeholder='{"Authorization": "Bearer your-token"}'
            />
          </div>
        </div>
      );

    default:
      return <div>未知的服务类型</div>;
  }
};
