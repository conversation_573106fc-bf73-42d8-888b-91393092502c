import React, { useState, useEffect } from 'react';
import { Plus, Edit2, Trash2, Save, Search, FileText, Upload, Wrench, X } from 'lucide-react';
import { getActiveService } from '../../types/aiConfig';

const RAGManagement = ({ backendBase, aiConfig }) => {
  // 从新配置格式中获取当前活跃的服务配置
  const getEffectiveAIConfig = () => {
    if (!aiConfig) return null;
    
    // 如果是新格式配置
    if (aiConfig.version === '2.0' && aiConfig.services) {
      const activeService = getActiveService(aiConfig);
      if (activeService && activeService.config) {
        return {
          api_key: activeService.config.apiKey,
          base_url: activeService.config.baseUrl,
          model: activeService.config.model,
          use_ollama: activeService.type === 'ollama'
        };
      }
      return null;
    }
    
    // 如果是旧格式配置，直接使用
    if (aiConfig.api_key && aiConfig.base_url && aiConfig.model) {
      return aiConfig;
    }
    
    return null;
  };

  const effectiveAIConfig = getEffectiveAIConfig();
  
  // 调试日志
  console.log('[RAG_DEBUG] aiConfig:', aiConfig);
  console.log('[RAG_DEBUG] effectiveAIConfig:', effectiveAIConfig);
  
  const [ragData, setRagData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    category: '',
    source: '',
    workflows: []
  });
  const [toast, setToast] = useState(null);
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [uploadFile, setUploadFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [workflows, setWorkflows] = useState([]);
  const [showWorkflowSelector, setShowWorkflowSelector] = useState(false);

  // 显示提示信息
  const showToast = (message, type = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  // 构建请求头的通用函数
  const buildHeaders = (includeContentType = false) => {
    const headers = {};
    if (includeContentType) {
      headers['Content-Type'] = 'application/json';
    }
    if (effectiveAIConfig && effectiveAIConfig.api_key && effectiveAIConfig.base_url && effectiveAIConfig.model) {
      headers['X-API-Key'] = effectiveAIConfig.api_key;
      headers['X-Base-URL'] = effectiveAIConfig.base_url;
      headers['X-Model'] = effectiveAIConfig.model;
    }
    return headers;
  };

  // 获取内部微工作流列表
  const fetchWorkflows = async () => {
    try {
      const response = await fetch(`${backendBase}/agent/v2/workflows/list`, {
        method: 'GET',
        headers: buildHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setWorkflows(result.data || []);
      } else {
        console.error(`获取内部微工作流失败: ${result.error}`);
      }
    } catch (error) {
      console.error(`获取内部微工作流失败: ${error.message}`);
    }
  };

  // 获取RAG数据列表
  const fetchRAGData = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/audit/rag/list`, {
        method: 'GET',
        headers: buildHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setRagData(result.data || []);
      } else {
        showToast(`获取RAG数据失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`获取RAG数据失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 上传规章制度文档
  const uploadRegulationDocument = async () => {
    if (!uploadFile) {
      showToast('请选择要上传的文档', 'error');
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', uploadFile);

      const response = await fetch(`${backendBase}/agent/v2/audit/rag/upload`, {
        method: 'POST',
        headers: buildHeaders(),
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast(`文档上传成功：${result.message}`);
        setShowUploadForm(false);
        setUploadFile(null);
        fetchRAGData();
      } else {
        showToast(`文档上传失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`文档上传失败: ${error.message}`, 'error');
    } finally {
      setIsUploading(false);
    }
  };

  // 添加RAG数据
  const addRAGData = async () => {
    if (!formData.title || !formData.content) {
      showToast('请填写标题和内容', 'error');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/audit/rag/add`, {
        method: 'POST',
        headers: buildHeaders(true),
        body: JSON.stringify({
          data: [formData]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('RAG数据添加成功');
        setShowAddForm(false);
        setFormData({ title: '', content: '', category: '', source: '', workflows: [] });
        fetchRAGData();
      } else {
        showToast(`添加RAG数据失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`添加RAG数据失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 更新RAG数据
  const updateRAGData = async () => {
    if (!formData.title || !formData.content) {
      showToast('请填写标题和内容', 'error');
      return;
    }

    setIsLoading(true);
    try {
      // 如果是规定类型，构建规定格式的数据
      let updateData = formData;
      if (editingItem.metadata.type === "regulation_rule") {
        updateData = {
          ...formData,
          type: "regulation_rule",
          rule_type: editingItem.metadata.rule_type,
          rule_description: editingItem.metadata.rule_description,
          rule_content: formData.content
        };
      }

      const response = await fetch(`${backendBase}/agent/v2/audit/rag/${editingItem.id}`, {
        method: 'PUT',
        headers: buildHeaders(true),
        body: JSON.stringify({
          data: updateData
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('RAG数据更新成功');
        setEditingItem(null);
        setFormData({ title: '', content: '', category: '', source: '', workflows: [] });
        fetchRAGData();
      } else {
        showToast(`更新RAG数据失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`更新RAG数据失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 删除RAG数据
  const deleteRAGData = async (id) => {
    if (!window.confirm('确定要删除这条RAG数据吗？')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/audit/rag/${id}`, {
        method: 'DELETE',
        headers: buildHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('RAG数据删除成功');
        fetchRAGData();
      } else {
        showToast(`删除RAG数据失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`删除RAG数据失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 开始编辑
  const startEdit = (item) => {
    setEditingItem(item);
    
    // 如果是规定类型，使用规定内容作为表单内容
    if (item.metadata.type === "regulation_rule") {
      setFormData({
        title: item.metadata.title,
        content: item.metadata.rule_content,
        category: item.metadata.category,
        source: item.metadata.source,
        workflows: item.metadata.workflows || []
      });
    } else {
      setFormData({
        title: item.metadata.title,
        content: item.content,
        category: item.metadata.category,
        source: item.metadata.source,
        workflows: item.metadata.workflows || []
      });
    }
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditingItem(null);
    setFormData({ title: '', content: '', category: '', source: '', workflows: [] });
  };

  // 处理表单变化
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // 添加微工作流到规则
  const addWorkflowToRule = (tool) => {
    const workflowsArray = Array.isArray(formData.workflows) ? formData.workflows : [];
    if (!workflowsArray.find(t => t.id === tool.id)) {
      setFormData(prev => ({
        ...prev,
        workflows: [...(Array.isArray(prev.workflows) ? prev.workflows : []), { id: tool.id, name: tool.name }]
      }));
    }
    setShowWorkflowSelector(false);
  };

  // 从规则中移除微工作流
  const removeWorkflowFromRule = (toolId) => {
    setFormData(prev => ({
      ...prev,
      workflows: (Array.isArray(prev.workflows) ? prev.workflows : []).filter(t => t.id !== toolId)
    }));
  };

  // 处理微工作流点击事件，跳转到微工作流管理页面
  const handleWorkflowClick = (workflow) => {
    console.log('handleWorkflowClick called with workflow:', workflow);
    // 检查是否在Electron环境中
    const isElectron = typeof window !== 'undefined' && window.electronAPI;
    console.log('isElectron:', isElectron);
    
    if (isElectron) {
      // 在Electron环境中，通过设置activeFeature和activeSetting来切换到微工作流管理页面
      // 首先切换到设置功能
      if (typeof window !== 'undefined') {
        // 触发一个自定义事件，通知主应用切换到微工作流管理
        const event = new CustomEvent('navigate-to-workflow', {
          detail: {
            workflowId: workflow.id || workflow.workflow_name,
            workflowName: workflow.name || workflow.workflow_name
          }
        });
        console.log('Dispatching navigate-to-workflow event:', event);
        window.dispatchEvent(event);
      }
    } else {
      // 在Web环境中，使用URL参数或路由来切换页面
      // 这里可以根据实际的路由实现进行调整
      console.log('跳转到微工作流:', workflow);
      // 例如: window.location.href = `/settings?tab=internal-tools&workflow=${workflow.id}`;
    }
  };

  // 提交表单
  const handleSubmit = (e) => {
    e.preventDefault();
    if (editingItem) {
      updateRAGData();
    } else {
      addRAGData();
    }
  };

  // 处理文件选择
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setUploadFile(e.target.files[0]);
    }
  };

  // 过滤数据
  const filteredData = ragData.filter(item => {
    const searchLower = searchTerm.toLowerCase();
    return (
      item.metadata.title.toLowerCase().includes(searchLower) ||
      item.content.toLowerCase().includes(searchLower) ||
      item.metadata.category.toLowerCase().includes(searchLower)
    );
  });

  // 同步AI配置到后端
  const syncAIConfigToBackend = async () => {
    if (aiConfig && aiConfig.version === '2.0' && aiConfig.services) {
      try {
        console.log('[RAG_DEBUG] 同步AI配置到后端:', aiConfig);
        const response = await fetch(`${backendBase}/ai/config`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(aiConfig)
        });
        
        if (response.ok) {
          console.log('[RAG_DEBUG] AI配置同步成功');
        } else {
          console.error('[RAG_DEBUG] AI配置同步失败:', response.status);
        }
      } catch (error) {
        console.error('[RAG_DEBUG] AI配置同步错误:', error);
      }
    }
  };

  // 初始化
  useEffect(() => {
    syncAIConfigToBackend();
    fetchRAGData();
    fetchWorkflows();
  }, []);

  return (
    <div className="flex flex-col h-full">
        {/* 头部 */}
        <div className="mb-6 flex-shrink-0">
          <p className="text-sm text-gray-600">管理单据审核相关的规章制度数据</p>
          {!effectiveAIConfig && (
            <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                ⚠️ 未检测到有效的AI配置，部分功能可能无法使用。请先在设置中配置AI服务。
              </p>
            </div>
          )}
        </div>

        {/* 搜索和添加按钮 */}
        <div className="flex items-center justify-between mb-6 flex-shrink-0">
          <div className="relative w-96">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
              placeholder="搜索规章制度..."
            />
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() => setShowUploadForm(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Upload size={18} />
              <span>上传文档</span>
            </button>
            <button
              onClick={() => setShowAddForm(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <Plus size={18} />
              <span>添加规章制度</span>
            </button>
          </div>
        </div>

        {/* 上传表单 */}
        {showUploadForm && (
          <div className="mb-6 p-6 bg-gray-50 rounded-xl border border-gray-200 flex-shrink-0">
            <h3 className="text-lg font-semibold mb-4">上传规章制度文档</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  选择文档 <span className="text-red-500">*</span>
                </label>
                <input
                  type="file"
                  onChange={handleFileChange}
                  className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 transition-all duration-200"
                  accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.bmp,.tiff,.tif,.webp"
                />
                <p className="mt-1 text-sm text-gray-500">支持PDF、Word、TXT文件和图片格式</p>
              </div>
              {uploadFile && (
                <div className="p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">已选择文件: {uploadFile.name}</p>
                </div>
              )}
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowUploadForm(false);
                    setUploadFile(null);
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-xl transition-all duration-200"
                >
                  取消
                </button>
                <button
                  type="button"
                  onClick={uploadRegulationDocument}
                  disabled={isUploading || !uploadFile}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUploading ? (
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  ) : (
                    <>
                      <Upload size={16} />
                      <span>上传并解析</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 表单 */}
        {(showAddForm || editingItem) && (
          <div className="mb-6 p-6 bg-gray-50 rounded-xl border border-gray-200 flex-shrink-0">
            <h3 className="text-lg font-semibold mb-4">
              {editingItem ? '编辑规章制度' : '添加规章制度'}
            </h3>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    标题 <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleFormChange}
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                    placeholder="请输入标题"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    分类
                  </label>
                  <input
                    type="text"
                    name="category"
                    value={formData.category}
                    onChange={handleFormChange}
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                    placeholder="请输入分类"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  来源
                </label>
                <input
                  type="text"
                  name="source"
                  value={formData.source}
                  onChange={handleFormChange}
                  className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                  placeholder="请输入来源"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  内容 <span className="text-red-500">*</span>
                </label>
                <textarea
                  name="content"
                  value={formData.content}
                  onChange={handleFormChange}
                  rows={4}
                  className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200 resize-y"
                  placeholder="请输入内容"
                  required
                />
              </div>

              {/* 微工作流关联 */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-900">
                    关联微工作流
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowWorkflowSelector(true)}
                    className="text-sm text-blue-600 hover:text-blue-800 flex items-center space-x-1"
                  >
                    <Wrench size={14} />
                    <span>添加微工作流</span>
                  </button>
                </div>
                
                {/* 已选择的微工作流 */}
                {formData.workflows && (
                  <div className="flex flex-wrap gap-2 mb-2">
                    {(() => {
                      try {
                        // 尝试解析JSON字符串
                        const workflows = typeof formData.workflows === 'string'
                          ? JSON.parse(formData.workflows)
                          : formData.workflows;
                        
                        // 确保workflows是数组
                        const workflowsArray = Array.isArray(workflows) ? workflows : [];
                        
                        return workflowsArray.map((workflow, index) => (
                          <div
                            key={workflow.id || workflow.workflow_name || index}
                            className="flex items-center space-x-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                          >
                            <Wrench size={12} />
                            <span>{workflow.name || workflow.workflow_name || '工作流'}</span>
                            <button
                              type="button"
                              onClick={() => removeWorkflowFromRule(workflow.id || workflow.workflow_name || index)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              <X size={12} />
                            </button>
                          </div>
                        ));
                      } catch (error) {
                        console.error('解析formData.workflows失败:', error);
                        return <span className="text-xs text-red-500">工作流数据解析失败</span>;
                      }
                    })()}
                  </div>
                )}
                
                {/* 微工作流选择器 */}
                {showWorkflowSelector && (
                  <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-900">选择内部微工作流</h4>
                      <button
                        type="button"
                        onClick={() => setShowWorkflowSelector(false)}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        <X size={16} />
                      </button>
                    </div>
                    <div className="max-h-40 overflow-y-auto space-y-1">
                      {workflows.length === 0 ? (
                        <p className="text-sm text-gray-500 text-center py-2">暂无可用微工作流</p>
                      ) : (
                        workflows
                          .filter(workflow => {
                            if (!formData.workflows) return true;
                            const workflowsArray = Array.isArray(formData.workflows) ? formData.workflows : [];
                            return !workflowsArray.find(w => w.id === workflow.id);
                          })
                          .map((workflow) => (
                            <button
                              key={workflow.id}
                              type="button"
                              onClick={() => addWorkflowToRule(workflow)}
                              className="w-full text-left p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
                            >
                              <div className="font-medium text-sm text-gray-900">{workflow.name}</div>
                              <div className="text-xs text-gray-500 truncate">{workflow.description}</div>
                            </button>
                          ))
                      )}
                    </div>
                  </div>
                )}
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddForm(false);
                    cancelEdit();
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-xl transition-all duration-200"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  ) : (
                    <>
                      <Save size={16} />
                      <span>{editingItem ? '更新' : '添加'}</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* 数据列表 */}
        <div className="flex-1 overflow-y-auto min-h-0">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="w-8 h-8 border-2 border-purple-500/30 border-t-purple-500 rounded-full animate-spin"></div>
            </div>
          ) : filteredData.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500">
              <FileText size={48} className="mb-4" />
              <p className="text-lg">暂无RAG数据</p>
              <p className="text-sm">点击"上传文档"或"添加规章制度"按钮添加数据</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {filteredData.map((item) => (
                <div key={item.id} className="p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-all duration-200">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{item.metadata.title}</h3>
                        {item.metadata.category && (
                          <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                            {item.metadata.category}
                          </span>
                        )}
                        {item.metadata.type === "regulation_rule" && (
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                            规定
                          </span>
                        )}
                      </div>
                      
                      {/* 如果是规定类型，显示规定内容 */}
                      {item.metadata.type === "regulation_rule" ? (
                        <div className="mb-2">
                          <div className="text-sm text-gray-500 mb-1">
                            {item.metadata.rule_description}
                          </div>
                          <div className="p-3 bg-blue-50 border-l-4 border-blue-500 rounded-r-lg">
                            <p className="text-gray-800">{item.metadata.rule_content}</p>
                          </div>
                        </div>
                      ) : (
                        <p className="text-gray-600 mb-2 line-clamp-3">{item.content}</p>
                      )}
                      
                      {/* 关联的微工作流 */}
                      {item.metadata.workflows && (
                        <div className="mb-2">
                          <div className="text-sm text-gray-500 mb-1">关联微工作流:</div>
                          <div className="flex flex-wrap gap-1">
                            {(() => {
                              try {
                                // 尝试解析JSON字符串
                                const workflows = typeof item.metadata.workflows === 'string'
                                  ? JSON.parse(item.metadata.workflows)
                                  : item.metadata.workflows;
                                
                                // 确保workflows是数组
                                const workflowsArray = Array.isArray(workflows) ? workflows : [];
                                
                                return workflowsArray.map((workflow, index) => (
                                  <span
                                    key={workflow.id || workflow.workflow_name || index}
                                    className="inline-flex items-center space-x-1 px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs cursor-pointer hover:bg-green-200 transition-colors duration-200"
                                    onClick={() => handleWorkflowClick(workflow)}
                                    title="点击查看微工作流详情"
                                  >
                                    <Wrench size={10} />
                                    <span>{workflow.name || workflow.workflow_name || '工作流'}</span>
                                  </span>
                                ));
                              } catch (error) {
                                console.error('解析workflows失败:', error);
                                return <span className="text-xs text-red-500">工作流数据解析失败</span>;
                              }
                            })()}
                          </div>
                        </div>
                      )}
                      
                      {item.metadata.source && (
                        <p className="text-sm text-gray-500">来源: {item.metadata.source}</p>
                      )}
                    </div>
                    <div className="flex space-x-2 ml-4">
                      <button
                        onClick={() => startEdit(item)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-xl transition-all duration-200"
                        title="编辑"
                      >
                        <Edit2 size={16} />
                      </button>
                      <button
                        onClick={() => deleteRAGData(item.id)}
                        className="p-2 text-red-600 hover:bg-red-50 rounded-xl transition-all duration-200"
                        title="删除"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Toast */}
        {toast && (
          <div className={`fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg text-white ${
            toast.type === 'error' ? 'bg-red-500' :
            toast.type === 'success' ? 'bg-green-500' :
            'bg-blue-500'
          }`}>
            {toast.message}
          </div>
        )}
    </div>
  );
};

export default RAGManagement;