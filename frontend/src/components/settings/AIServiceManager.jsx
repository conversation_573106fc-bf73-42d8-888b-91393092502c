import React, { useState, useEffect } from 'react';
import { Plus, Trash2, Edit3, TestTube, Save, X, Check, AlertCircle, Cloud, HardDrive, Database, Settings as SettingsIcon } from 'lucide-react';
import { 
  AI_SERVICE_TYPES, 
  createAIServiceConfig, 
  getDefaultConfigForType, 
  validateAIServiceConfig,
  generateServiceId
} from '../../types/aiConfig';
import api from '../../api';
import { AddServiceDialog, EditServiceDialog } from './AIServiceDialogs';

const AIServiceManager = ({ aiConfig, setAiConfig, backendBase }) => {
  const [services, setServices] = useState([]);
  const [activeServiceId, setActiveServiceId] = useState(null);
  const [showAddService, setShowAddService] = useState(false);
  const [editingService, setEditingService] = useState(null);
  const [testingServices, setTestingServices] = useState(new Set());
  const [testResults, setTestResults] = useState({});

  // 新服务表单状态
  const [newService, setNewService] = useState(() => 
    createAIServiceConfig({
      name: '',
      type: AI_SERVICE_TYPES.OPENAI_COMPATIBLE,
      enabled: true
    })
  );

  // 从aiConfig初始化服务列表
  useEffect(() => {
    if (aiConfig?.services) {
      setServices(aiConfig.services);
      setActiveServiceId(aiConfig.activeServiceId);
    }
  }, [aiConfig]);

  // 保存配置到父组件
  const saveConfig = (updatedServices, updatedActiveServiceId = activeServiceId) => {
    const newConfig = {
      ...aiConfig,
      services: updatedServices,
      activeServiceId: updatedActiveServiceId
    };
    setAiConfig(newConfig);
    localStorage.setItem('aiConfig', JSON.stringify(newConfig));
  };

  // 添加新服务
  const handleAddService = () => {
    const validation = validateAIServiceConfig(newService);
    if (!validation.isValid) {
      alert('配置验证失败：\n' + validation.errors.join('\n'));
      return;
    }

    const serviceWithId = {
      ...newService,
      id: generateServiceId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const updatedServices = [...services, serviceWithId];
    setServices(updatedServices);
    
    // 如果是第一个服务，设为活动服务
    const newActiveServiceId = services.length === 0 ? serviceWithId.id : activeServiceId;
    setActiveServiceId(newActiveServiceId);
    
    saveConfig(updatedServices, newActiveServiceId);
    
    // 重置表单
    setNewService(createAIServiceConfig({
      name: '',
      type: AI_SERVICE_TYPES.OPENAI_COMPATIBLE,
      enabled: true
    }));
    setShowAddService(false);
  };

  // 删除服务
  const handleDeleteService = (serviceId) => {
    if (!confirm('确定要删除这个AI服务配置吗？')) return;
    
    const updatedServices = services.filter(s => s.id !== serviceId);
    setServices(updatedServices);
    
    // 如果删除的是活动服务，选择第一个可用服务
    let newActiveServiceId = activeServiceId;
    if (activeServiceId === serviceId) {
      newActiveServiceId = updatedServices.length > 0 ? updatedServices[0].id : null;
      setActiveServiceId(newActiveServiceId);
    }
    
    saveConfig(updatedServices, newActiveServiceId);
  };

  // 更新服务
  const handleUpdateService = (serviceId, updates) => {
    const updatedServices = services.map(service => 
      service.id === serviceId 
        ? { ...service, ...updates, updatedAt: new Date().toISOString() }
        : service
    );
    setServices(updatedServices);
    saveConfig(updatedServices);
  };

  // 测试服务连接
  const handleTestService = async (service) => {
    setTestingServices(prev => new Set([...prev, service.id]));
    
    try {
      const response = await fetch(`${backendBase}/ai/services/${service.id}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(service),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '连接测试失败');
      }
      
      const result = await response.json();
      
      // 更新连接状态
      handleUpdateService(service.id, { 
        connectionStatus: 'success',
        lastTested: new Date().toISOString()
      });
      
      setTestResults(prev => ({
        ...prev,
        [service.id]: { success: true, message: result.message }
      }));
      
    } catch (error) {
      // 更新连接状态
      handleUpdateService(service.id, { 
        connectionStatus: 'error',
        lastTested: new Date().toISOString()
      });
      
      setTestResults(prev => ({
        ...prev,
        [service.id]: { success: false, message: error.message }
      }));
    } finally {
      setTestingServices(prev => {
        const newSet = new Set(prev);
        newSet.delete(service.id);
        return newSet;
      });
      
      // 3秒后清除测试结果
      setTimeout(() => {
        setTestResults(prev => {
          const newResults = { ...prev };
          delete newResults[service.id];
          return newResults;
        });
      }, 3000);
    }
  };



  // 获取服务类型图标
  const getServiceTypeIcon = (type) => {
    switch (type) {
      case AI_SERVICE_TYPES.OLLAMA:
        return <HardDrive size={16} />;
      case AI_SERVICE_TYPES.OPENAI_COMPATIBLE:
        return <Cloud size={16} />;
      case AI_SERVICE_TYPES.LMSTUDIO:
        return <Database size={16} />;
      default:
        return <SettingsIcon size={16} />;
    }
  };

  // 获取服务类型名称
  const getServiceTypeName = (type) => {
    switch (type) {
      case AI_SERVICE_TYPES.OLLAMA:
        return 'Ollama本地';
      case AI_SERVICE_TYPES.OPENAI_COMPATIBLE:
        return 'OpenAI兼容';
      case AI_SERVICE_TYPES.LMSTUDIO:
        return 'LM Studio本地';
      case AI_SERVICE_TYPES.CUSTOM:
        return '自定义';
      default:
        return '未知';
    }
  };

  return (
    <div className="space-y-6">
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">AI服务管理</h2>
          <p className="text-sm text-gray-600 mt-1">管理您的AI服务配置，支持多种服务类型</p>
        </div>
        <button
          onClick={() => setShowAddService(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus size={16} />
          <span>添加服务</span>
        </button>
      </div>

      {/* 服务列表 */}
      <div className="space-y-4">
        {services.map((service) => (
          <ServiceCard
            key={service.id}
            service={service}
            isActive={service.id === activeServiceId}
            isTesting={testingServices.has(service.id)}
            testResult={testResults[service.id]}

            onTest={() => handleTestService(service)}
            onEdit={() => setEditingService(service)}
            onDelete={() => handleDeleteService(service.id)}
            onUpdate={(updates) => handleUpdateService(service.id, updates)}
            getServiceTypeIcon={getServiceTypeIcon}
            getServiceTypeName={getServiceTypeName}
          />
        ))}
        
        {services.length === 0 && (
          <div className="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <Cloud size={48} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无AI服务</h3>
            <p className="text-gray-500 mb-4">添加您的第一个AI服务配置</p>
            <button
              onClick={() => setShowAddService(true)}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus size={16} />
              <span>添加服务</span>
            </button>
          </div>
        )}
      </div>

      {/* 添加服务对话框 */}
      {showAddService && (
        <AddServiceDialog
          service={newService}
          onServiceChange={setNewService}
          onSave={handleAddService}
          onCancel={() => {
            setShowAddService(false);
            setNewService(createAIServiceConfig({
              name: '',
              type: AI_SERVICE_TYPES.OPENAI_COMPATIBLE,
              enabled: true
            }));
          }}
        />
      )}

      {/* 编辑服务对话框 */}
      {editingService && (
        <EditServiceDialog
          service={editingService}
          onSave={(updates) => {
            handleUpdateService(editingService.id, updates);
            setEditingService(null);
          }}
          onCancel={() => setEditingService(null)}
        />
      )}
    </div>
  );
};

// 服务卡片组件
const ServiceCard = ({
  service,
  isActive,
  isTesting,
  testResult,
  onTest,
  onEdit,
  onDelete,
  onUpdate,
  getServiceTypeIcon,
  getServiceTypeName
}) => {
  const getStatusColor = () => {
    if (isTesting) return 'text-yellow-500';
    switch (service.connectionStatus) {
      case 'success': return 'text-green-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = () => {
    if (isTesting) return <div className="w-3 h-3 border-2 border-yellow-500/30 border-t-yellow-500 rounded-full animate-spin" />;
    switch (service.connectionStatus) {
      case 'success': return <Check size={12} className="text-green-500" />;
      case 'error': return <AlertCircle size={12} className="text-red-500" />;
      default: return <div className="w-3 h-3 bg-gray-400 rounded-full" />;
    }
  };

  return (
    <div className={`p-4 rounded-lg border transition-all ${
      isActive
        ? 'border-blue-200 bg-blue-50'
        : 'border-gray-200 bg-white hover:border-gray-300'
    }`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
            isActive ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
          }`}>
            {getServiceTypeIcon(service.type)}
          </div>
          <div>
            <div className="flex items-center space-x-2">
              <h3 className="font-medium text-gray-900">{service.name}</h3>
              {service.supportsMultimodal && (
                <span className="px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded-full">
                  多模态
                </span>
              )}
              {isActive && (
                <span className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded-full">
                  当前使用
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2 mt-1">
              <span className="text-sm text-gray-500">{getServiceTypeName(service.type)}</span>
              <span className="text-gray-300">•</span>
              <span className="text-sm text-gray-500">{service.config.model}</span>
              <div className="flex items-center space-x-1">
                {getStatusIcon()}
                <span className={`text-xs ${getStatusColor()}`}>
                  {isTesting ? '测试中' :
                   service.connectionStatus === 'success' ? '已连接' :
                   service.connectionStatus === 'error' ? '连接失败' : '未测试'}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* 启用/禁用切换 */}
          <div className="relative inline-block w-10 h-5">
            <input
              type="checkbox"
              checked={service.enabled}
              onChange={(e) => onUpdate({ enabled: e.target.checked })}
              className="opacity-0 w-0 h-0 peer"
              id={`service-enabled-${service.id}`}
            />
            <label
              htmlFor={`service-enabled-${service.id}`}
              className="absolute cursor-pointer top-0 left-0 right-0 bottom-0 bg-gray-300 rounded-full transition-all duration-300 peer-checked:bg-blue-600 peer-checked:before:translate-x-5 before:absolute before:h-3 before:w-3 before:left-1 before:bottom-1 before:bg-white before:rounded-full before:transition-all before:duration-300"
            />
          </div>



          <button
            onClick={onTest}
            disabled={isTesting}
            className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
            title="测试连接"
          >
            <TestTube size={16} />
          </button>

          <button
            onClick={onEdit}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
            title="编辑"
          >
            <Edit3 size={16} />
          </button>

          <button
            onClick={onDelete}
            className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
            title="删除"
          >
            <Trash2 size={16} />
          </button>
        </div>
      </div>

      {/* 测试结果 */}
      {testResult && (
        <div className={`mt-3 p-3 rounded-md ${
          testResult.success
            ? 'bg-green-50 border border-green-200 text-green-800'
            : 'bg-red-50 border border-red-200 text-red-800'
        }`}>
          <div className="flex items-center space-x-2">
            {testResult.success ? <Check size={16} /> : <AlertCircle size={16} />}
            <span className="text-sm font-medium">
              {testResult.success ? '连接成功' : '连接失败'}
            </span>
          </div>
          <p className="text-sm mt-1">{testResult.message}</p>
        </div>
      )}
    </div>
  );
};

export default AIServiceManager;
