import React, { useState, useEffect } from 'react';
import { Plus, Edit2, Trash2, Save, Search, Play, Code, Settings, FileText, AlertCircle, Wrench, Bot, X } from 'lucide-react';
import { getActiveService } from '../../types/aiConfig';

const InternalToolsManagement = ({ backendBase, aiConfig }) => {
  // 从新配置格式中获取当前活跃的服务配置
  const getEffectiveAIConfig = () => {
    if (!aiConfig) return null;
    
    // 如果是新格式配置
    if (aiConfig.version === '2.0' && aiConfig.services) {
      const activeService = getActiveService(aiConfig);
      if (activeService && activeService.config) {
        return {
          api_key: activeService.config.apiKey,
          base_url: activeService.config.baseUrl,
          model: activeService.config.model,
          use_ollama: activeService.type === 'ollama'
        };
      }
      return null;
    }
    
    // 如果是旧格式配置，直接使用
    if (aiConfig.api_key && aiConfig.base_url && aiConfig.model) {
      return aiConfig;
    }
    
    return null;
  };

  const effectiveAIConfig = getEffectiveAIConfig();
  
  // 调试日志
  console.log('[InternalTools_DEBUG] aiConfig:', aiConfig);
  console.log('[InternalTools_DEBUG] effectiveAIConfig:', effectiveAIConfig);
  
  const [tools, setTools] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [showAICreateForm, setShowAICreateForm] = useState(false);
  const [aiToolDescription, setAiToolDescription] = useState('');
  const [isGeneratingTool, setIsGeneratingTool] = useState(false);
  const [editingTool, setEditingTool] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    inputs: [{ name: '', type: 'string', description: '', required: false }],
    outputs: [{ name: '', type: 'string', description: '' }],
    script: '',
    enabled: true,
    version: '1.0'
  });
  const [toast, setToast] = useState(null);
  const [executeResult, setExecuteResult] = useState(null);
  const [executeInputs, setExecuteInputs] = useState({});
  const [showExecuteForm, setShowExecuteForm] = useState(false);
  const [executingToolId, setExecutingToolId] = useState(null);

  // 显示提示信息
  const showToast = (message, type = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  // 构建请求头的通用函数
  const buildHeaders = (includeContentType = false) => {
    const headers = {};
    if (includeContentType) {
      headers['Content-Type'] = 'application/json';
    }
    if (effectiveAIConfig && effectiveAIConfig.api_key && effectiveAIConfig.base_url && effectiveAIConfig.model) {
      headers['X-API-Key'] = effectiveAIConfig.api_key;
      headers['X-Base-URL'] = effectiveAIConfig.base_url;
      headers['X-Model'] = effectiveAIConfig.model;
    }
    return headers;
  };

  // 获取内部工具列表
  const fetchTools = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/internal-tools/list`, {
        method: 'GET',
        headers: buildHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setTools(result.data || []);
      } else {
        showToast(`获取内部工具失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`获取内部工具失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 添加内部工具
  const addTool = async () => {
    if (!formData.name || !formData.description || !formData.script) {
      showToast('请填写名称、描述和脚本', 'error');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/internal-tools/add`, {
        method: 'POST',
        headers: buildHeaders(true),
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('内部工具添加成功');
        setShowAddForm(false);
        resetFormData();
        fetchTools();
      } else {
        showToast(`添加内部工具失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`添加内部工具失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 更新内部工具
  const updateTool = async () => {
    if (!formData.name || !formData.description || !formData.script) {
      showToast('请填写名称、描述和脚本', 'error');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/internal-tools/${editingTool.id}`, {
        method: 'PUT',
        headers: buildHeaders(true),
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('内部工具更新成功');
        setEditingTool(null);
        resetFormData();
        fetchTools();
      } else {
        showToast(`更新内部工具失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`更新内部工具失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 删除内部工具
  const deleteTool = async (id) => {
    if (!window.confirm('确定要删除这个内部工具吗？')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/internal-tools/${id}`, {
        method: 'DELETE',
        headers: buildHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('内部工具删除成功');
        fetchTools();
      } else {
        showToast(`删除内部工具失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`删除内部工具失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 执行内部工具
  const executeTool = async (toolId, inputs) => {
    setExecutingToolId(toolId);
    try {
      const response = await fetch(`${backendBase}/agent/v2/internal-tools/${toolId}/execute`, {
        method: 'POST',
        headers: buildHeaders(true),
        body: JSON.stringify({ inputs })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setExecuteResult({
          success: true,
          data: result.data,
          message: result.message
        });
      } else {
        setExecuteResult({
          success: false,
          error: result.error
        });
      }
    } catch (error) {
      setExecuteResult({
        success: false,
        error: error.message
      });
    } finally {
      setExecutingToolId(null);
    }
  };

  // 开始编辑
  const startEdit = (tool) => {
    setEditingTool(tool);
    setFormData({
      name: tool.name,
      description: tool.description,
      inputs: tool.inputs || [{ name: '', type: 'string', description: '', required: false }],
      outputs: tool.outputs || [{ name: '', type: 'string', description: '' }],
      script: tool.script || '',
      enabled: tool.enabled !== false,
      version: tool.version || '1.0'
    });
  };

  // 开始执行
  const startExecute = (tool) => {
    setExecutingToolId(tool.id);
    setShowExecuteForm(true);
    
    // 初始化输入参数
    const inputs = {};
    (tool.inputs || []).forEach(input => {
      if (input.required !== false) {
        inputs[input.name] = '';
      }
    });
    setExecuteInputs(inputs);
    setExecuteResult(null);
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditingTool(null);
    resetFormData();
  };

  // 取消执行
  const cancelExecute = () => {
    setShowExecuteForm(false);
    setExecutingToolId(null);
    setExecuteInputs({});
    setExecuteResult(null);
  };

  // 重置表单数据
  const resetFormData = () => {
    setFormData({
      name: '',
      description: '',
      inputs: [{ name: '', type: 'string', description: '', required: false }],
      outputs: [{ name: '', type: 'string', description: '' }],
      script: '',
      enabled: true,
      version: '1.0'
    });
  };

  // 处理表单变化
  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({ 
      ...prev, 
      [name]: type === 'checkbox' ? checked : value 
    }));
  };

  // 处理输入参数变化
  const handleInputChange = (e, index, field) => {
    const { value, type, checked } = e.target;
    const newInputs = [...formData.inputs];
    newInputs[index] = {
      ...newInputs[index],
      [field]: type === 'checkbox' ? checked : value
    };
    setFormData(prev => ({ ...prev, inputs: newInputs }));
  };

  // 处理输出参数变化
  const handleOutputChange = (e, index, field) => {
    const { value } = e.target;
    const newOutputs = [...formData.outputs];
    newOutputs[index] = {
      ...newOutputs[index],
      [field]: value
    };
    setFormData(prev => ({ ...prev, outputs: newOutputs }));
  };

  // 添加输入参数
  const addInput = () => {
    setFormData(prev => ({
      ...prev,
      inputs: [...prev.inputs, { name: '', type: 'string', description: '', required: false }]
    }));
  };

  // 删除输入参数
  const removeInput = (index) => {
    if (formData.inputs.length > 1) {
      const newInputs = [...formData.inputs];
      newInputs.splice(index, 1);
      setFormData(prev => ({ ...prev, inputs: newInputs }));
    }
  };

  // 添加输出参数
  const addOutput = () => {
    setFormData(prev => ({
      ...prev,
      outputs: [...prev.outputs, { name: '', type: 'string', description: '' }]
    }));
  };

  // 删除输出参数
  const removeOutput = (index) => {
    if (formData.outputs.length > 1) {
      const newOutputs = [...formData.outputs];
      newOutputs.splice(index, 1);
      setFormData(prev => ({ ...prev, outputs: newOutputs }));
    }
  };

  // 处理执行输入变化
  const handleExecuteInputChange = (e, paramName) => {
    const { value } = e.target;
    setExecuteInputs(prev => ({ ...prev, [paramName]: value }));
  };

  // 提交表单
  const handleSubmit = (e) => {
    e.preventDefault();
    if (editingTool) {
      updateTool();
    } else {
      addTool();
    }
  };

  // 提交执行
  const handleExecuteSubmit = (e) => {
    e.preventDefault();
    executeTool(executingToolId, executeInputs);
  };

  // 过滤数据
  const filteredTools = tools.filter(tool => {
    const searchLower = searchTerm.toLowerCase();
    return (
      tool.name.toLowerCase().includes(searchLower) ||
      tool.description.toLowerCase().includes(searchLower)
    );
  });

  // 同步AI配置到后端
  const syncAIConfigToBackend = async () => {
    if (aiConfig && aiConfig.version === '2.0' && aiConfig.services) {
      try {
        console.log('[InternalTools_DEBUG] 同步AI配置到后端:', aiConfig);
        const response = await fetch(`${backendBase}/ai/config`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(aiConfig)
        });
        
        if (response.ok) {
          console.log('[InternalTools_DEBUG] AI配置同步成功');
        } else {
          console.error('[InternalTools_DEBUG] AI配置同步失败:', response.status);
        }
      } catch (error) {
        console.error('[InternalTools_DEBUG] AI配置同步错误:', error);
      }
    }
  };

  // 处理AI创建工具
  const handleAICreateTool = async (e) => {
    e.preventDefault();
    
    if (!aiToolDescription.trim()) {
      showToast('请输入工具描述', 'error');
      return;
    }

    setIsGeneratingTool(true);
    
    try {
      // 调用后端AI创建工具API
      const response = await fetch(`${backendBase}/agent/v2/internal-tools/ai-create`, {
        method: 'POST',
        headers: buildHeaders(true),
        body: JSON.stringify({
          description: aiToolDescription
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        const toolData = result.data;
        
        // 验证必要字段
        if (!toolData.name || !toolData.description || !toolData.script) {
          throw new Error('AI生成的工具数据不完整');
        }
        
        // 设置表单数据
        setFormData({
          name: toolData.name,
          description: toolData.description,
          inputs: toolData.inputs || [{ name: '', type: 'string', description: '', required: false }],
          outputs: toolData.outputs || [{ name: '', type: 'string', description: '' }],
          script: toolData.script,
          enabled: true,
          version: '1.0'
        });
        
        // 关闭AI创建表单，打开添加表单
        setShowAICreateForm(false);
        setShowAddForm(true);
        setAiToolDescription('');
        
        showToast('AI已生成工具脚本，请检查并保存');
      } else {
        showToast(`AI生成工具失败: ${result.error}`, 'error');
      }
    } catch (error) {
      console.error('AI创建工具失败:', error);
      showToast(`AI创建工具失败: ${error.message}`, 'error');
    } finally {
      setIsGeneratingTool(false);
    }
  };

  // 初始化
  useEffect(() => {
    syncAIConfigToBackend();
    fetchTools();
  }, []);

  return (
    <div className="flex flex-col h-full">
      {/* 头部 */}
      <div className="mb-6 flex-shrink-0">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">内部工具管理</h2>
        <p className="text-sm text-gray-600">管理智能体使用的内部工具，每个工具包含名称、描述、输入、输出、脚本等</p>
        {!effectiveAIConfig && (
          <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              ⚠️ 未检测到有效的AI配置，部分功能可能无法使用。请先在设置中配置AI服务。
            </p>
          </div>
        )}
      </div>

      {/* 搜索和添加按钮 */}
      <div className="flex items-center justify-between mb-6 flex-shrink-0">
        <div className="relative w-96">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
            placeholder="搜索内部工具..."
          />
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowAddForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Plus size={18} />
            <span>添加内部工具</span>
          </button>
          <button
            onClick={() => setShowAICreateForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Bot size={18} />
            <span>AI 创建工具</span>
          </button>
        </div>
      </div>

      {/* 执行结果 */}
      {executeResult && (
        <div className={`mb-6 p-4 rounded-xl border ${
          executeResult.success 
            ? 'bg-green-50 border-green-200' 
            : 'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-start space-x-3">
            <AlertCircle className={`mt-0.5 ${
              executeResult.success ? 'text-green-500' : 'text-red-500'
            }`} size={20} />
            <div>
              <h3 className={`font-medium ${
                executeResult.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {executeResult.success ? '执行成功' : '执行失败'}
              </h3>
              {executeResult.success ? (
                <pre className="mt-2 text-sm text-green-700 bg-green-100 p-3 rounded-lg overflow-auto max-h-60">
                  {JSON.stringify(executeResult.data, null, 2)}
                </pre>
              ) : (
                <p className="mt-1 text-sm text-red-700">{executeResult.error}</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 添加/编辑表单 */}
      {(showAddForm || editingTool) && (
        <div className="mb-6 p-6 bg-gray-50 rounded-xl border border-gray-200 flex-shrink-0">
          <h3 className="text-lg font-semibold mb-4">
            {editingTool ? '编辑内部工具' : '添加内部工具'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  名称 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleFormChange}
                  className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                  placeholder="请输入工具名称"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  版本
                </label>
                <input
                  type="text"
                  name="version"
                  value={formData.version}
                  onChange={handleFormChange}
                  className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                  placeholder="请输入版本号"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">
                描述 <span className="text-red-500">*</span>
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleFormChange}
                rows={2}
                className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                placeholder="请输入工具描述"
                required
              />
            </div>

            {/* 输入参数 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-900">
                  输入参数
                </label>
                <button
                  type="button"
                  onClick={addInput}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  + 添加参数
                </button>
              </div>
              <div className="space-y-3">
                {formData.inputs.map((input, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-start">
                    <div className="col-span-3">
                      <input
                        type="text"
                        value={input.name}
                        onChange={(e) => handleInputChange(e, index, 'name')}
                        className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                        placeholder="参数名"
                      />
                    </div>
                    <div className="col-span-2">
                      <select
                        value={input.type}
                        onChange={(e) => handleInputChange(e, index, 'type')}
                        className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                      >
                        <option value="string">字符串</option>
                        <option value="number">数字</option>
                        <option value="boolean">布尔值</option>
                        <option value="object">对象</option>
                        <option value="array">数组</option>
                      </select>
                    </div>
                    <div className="col-span-5">
                      <input
                        type="text"
                        value={input.description}
                        onChange={(e) => handleInputChange(e, index, 'description')}
                        className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                        placeholder="参数描述"
                      />
                    </div>
                    <div className="col-span-1 flex items-center">
                      <label className="flex items-center text-sm text-gray-700">
                        <input
                          type="checkbox"
                          checked={input.required || false}
                          onChange={(e) => handleInputChange(e, index, 'required')}
                          className="mr-1"
                        />
                        必填
                      </label>
                    </div>
                    <div className="col-span-1 flex justify-end">
                      {formData.inputs.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeInput(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 size={16} />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 输出参数 */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-900">
                  输出参数
                </label>
                <button
                  type="button"
                  onClick={addOutput}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  + 添加参数
                </button>
              </div>
              <div className="space-y-3">
                {formData.outputs.map((output, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 items-start">
                    <div className="col-span-3">
                      <input
                        type="text"
                        value={output.name}
                        onChange={(e) => handleOutputChange(e, index, 'name')}
                        className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                        placeholder="参数名"
                      />
                    </div>
                    <div className="col-span-2">
                      <select
                        value={output.type}
                        onChange={(e) => handleOutputChange(e, index, 'type')}
                        className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                      >
                        <option value="string">字符串</option>
                        <option value="number">数字</option>
                        <option value="boolean">布尔值</option>
                        <option value="object">对象</option>
                        <option value="array">数组</option>
                      </select>
                    </div>
                    <div className="col-span-6">
                      <input
                        type="text"
                        value={output.description}
                        onChange={(e) => handleOutputChange(e, index, 'description')}
                        className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                        placeholder="参数描述"
                      />
                    </div>
                    <div className="col-span-1 flex justify-end">
                      {formData.outputs.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeOutput(index)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 size={16} />
                        </button>
                      )}
              
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">
                脚本 <span className="text-red-500">*</span>
              </label>
              <textarea
                name="script"
                value={formData.script}
                onChange={handleFormChange}
                rows={10}
                className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200 font-mono text-sm"
                placeholder="请输入Python脚本，必须包含execute函数"
                required
              />
              <p className="mt-1 text-sm text-gray-500">
                脚本必须包含一个execute函数，接收inputs参数，返回执行结果
              </p>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="enabled"
                name="enabled"
                checked={formData.enabled}
                onChange={handleFormChange}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="enabled" className="ml-2 text-sm text-gray-700">
                启用此工具
              </label>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  setEditingTool(null);
                  resetFormData();
                }}
                className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-xl transition-all duration-200"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                ) : (
                  <Save size={16} />
                )}
                <span>{editingTool ? '更新' : '添加'}</span>
              </button>
            </div>
          </form>
        </div>
      )}

      {/* AI 创建工具表单 */}
      {showAICreateForm && (
        <div className="mb-6 p-6 bg-gray-50 rounded-xl border border-gray-200 flex-shrink-0">
          <h3 className="text-lg font-semibold mb-4">AI 创建工具</h3>
          <form onSubmit={handleAICreateTool} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-1">
                描述您想要创建的工具 <span className="text-red-500">*</span>
              </label>
              <textarea
                value={aiToolDescription}
                onChange={(e) => setAiToolDescription(e.target.value)}
                rows={4}
                className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                placeholder="例如：创建一个工具，用于查询数据库中的用户信息..."
                required
              />
              <p className="mt-1 text-sm text-gray-500">
                请详细描述工具的功能、输入参数和预期输出，AI 将根据您的描述生成工具脚本
              </p>
            </div>
  
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => {
                  setShowAICreateForm(false);
                  setAiToolDescription('');
                }}
                className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-xl transition-all duration-200"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isGeneratingTool || !aiToolDescription.trim()}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGeneratingTool ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                ) : (
                  <Bot size={16} />
                )}
                <span>生成工具</span>
              </button>
            </div>
          </form>
        </div>
      )}

      {/* 执行表单 */}
      {showExecuteForm && executingToolId && (
        <div className="mb-6 p-6 bg-gray-50 rounded-xl border border-gray-200 flex-shrink-0">
          <h3 className="text-lg font-semibold mb-4">执行内部工具</h3>
          <form onSubmit={handleExecuteSubmit} className="space-y-4">
            {(() => {
              const tool = tools.find(t => t.id === executingToolId);
              if (!tool) return null;
              
              return (
                <>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">{tool.name}</h4>
                    <p className="text-sm text-gray-600 mb-4">{tool.description}</p>
                  </div>
                  
                  {(tool.inputs || []).map((input, index) => (
                    input.required !== false && (
                      <div key={index}>
                        <label className="block text-sm font-medium text-gray-900 mb-1">
                          {input.name}
                          {input.required && <span className="text-red-500">*</span>}
                          <span className="text-xs text-gray-500 ml-1">({input.type})</span>
                        </label>
                        <input
                          type={input.type === 'number' ? 'number' : 'text'}
                          value={executeInputs[input.name] || ''}
                          onChange={(e) => handleExecuteInputChange(e, input.name)}
                          className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                          placeholder={input.description || `请输入${input.name}`}
                          required={input.required}
                        />
                      </div>
                    )
                  ))}
                </>
              );
            })()}
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={cancelExecute}
                className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-xl transition-all duration-200"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={executingToolId}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {executingToolId ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                ) : (
                  <Play size={16} />
                )}
                <span>执行</span>
              </button>
            </div>
          </form>
        </div>
      )}

      {/* 工具列表 */}
      <div className="flex-1 overflow-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : filteredTools.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-gray-500">
            <Settings size={48} className="mb-4" />
            <p className="text-lg">暂无内部工具</p>
            <p className="text-sm mt-2">点击"添加内部工具"按钮创建新的工具</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTools.map((tool) => (
              <div
                key={tool.id}
                className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <div className={`p-2 rounded-lg ${
                      tool.enabled ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'
                    }`}>
                      <Code size={16} />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{tool.name}</h3>
                      <p className="text-xs text-gray-500">v{tool.version || '1.0'}</p>
                    </div>
                  </div>
                  <div className="flex space-x-1">
                    <button
                      onClick={() => startEdit(tool)}
                      className="p-1.5 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                    >
                      <Edit2 size={14} />
                    </button>
                    <button
                      onClick={() => deleteTool(tool.id)}
                      className="p-1.5 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                </div>
                
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{tool.description}</p>
                
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-xs text-gray-500">
                    <FileText size={12} className="mr-1" />
                    <span>输入: {(tool.inputs || []).length} 个参数</span>
                  </div>
                  <div className="flex items-center text-xs text-gray-500">
                    <FileText size={12} className="mr-1" />
                    <span>输出: {(tool.outputs || []).length} 个参数</span>
                  </div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    tool.enabled 
                      ? 'bg-green-100 text-green-700' 
                      : 'bg-gray-100 text-gray-700'
                  }`}>
                    {tool.enabled ? '已启用' : '已禁用'}
                  </span>
                  <button
                    onClick={() => startExecute(tool)}
                    disabled={!tool.enabled}
                    className="flex items-center space-x-1 px-3 py-1.5 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white text-sm font-medium rounded-lg shadow hover:shadow-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Play size={12} />
                    <span>执行</span>
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 提示信息 */}
      {toast && (
        <div className={`fixed bottom-4 right-4 px-4 py-3 rounded-lg shadow-lg z-50 ${
          toast.type === 'error' 
            ? 'bg-red-500 text-white' 
            : toast.type === 'success'
              ? 'bg-green-500 text-white'
              : 'bg-blue-500 text-white'
        }`}>
          <div className="flex items-center space-x-2">
            <span>{toast.message}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default InternalToolsManagement;