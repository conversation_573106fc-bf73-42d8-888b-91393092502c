import React, { useState, useEffect } from 'react';
import { Plus, Edit2, Trash2, Save, Search, Play, Settings, AlertCircle, Workflow, Bot, X, ArrowRight, ArrowDown, ArrowUp } from 'lucide-react';
import { getActiveService } from '../../types/aiConfig';

const WorkflowManagement = ({ backendBase, aiConfig }) => {
  // 从新配置格式中获取当前活跃的服务配置
  const getEffectiveAIConfig = () => {
    if (!aiConfig) return null;
    
    // 如果是新格式配置
    if (aiConfig.version === '2.0' && aiConfig.services) {
      const activeService = getActiveService(aiConfig);
      if (activeService && activeService.config) {
        return {
          api_key: activeService.config.apiKey,
          base_url: activeService.config.baseUrl,
          model: activeService.config.model,
          use_ollama: activeService.type === 'ollama'
        };
      }
      return null;
    }
    
    // 如果是旧格式配置，直接使用
    if (aiConfig.api_key && aiConfig.base_url && aiConfig.model) {
      return aiConfig;
    }
    
    return null;
  };

  const effectiveAIConfig = getEffectiveAIConfig();
  
  const [workflows, setWorkflows] = useState([]);
  const [builtinTools, setBuiltinTools] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingWorkflow, setEditingWorkflow] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    inputs: [],
    outputs: [],
    steps: []
  });
  const [toast, setToast] = useState(null);
  const [executeResult, setExecuteResult] = useState(null);
  const [executeInputs, setExecuteInputs] = useState({});
  const [showExecuteForm, setShowExecuteForm] = useState(false);
  const [executingWorkflowId, setExecutingWorkflowId] = useState(null);
  const [selectedTool, setSelectedTool] = useState(null);
  const [showToolSelector, setShowToolSelector] = useState(false);
  const [editingStepIndex, setEditingStepIndex] = useState(-1);
  const [showAIGenerateForm, setShowAIGenerateForm] = useState(false);
  const [aiWorkflowDescription, setAiWorkflowDescription] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [targetWorkflowId, setTargetWorkflowId] = useState(null);

  // 监听从RAG管理跳转过来的微工作流ID
  useEffect(() => {
    const handleNavigateToWorkflow = (event) => {
      console.log('WorkflowManagement received navigate-to-workflow event:', event.detail);
      if (event.detail && event.detail.workflowId) {
        console.log('Setting target workflow ID to:', event.detail.workflowId);
        setTargetWorkflowId(event.detail.workflowId);
      }
    };

    window.addEventListener('navigate-to-workflow', handleNavigateToWorkflow);
    
    return () => {
      window.removeEventListener('navigate-to-workflow', handleNavigateToWorkflow);
    };
  }, []);

  // 当工作流列表加载完成且目标工作流ID存在时，滚动到目标工作流
  useEffect(() => {
    if (targetWorkflowId && workflows.length > 0) {
      // 查找目标工作流
      const targetWorkflow = workflows.find(w => w.id === targetWorkflowId || w.name === targetWorkflowId);
      if (targetWorkflow) {
        // 延迟一下确保DOM已经渲染
        setTimeout(() => {
          const element = document.getElementById(`workflow-${targetWorkflow.id}`);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            // 高亮显示目标工作流
            element.classList.add('ring-2', 'ring-blue-500');
            setTimeout(() => {
              element.classList.remove('ring-2', 'ring-blue-500');
            }, 2000);
          }
        }, 100);
      }
      setTargetWorkflowId(null); // 重置目标工作流ID
    }
  }, [workflows, targetWorkflowId]);

  // 显示提示信息
  const showToast = (message, type = 'info') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  // 构建请求头的通用函数
  const buildHeaders = (includeContentType = false) => {
    const headers = {};
    if (includeContentType) {
      headers['Content-Type'] = 'application/json';
    }
    if (effectiveAIConfig && effectiveAIConfig.api_key && effectiveAIConfig.base_url && effectiveAIConfig.model) {
      headers['X-API-Key'] = effectiveAIConfig.api_key;
      headers['X-Base-URL'] = effectiveAIConfig.base_url;
      headers['X-Model'] = effectiveAIConfig.model;
    }
    return headers;
  };

  // 获取内置工具列表
  const fetchBuiltinTools = async () => {
    try {
      const response = await fetch(`${backendBase}/agent/v2/workflows/builtin-tools`, {
        method: 'GET',
        headers: buildHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setBuiltinTools(result.data || []);
      } else {
        showToast(`获取内置工具失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`获取内置工具失败: ${error.message}`, 'error');
    }
  };

  // 获取工作流列表
  const fetchWorkflows = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/workflows/list`, {
        method: 'GET',
        headers: buildHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setWorkflows(result.data || []);
      } else {
        showToast(`获取工作流失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`获取工作流失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 创建工作流
  const createWorkflow = async () => {
    if (!formData.name || !formData.description) {
      showToast('请填写名称和描述', 'error');
      return;
    }

    if (formData.steps.length === 0) {
      showToast('请至少添加一个步骤', 'error');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/workflows/create`, {
        method: 'POST',
        headers: buildHeaders(true),
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('工作流创建成功');
        setShowAddForm(false);
        resetFormData();
        fetchWorkflows();
      } else {
        showToast(`创建工作流失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`创建工作流失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 更新工作流
  const updateWorkflow = async () => {
    if (!formData.name || !formData.description) {
      showToast('请填写名称和描述', 'error');
      return;
    }

    if (formData.steps.length === 0) {
      showToast('请至少添加一个步骤', 'error');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/workflows/${editingWorkflow.id}`, {
        method: 'PUT',
        headers: buildHeaders(true),
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('工作流更新成功');
        setEditingWorkflow(null);
        resetFormData();
        fetchWorkflows();
      } else {
        showToast(`更新工作流失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`更新工作流失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 删除工作流
  const deleteWorkflow = async (id) => {
    if (!window.confirm('确定要删除这个工作流吗？')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/workflows/${id}`, {
        method: 'DELETE',
        headers: buildHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        showToast('工作流删除成功');
        fetchWorkflows();
      } else {
        showToast(`删除工作流失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`删除工作流失败: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // 执行工作流
  const executeWorkflow = async (workflowId, inputs) => {
    setExecutingWorkflowId(workflowId);
    try {
      const response = await fetch(`${backendBase}/agent/v2/workflows/${workflowId}/execute`, {
        method: 'POST',
        headers: buildHeaders(true),
        body: JSON.stringify({ inputs })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        setExecuteResult({
          success: true,
          data: result.data,
          message: result.message
        });
      } else {
        setExecuteResult({
          success: false,
          error: result.error
        });
      }
    } catch (error) {
      setExecuteResult({
        success: false,
        error: error.message
      });
    } finally {
      setExecutingWorkflowId(null);
    }
  };

  // 开始编辑
  const startEdit = (workflow) => {
    setEditingWorkflow(workflow);
    setFormData({
      name: workflow.name,
      description: workflow.description,
      inputs: workflow.inputs || [],
      outputs: workflow.outputs || [],
      steps: workflow.steps || []
    });
  };

  // 开始执行
  const startExecute = (workflow) => {
    setExecutingWorkflowId(workflow.id);
    setShowExecuteForm(true);
    
    // 使用工作流定义的输入参数
    const workflowInputs = {};
    if (workflow.inputs && workflow.inputs.length > 0) {
      workflow.inputs.forEach(input => {
        workflowInputs[input.name] = '';
      });
    }
    
    setExecuteInputs(workflowInputs);
    setExecuteResult(null);
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditingWorkflow(null);
    resetFormData();
  };

  // 取消执行
  const cancelExecute = () => {
    setShowExecuteForm(false);
    setExecutingWorkflowId(null);
    setExecuteInputs({});
    setExecuteResult(null);
  };

  // 重置表单数据
  const resetFormData = () => {
    setFormData({
      name: '',
      description: '',
      inputs: [],
      outputs: [],
      steps: []
    });
  };

  // 处理表单变化
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ 
      ...prev, 
      [name]: value 
    }));
  };

  // 添加步骤
  const addStep = (tool) => {
    const newStep = {
      step_id: `step_${formData.steps.length + 1}`,
      tool_id: tool.id,
      inputs: {},
      output_mapping: {}
    };
    
    setFormData(prev => ({
      ...prev,
      steps: [...prev.steps, newStep]
    }));
    
    setShowToolSelector(false);
    setSelectedTool(null);
  };

  // 删除步骤
  const removeStep = (index) => {
    const newSteps = [...formData.steps];
    newSteps.splice(index, 1);
    setFormData(prev => ({ ...prev, steps: newSteps }));
  };

  // 移动步骤
  const moveStep = (index, direction) => {
    const newSteps = [...formData.steps];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    
    if (targetIndex >= 0 && targetIndex < newSteps.length) {
      [newSteps[index], newSteps[targetIndex]] = [newSteps[targetIndex], newSteps[index]];
      setFormData(prev => ({ ...prev, steps: newSteps }));
    }
  };

  // 更新步骤输入
  const updateStepInput = (stepIndex, inputName, value) => {
    const newSteps = [...formData.steps];
    newSteps[stepIndex].inputs[inputName] = value;
    setFormData(prev => ({ ...prev, steps: newSteps }));
  };

  // 添加工作流输入参数
  const addWorkflowInput = () => {
    setFormData(prev => ({
      ...prev,
      inputs: [...prev.inputs, { name: '', type: 'string', description: '', required: false }]
    }));
  };

  // 删除工作流输入参数
  const removeWorkflowInput = (index) => {
    if (formData.inputs.length > 0) {
      const newInputs = [...formData.inputs];
      newInputs.splice(index, 1);
      setFormData(prev => ({ ...prev, inputs: newInputs }));
    }
  };

  // 更新工作流输入参数
  const updateWorkflowInput = (index, field, value) => {
    const newInputs = [...formData.inputs];
    newInputs[index] = {
      ...newInputs[index],
      [field]: typeof value === 'boolean' ? value : value
    };
    setFormData(prev => ({ ...prev, inputs: newInputs }));
  };

  // 添加工作流输出参数
  const addWorkflowOutput = () => {
    setFormData(prev => ({
      ...prev,
      outputs: [...prev.outputs, { name: '', type: 'string', description: '' }]
    }));
  };

  // 删除工作流输出参数
  const removeWorkflowOutput = (index) => {
    if (formData.outputs.length > 0) {
      const newOutputs = [...formData.outputs];
      newOutputs.splice(index, 1);
      setFormData(prev => ({ ...prev, outputs: newOutputs }));
    }
  };

  // 更新工作流输出参数
  const updateWorkflowOutput = (index, field, value) => {
    const newOutputs = [...formData.outputs];
    newOutputs[index] = {
      ...newOutputs[index],
      [field]: value
    };
    setFormData(prev => ({ ...prev, outputs: newOutputs }));
  };

  // 处理执行输入变化
  const handleExecuteInputChange = (e, paramName) => {
    const { value } = e.target;
    setExecuteInputs(prev => ({ ...prev, [paramName]: value }));
  };

  // 提交表单
  const handleSubmit = (e) => {
    e.preventDefault();
    if (editingWorkflow) {
      updateWorkflow();
    } else {
      createWorkflow();
    }
  };

  // 提交执行
  const handleExecuteSubmit = (e) => {
    e.preventDefault();
    executeWorkflow(executingWorkflowId, executeInputs);
  };

  // AI生成工作流
  const generateWorkflowWithAI = async () => {
    if (!aiWorkflowDescription.trim()) {
      showToast('请输入工作流描述', 'error');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch(`${backendBase}/agent/v2/workflows/ai-generate`, {
        method: 'POST',
        headers: buildHeaders(true),
        body: JSON.stringify({ description: aiWorkflowDescription })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      if (result.success) {
        // 使用AI生成的工作流数据填充表单
        const workflowData = result.data;
        setFormData({
          name: workflowData.name || '',
          description: workflowData.description || '',
          inputs: workflowData.inputs || [],
          outputs: workflowData.outputs || [],
          steps: workflowData.steps || []
        });
        
        // 关闭AI生成表单，显示添加/编辑表单
        setShowAIGenerateForm(false);
        setShowAddForm(true);
        setAiWorkflowDescription('');
        
        showToast('AI生成工作流成功，请检查并保存');
      } else {
        showToast(`AI生成工作流失败: ${result.error}`, 'error');
      }
    } catch (error) {
      showToast(`AI生成工作流失败: ${error.message}`, 'error');
    } finally {
      setIsGenerating(false);
    }
  };

  // 取消AI生成
  const cancelAIGenerate = () => {
    setShowAIGenerateForm(false);
    setAiWorkflowDescription('');
  };

  // 过滤数据
  const filteredWorkflows = workflows.filter(workflow => {
    const searchLower = searchTerm.toLowerCase();
    return (
      workflow.name.toLowerCase().includes(searchLower) ||
      workflow.description.toLowerCase().includes(searchLower)
    );
  });

  // 按分类分组内置工具
  const toolsByCategory = builtinTools.reduce((acc, tool) => {
    const category = tool.category || 'general';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(tool);
    return acc;
  }, {});

  // 初始化
  useEffect(() => {
    fetchBuiltinTools();
    fetchWorkflows();
  }, []);

  return (
    <div className="flex flex-col h-full">
      {/* Toast 提示 */}
      {toast && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
          toast.type === 'error' ? 'bg-red-500 text-white' : 'bg-green-500 text-white'
        }`}>
          {toast.message}
        </div>
      )}

      {/* 头部 */}
      <div className="mb-6 flex-shrink-0">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">微工作流管理</h2>
        <p className="text-sm text-gray-600">通过组合内置工具创建微工作流，替代手动编写Python脚本</p>
        {!effectiveAIConfig && (
          <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              ⚠️ 未检测到有效的AI配置，部分功能可能无法使用。请先在设置中配置AI服务。
            </p>
          </div>
        )}
      </div>

      {/* 搜索和添加按钮 */}
      <div className="flex items-center justify-between mb-6 flex-shrink-0">
        <div className="relative w-96">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
            placeholder="搜索工作流..."
          />
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowAddForm(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <Plus size={18} />
            <span>创建工作流</span>
          </button>
          <button
            onClick={() => setShowAIGenerateForm(true)}
            disabled={!effectiveAIConfig}
            className={`flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            <Bot size={18} />
            <span>AI生成工作流</span>
          </button>
        </div>
      </div>

      {/* 执行结果 */}
      {executeResult && (
        <div className={`mb-6 p-4 rounded-xl border ${
          executeResult.success 
            ? 'bg-green-50 border-green-200' 
            : 'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-start space-x-3">
            <AlertCircle className={`mt-0.5 ${
              executeResult.success ? 'text-green-500' : 'text-red-500'
            }`} size={20} />
            <div className="flex-1">
              <h3 className={`font-medium ${
                executeResult.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {executeResult.success ? '执行成功' : '执行失败'}
              </h3>
              {executeResult.success ? (
                <div className="mt-2">
                  <pre className="text-sm text-green-700 bg-green-100 p-3 rounded-lg overflow-auto max-h-60">
                    {JSON.stringify(executeResult.data, null, 2)}
                  </pre>
                </div>
              ) : (
                <p className="mt-1 text-sm text-red-700">{executeResult.error}</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 工作流列表 */}
      <div className="flex-1 overflow-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="w-8 h-8 border-4 border-purple-200 border-t-purple-500 rounded-full animate-spin"></div>
          </div>
        ) : filteredWorkflows.length === 0 ? (
          <div className="text-center py-12">
            <Workflow className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无工作流</h3>
            <p className="text-gray-500 mb-4">创建您的第一个微工作流</p>
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
            >
              <Plus size={16} className="mr-2" />
              创建工作流
            </button>
          </div>
        ) : (
          <div className="grid gap-4">
            {filteredWorkflows.map((workflow) => (
              <div
                key={workflow.id}
                id={`workflow-${workflow.id}`}
                className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-200"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <Workflow className="text-purple-500" size={20} />
                      <h3 className="text-lg font-semibold text-gray-900">{workflow.name}</h3>
                      <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                        {workflow.steps?.length || 0} 步骤
                      </span>
                    </div>
                    <p className="text-gray-600 mb-3">{workflow.description}</p>
                    
                    {/* 步骤预览 */}
                    {workflow.steps && workflow.steps.length > 0 && (
                      <div className="flex items-center space-x-2 mb-3">
                        <span className="text-sm text-gray-500">步骤:</span>
                        {workflow.steps.slice(0, 3).map((step, index) => {
                          const tool = builtinTools.find(t => t.id === step.tool_id);
                          return (
                            <div key={index} className="flex items-center space-x-1">
                              <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                {tool?.name || step.tool_id}
                              </span>
                              {index < Math.min(workflow.steps.length - 1, 2) && (
                                <ArrowRight size={12} className="text-gray-400" />
                              )}
                            </div>
                          );
                        })}
                        {workflow.steps.length > 3 && (
                          <span className="text-xs text-gray-500">...</span>
                        )}
                      </div>
                    )}
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>创建时间: {new Date(workflow.created_at).toLocaleString()}</span>
                      {workflow.updated_at !== workflow.created_at && (
                        <span>更新时间: {new Date(workflow.updated_at).toLocaleString()}</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => startExecute(workflow)}
                      disabled={executingWorkflowId === workflow.id}
                      className="flex items-center space-x-1 px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white text-sm rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {executingWorkflowId === workflow.id ? (
                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      ) : (
                        <Play size={14} />
                      )}
                      <span>执行</span>
                    </button>
                    <button
                      onClick={() => startEdit(workflow)}
                      className="flex items-center space-x-1 px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg transition-colors"
                    >
                      <Edit2 size={14} />
                      <span>编辑</span>
                    </button>
                    <button
                      onClick={() => deleteWorkflow(workflow.id)}
                      className="flex items-center space-x-1 px-3 py-1.5 bg-red-500 hover:bg-red-600 text-white text-sm rounded-lg transition-colors"
                    >
                      <Trash2 size={14} />
                      <span>删除</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* AI生成工作流表单 */}
      {showAIGenerateForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[80vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold">AI生成工作流</h3>
                <button
                  onClick={cancelAIGenerate}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
              
              <form onSubmit={(e) => { e.preventDefault(); generateWorkflowWithAI(); }} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-900 mb-1">
                    工作流描述 <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={aiWorkflowDescription}
                    onChange={(e) => setAiWorkflowDescription(e.target.value)}
                    rows={6}
                    className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                    placeholder="请详细描述您需要的工作流功能，例如：查询某个部门的年度报销总额，并按费用类型和月份进行统计..."
                    required
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    请尽可能详细地描述您需要的工作流功能，AI将根据您的描述和系统现有资源生成工作流。
                  </p>
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <button
                    type="button"
                    onClick={cancelAIGenerate}
                    className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-xl transition-all duration-200"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isGenerating || !aiWorkflowDescription.trim()}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isGenerating ? (
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    ) : (
                      <Bot size={16} />
                    )}
                    <span>生成工作流</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* 添加/编辑工作流表单 */}
      {(showAddForm || editingWorkflow) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold">
                  {editingWorkflow ? '编辑工作流' : '创建工作流'}
                </h3>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    setEditingWorkflow(null);
                    resetFormData();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      名称 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleFormChange}
                      className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                      placeholder="请输入工作流名称"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-900 mb-1">
                      描述 <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="description"
                      value={formData.description}
                      onChange={handleFormChange}
                      className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                      placeholder="请输入工作流描述"
                      required
                    />
                  </div>
                </div>

                {/* 工作流输入参数 */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <label className="block text-sm font-medium text-gray-900">
                      输入参数
                    </label>
                    <button
                      type="button"
                      onClick={addWorkflowInput}
                      className="flex items-center space-x-2 px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg transition-colors"
                    >
                      <Plus size={14} />
                      <span>添加输入</span>
                    </button>
                  </div>
                  
                  {formData.inputs.length === 0 ? (
                    <div className="text-center py-4 border-2 border-dashed border-gray-300 rounded-lg">
                      <p className="text-gray-500 text-sm">暂无输入参数</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {formData.inputs.map((input, index) => (
                        <div key={index} className="grid grid-cols-12 gap-2 items-start p-3 border border-gray-200 rounded-lg">
                          <div className="col-span-3">
                            <input
                              type="text"
                              value={input.name}
                              onChange={(e) => updateWorkflowInput(index, 'name', e.target.value)}
                              className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                              placeholder="参数名"
                            />
                          </div>
                          <div className="col-span-2">
                            <select
                              value={input.type}
                              onChange={(e) => updateWorkflowInput(index, 'type', e.target.value)}
                              className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                            >
                              <option value="string">字符串</option>
                              <option value="number">数字</option>
                              <option value="boolean">布尔值</option>
                              <option value="object">对象</option>
                              <option value="array">数组</option>
                            </select>
                          </div>
                          <div className="col-span-5">
                            <input
                              type="text"
                              value={input.description}
                              onChange={(e) => updateWorkflowInput(index, 'description', e.target.value)}
                              className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                              placeholder="参数描述"
                            />
                          </div>
                          <div className="col-span-1 flex items-center">
                            <label className="flex items-center text-sm text-gray-700">
                              <input
                                type="checkbox"
                                checked={input.required || false}
                                onChange={(e) => updateWorkflowInput(index, 'required', e.target.checked)}
                                className="mr-1"
                              />
                              必填
                            </label>
                          </div>
                          <div className="col-span-1 flex justify-end">
                            <button
                              type="button"
                              onClick={() => removeWorkflowInput(index)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* 工作流输出参数 */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <label className="block text-sm font-medium text-gray-900">
                      输出参数
                    </label>
                    <button
                      type="button"
                      onClick={addWorkflowOutput}
                      className="flex items-center space-x-2 px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg transition-colors"
                    >
                      <Plus size={14} />
                      <span>添加输出</span>
                    </button>
                  </div>
                  
                  {formData.outputs.length === 0 ? (
                    <div className="text-center py-4 border-2 border-dashed border-gray-300 rounded-lg">
                      <p className="text-gray-500 text-sm">暂无输出参数</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {formData.outputs.map((output, index) => (
                        <div key={index} className="grid grid-cols-12 gap-2 items-start p-3 border border-gray-200 rounded-lg">
                          <div className="col-span-3">
                            <input
                              type="text"
                              value={output.name}
                              onChange={(e) => updateWorkflowOutput(index, 'name', e.target.value)}
                              className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                              placeholder="参数名"
                            />
                          </div>
                          <div className="col-span-2">
                            <select
                              value={output.type}
                              onChange={(e) => updateWorkflowOutput(index, 'type', e.target.value)}
                              className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                            >
                              <option value="string">字符串</option>
                              <option value="number">数字</option>
                              <option value="boolean">布尔值</option>
                              <option value="object">对象</option>
                              <option value="array">数组</option>
                            </select>
                          </div>
                          <div className="col-span-6">
                            <input
                              type="text"
                              value={output.description}
                              onChange={(e) => updateWorkflowOutput(index, 'description', e.target.value)}
                              className="w-full px-3 py-1.5 bg-white border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 text-sm"
                              placeholder="参数描述"
                            />
                          </div>
                          <div className="col-span-1 flex justify-end">
                            <button
                              type="button"
                              onClick={() => removeWorkflowOutput(index)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* 工作流步骤 */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <label className="block text-sm font-medium text-gray-900">
                      工作流步骤
                    </label>
                    <button
                      type="button"
                      onClick={() => setShowToolSelector(true)}
                      className="flex items-center space-x-2 px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-lg transition-colors"
                    >
                      <Plus size={14} />
                      <span>添加步骤</span>
                    </button>
                  </div>
                  
                  {formData.steps.length === 0 ? (
                    <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                      <p className="text-gray-500">暂无步骤，点击"添加步骤"开始构建工作流</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {formData.steps.map((step, index) => {
                        const tool = builtinTools.find(t => t.id === step.tool_id);
                        return (
                          <div key={index} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center space-x-3">
                                <span className="flex items-center justify-center w-6 h-6 bg-purple-500 text-white text-sm rounded-full">
                                  {index + 1}
                                </span>
                                <span className="font-medium">{tool?.name || step.tool_id}</span>
                                <span className="text-sm text-gray-500">({tool?.category || 'unknown'})</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <button
                                  type="button"
                                  onClick={() => moveStep(index, 'up')}
                                  disabled={index === 0}
                                  className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                                >
                                  <ArrowUp size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => moveStep(index, 'down')}
                                  disabled={index === formData.steps.length - 1}
                                  className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                                >
                                  <ArrowDown size={16} />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => removeStep(index)}
                                  className="p-1 text-red-400 hover:text-red-600"
                                >
                                  <Trash2 size={16} />
                                </button>
                              </div>
                            </div>
                            
                            {tool?.description && (
                              <p className="text-sm text-gray-600 mb-3">{tool.description}</p>
                            )}
                            
                            {/* 步骤输入参数 */}
                            {tool?.inputs && tool.inputs.length > 0 && (
                              <div className="space-y-2">
                                <h5 className="text-sm font-medium text-gray-700">输入参数:</h5>
                                {tool.inputs.map((input) => (
                                  <div key={input.name} className="grid grid-cols-3 gap-2 items-center">
                                    <label className="text-sm text-gray-600">
                                      {input.name}
                                      {input.required && <span className="text-red-500 ml-1">*</span>}
                                    </label>
                                    <div className="relative">
                                      <input
                                        type="text"
                                        value={step.inputs[input.name] || ''}
                                        onChange={(e) => updateStepInput(index, input.name, e.target.value)}
                                        placeholder={`${input.description || input.name} (支持 \${变量} 或 @step_id.输出)`}
                                        className="w-full px-3 py-1.5 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                      />
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      <div>{input.description}</div>
                                      <div className="mt-1 text-blue-600">
                                        工作流变量: ${'{变量名}'}
                                        <br />
                                        步骤输出: @step_id.输出名
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddForm(false);
                      setEditingWorkflow(null);
                      resetFormData();
                    }}
                    className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-xl transition-all duration-200"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? (
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    ) : (
                      <Save size={16} />
                    )}
                    <span>{editingWorkflow ? '更新' : '创建'}</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* 工具选择器 */}
      {showToolSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-3xl w-full max-h-[80vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold">选择内置工具</h3>
                <button
                  onClick={() => setShowToolSelector(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
              
              <div className="space-y-6">
                {Object.entries(toolsByCategory).map(([category, tools]) => (
                  <div key={category}>
                    <h4 className="text-lg font-medium text-gray-900 mb-3 capitalize">
                      {category === 'employee' ? '员工管理' : 
                       category === 'finance' ? '财务管理' : 
                       category === 'calculation' ? '计算工具' : 
                       category === 'data' ? '数据处理' : category}
                    </h4>
                    <div className="grid gap-3">
                      {tools.map((tool) => (
                        <div
                          key={tool.id}
                          onClick={() => addStep(tool)}
                          className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 cursor-pointer transition-all duration-200"
                        >
                          <div className="flex items-start justify-between">
                            <div>
                              <h5 className="font-medium text-gray-900 mb-1">{tool.name}</h5>
                              <p className="text-sm text-gray-600 mb-2">{tool.description}</p>
                              <div className="flex items-center space-x-4 text-xs text-gray-500">
                                <span>输入: {tool.inputs?.length || 0} 个参数</span>
                                <span>输出: {tool.outputs?.length || 0} 个参数</span>
                              </div>
                            </div>
                            <Plus size={20} className="text-purple-500" />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 执行工作流表单 */}
      {showExecuteForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[80vh] overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold">执行工作流</h3>
                <button
                  onClick={cancelExecute}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
              
              <form onSubmit={handleExecuteSubmit} className="space-y-4">
                <div className="space-y-4">
                  {Object.keys(executeInputs).length === 0 ? (
                    <p className="text-gray-500 text-center py-4">此工作流无需输入参数</p>
                  ) : (
                    (() => {
                      const currentWorkflow = workflows.find(w => w.id === executingWorkflowId);
                      return currentWorkflow?.inputs?.map((inputDef) => (
                        <div key={inputDef.name}>
                          <label className="block text-sm font-medium text-gray-900 mb-1">
                            {inputDef.name}
                            {inputDef.required && <span className="text-red-500 ml-1">*</span>}
                          </label>
                          <input
                            type={inputDef.type === 'number' ? 'number' : 'text'}
                            value={executeInputs[inputDef.name] || ''}
                            onChange={(e) => handleExecuteInputChange(e, inputDef.name)}
                            className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                            placeholder={inputDef.description || `请输入${inputDef.name}`}
                            required={inputDef.required}
                          />
                          {inputDef.description && (
                            <p className="mt-1 text-sm text-gray-500">{inputDef.description}</p>
                          )}
                        </div>
                      )) || Object.keys(executeInputs).map((paramName) => (
                        <div key={paramName}>
                          <label className="block text-sm font-medium text-gray-900 mb-1">
                            {paramName}
                          </label>
                          <input
                            type="text"
                            value={executeInputs[paramName]}
                            onChange={(e) => handleExecuteInputChange(e, paramName)}
                            className="w-full px-4 py-2 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-gray-900 placeholder-gray-500 transition-all duration-200"
                            placeholder={`请输入${paramName}`}
                          />
                        </div>
                      ));
                    })()
                  )}
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t">
                  <button
                    type="button"
                    onClick={cancelExecute}
                    className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-xl transition-all duration-200"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={executingWorkflowId}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {executingWorkflowId ? (
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    ) : (
                      <Play size={16} />
                    )}
                    <span>执行</span>
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkflowManagement;