import React, { useState, useEffect } from 'react';
import { Building, Phone, Mail, MapPin, Search, Filter, FileText, CheckCircle, Clock, XCircle, Eye, Edit, Trash2, Check, X, Plus } from 'lucide-react';
import Watermark from './Watermark';

const Supplier = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState(null);

  // 供应商状态选项
  const statusOptions = [
    { value: 'active', label: '合作中', color: 'green' },
    { value: 'inactive', label: '已暂停', color: 'yellow' },
    { value: 'terminated', label: '已终止', color: 'red' },
    { value: 'pending', label: '待审核', color: 'blue' }
  ];

  // 供应商类型选项
  const typeOptions = [
    { value: 'material', label: '材料供应商' },
    { value: 'equipment', label: '设备供应商' },
    { value: 'service', label: '服务供应商' },
    { value: 'logistics', label: '物流供应商' }
  ];

  // 获取供应商记录
  const fetchSuppliers = async () => {
    setLoading(true);
    try {
      // 模拟数据，实际应该从API获取
      const mockData = [
        {
          id: 1,
          supplier_no: 'S2024001',
          name: 'ABC办公用品有限公司',
          type: 'material',
          contact_person: '张经理',
          phone: '13800138001',
          email: '<EMAIL>',
          address: '北京市朝阳区建国路88号',
          status: 'active',
          register_date: '2024-01-15',
          last_order_date: '2024-01-20',
          total_orders: 15,
          total_amount: 58000.00,
          description: '专业办公用品供应商，提供文具、打印机耗材等产品'
        },
        {
          id: 2,
          supplier_no: 'S2024002',
          name: 'XYZ设备制造厂',
          type: 'equipment',
          contact_person: '李总',
          phone: '13900139002',
          email: '<EMAIL>',
          address: '上海市浦东新区科技园区100号',
          status: 'active',
          register_date: '2024-01-18',
          last_order_date: '2024-01-25',
          total_orders: 8,
          total_amount: 125000.00,
          description: '专业办公设备制造商，提供电脑、打印机等设备'
        },
        {
          id: 3,
          supplier_no: 'S2024003',
          name: '速达物流服务有限公司',
          type: 'logistics',
          contact_person: '王经理',
          phone: '13700137003',
          email: '<EMAIL>',
          address: '广州市天河区物流园区88号',
          status: 'inactive',
          register_date: '2024-01-20',
          last_order_date: '2024-01-10',
          total_orders: 25,
          total_amount: 35000.00,
          description: '专业物流服务提供商，提供货物运输、仓储等服务'
        },
        {
          id: 4,
          supplier_no: 'S2024004',
          name: '蓝天清洁服务有限公司',
          type: 'service',
          contact_person: '赵经理',
          phone: '13600136004',
          email: '<EMAIL>',
          address: '深圳市南山区科技园200号',
          status: 'pending',
          register_date: '2024-01-22',
          last_order_date: null,
          total_orders: 0,
          total_amount: 0,
          description: '专业清洁服务提供商，提供办公室清洁、维护等服务'
        },
        {
          id: 5,
          supplier_no: 'S2024005',
          name: '大地建材批发市场',
          type: 'material',
          contact_person: '钱老板',
          phone: '13500135005',
          email: '<EMAIL>',
          address: '成都市武侯区建材市场300号',
          status: 'terminated',
          register_date: '2023-06-10',
          last_order_date: '2023-12-15',
          total_orders: 12,
          total_amount: 85000.00,
          description: '建筑材料批发商，提供装修材料、建材等产品'
        }
      ];
      setSuppliers(mockData);
    } catch (error) {
      console.error('获取供应商记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSuppliers();
  }, []);

  // 过滤供应商记录
  const filteredSuppliers = suppliers.filter(supplier => {
    const matchesSearch = supplier.supplier_no?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         supplier.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         supplier.contact_person?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || supplier.status === filterStatus;
    const matchesType = filterType === 'all' || supplier.type === filterType;
    return matchesSearch && matchesStatus && matchesType;
  });

  // 计算统计数据
  const stats = {
    totalSuppliers: suppliers.length,
    activeSuppliers: suppliers.filter(s => s.status === 'active').length,
    inactiveSuppliers: suppliers.filter(s => s.status === 'inactive').length,
    pendingSuppliers: suppliers.filter(s => s.status === 'pending').length
  };

  // 获取状态信息
  const getStatusInfo = (status) => {
    return statusOptions.find(opt => opt.value === status) || statusOptions[0];
  };

  // 获取类型信息
  const getTypeInfo = (type) => {
    return typeOptions.find(opt => opt.value === type) || typeOptions[0];
  };

  // 处理审核操作
  const handleApproval = async (id, action) => {
    if (!window.confirm(`确定要${action === 'approve' ? '通过' : '拒绝'}此供应商吗？`)) return;
    
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuppliers(prev => prev.map(supplier =>
        supplier.id === id
          ? {
              ...supplier,
              status: action === 'approve' ? 'active' : 'terminated'
            }
          : supplier
      ));
    } catch (error) {
      console.error('审核操作失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 水印 */}
      <Watermark text="供应商管理功能暂时不开放" />
      
      {/* 原有功能已隐藏 */}
      <div className="hidden">
        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">供应商管理</h1>
          <p className="text-gray-600">管理和维护供应商信息及合作关系</p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Building className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总供应商数</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalSuppliers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">合作中</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.activeSuppliers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">已暂停</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.inactiveSuppliers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircle className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">待审核</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.pendingSuppliers}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 工具栏 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
              {/* 搜索框 */}
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索供应商编号、名称或联系人..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
                />
              </div>

              {/* 筛选器 */}
              <div className="flex gap-2">
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">所有状态</option>
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>

                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">所有类型</option>
                  {typeOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2">
              <button
                onClick={() => setShowAddModal(true)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
              >
                <Plus size={18} />
                新增供应商
              </button>
            </div>
          </div>
        </div>

        {/* 供应商记录列表 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : filteredSuppliers.length === 0 ? (
            <div className="text-center py-12">
              <Building size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500 mb-4">暂无供应商记录</p>
              <button
                onClick={() => setShowAddModal(true)}
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
              >
                <Plus size={18} />
                添加第一个供应商
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商编号</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系人</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">注册日期</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSuppliers.map((supplier) => {
                    const statusInfo = getStatusInfo(supplier.status);
                    const typeInfo = getTypeInfo(supplier.type);
                    
                    return (
                      <tr key={supplier.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {supplier.supplier_no}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900">
                          <div className="max-w-xs">
                            <div className="font-medium">{supplier.name}</div>
                            <div className="text-xs text-gray-500 mt-1">{supplier.description}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800`}>
                            {typeInfo.label}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div>
                            <div className="font-medium">{supplier.contact_person}</div>
                            <div className="text-xs text-gray-400">{supplier.email}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {supplier.phone}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {supplier.register_date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            statusInfo.color === 'green' ? 'bg-green-100 text-green-800' :
                            statusInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                            statusInfo.color === 'red' ? 'bg-red-100 text-red-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {statusInfo.label}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            {supplier.status === 'pending' && (
                              <>
                                <button
                                  onClick={() => handleApproval(supplier.id, 'approve')}
                                  className="text-green-600 hover:text-green-900"
                                  title="通过"
                                >
                                  <Check size={16} />
                                </button>
                                <button
                                  onClick={() => handleApproval(supplier.id, 'reject')}
                                  className="text-red-600 hover:text-red-900"
                                  title="拒绝"
                                >
                                  <X size={16} />
                                </button>
                              </>
                            )}
                            <button
                              onClick={() => setEditingSupplier(supplier)}
                              className="text-blue-600 hover:text-blue-900"
                              title="查看详情"
                            >
                              <Eye size={16} />
                            </button>
                            <button
                              onClick={() => setEditingSupplier(supplier)}
                              className="text-gray-600 hover:text-gray-900"
                              title="编辑"
                            >
                              <Edit size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* 供应商详情模态框 */}
        {editingSupplier && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">供应商详情 - {editingSupplier.supplier_no}</h3>
                  <button
                    onClick={() => setEditingSupplier(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X size={24} />
                  </button>
                </div>
                
                {/* 供应商基本信息 */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">供应商编号</p>
                      <p className="font-medium">{editingSupplier.supplier_no}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">供应商名称</p>
                      <p className="font-medium">{editingSupplier.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">供应商类型</p>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800`}>
                        {getTypeInfo(editingSupplier.type).label}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">状态</p>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        getStatusInfo(editingSupplier.status).color === 'green' ? 'bg-green-100 text-green-800' :
                        getStatusInfo(editingSupplier.status).color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                        getStatusInfo(editingSupplier.status).color === 'red' ? 'bg-red-100 text-red-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {getStatusInfo(editingSupplier.status).label}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">联系人</p>
                      <p className="font-medium">{editingSupplier.contact_person}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">联系电话</p>
                      <p className="font-medium">{editingSupplier.phone}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">电子邮箱</p>
                      <p className="font-medium">{editingSupplier.email}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">注册日期</p>
                      <p className="font-medium">{editingSupplier.register_date}</p>
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <p className="text-sm text-gray-500">地址</p>
                    <p className="font-medium">{editingSupplier.address}</p>
                  </div>
                  
                  <div className="mt-4">
                    <p className="text-sm text-gray-500">描述</p>
                    <p className="font-medium">{editingSupplier.description}</p>
                  </div>
                </div>

                {/* 供应商统计信息 */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">合作统计</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm text-gray-500">总订单数</p>
                      <p className="font-medium">{editingSupplier.total_orders}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">总交易金额</p>
                      <p className="font-medium">¥{editingSupplier.total_amount?.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) || '0.00'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">最后下单日期</p>
                      <p className="font-medium">{editingSupplier.last_order_date || '无'}</p>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    onClick={() => setEditingSupplier(null)}
                    className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Supplier;