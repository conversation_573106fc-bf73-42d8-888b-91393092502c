import React, { useEffect, useState } from 'react';
import Watermark from './Watermark';

const LEDGER_TYPES = [
  { key: 'general', label: '总账' },
  { key: 'detail', label: '明细账' },
  { key: 'journal', label: '日记账' },
];

const Bookkeeping = () => {
  const [ledgerType, setLedgerType] = useState('general');
  const [ledger, setLedger] = useState([]);
  const [accountCode, setAccountCode] = useState('');
  const [subjects, setSubjects] = useState([]);

  // 加载科目表
  useEffect(() => {
    fetch('http://localhost:8000/subjects')
      .then(res => res.json())
      .then(data => setSubjects(data.subjects || []));
  }, []);

  // 加载账簿数据
  const fetchLedger = async () => {
    let url = 'http://localhost:8000/ledger?';
    if (ledgerType === 'detail' && accountCode) {
      url += `account_code=${accountCode}&detail=true`;
    } else if (ledgerType === 'general' && accountCode) {
      url += `account_code=${accountCode}`;
    }
    const res = await fetch(url);
    const data = await res.json();
    setLedger(data.ledger || []);
  };

  useEffect(() => {
    fetchLedger();
    // eslint-disable-next-line
  }, [ledgerType, accountCode]);

  // 导出账簿
  const handleExport = () => {
    let url = 'http://localhost:8000/ledger/export?';
    if (ledgerType === 'detail' && accountCode) {
      url += `account_code=${accountCode}&detail=true`;
    } else if (ledgerType === 'general' && accountCode) {
      url += `account_code=${accountCode}`;
    }
    window.open(url, '_blank');
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 水印 */}
      <Watermark text="账簿管理功能暂时不开放" />
      
      {/* 原有功能已隐藏 */}
      <div className="hidden">
        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">账簿管理</h1>
          <p className="text-gray-600">查看和管理各类账簿信息</p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">总记录数</p>
                <p className="text-2xl font-semibold text-gray-900">{ledger.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">借方合计</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {ledger.reduce((sum, e) => sum + (e.debit || 0), 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">贷方合计</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {ledger.reduce((sum, e) => sum + (e.credit || 0), 0).toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">当前余额</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {ledger.length > 0
                    ? ledger[ledger.length - 1].balance?.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) || '0.00'
                    : '0.00'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 工具栏 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
              <div className="flex items-center space-x-4">
                {LEDGER_TYPES.map(t => (
                  <button
                    key={t.key}
                    className={`px-4 py-2 rounded-lg ${ledgerType === t.key ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                    onClick={() => setLedgerType(t.key)}
                  >
                    {t.label}
                  </button>
                ))}
                {(ledgerType === 'general' || ledgerType === 'detail') && (
                  <select
                    className="ml-4 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={accountCode}
                    onChange={e => setAccountCode(e.target.value)}
                  >
                    <option value="">全部科目</option>
                    {subjects.map(s => (
                      <option key={s.code} value={s.code}>{s.code} {s.name}</option>
                    ))}
                  </select>
                )}
              </div>
            </div>
            <button className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center" onClick={handleExport}>
              导出
            </button>
          </div>
        </div>

        {/* 账簿表格 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">凭证号</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">摘要</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科目编码</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科目名称</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">借方</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">贷方</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">余额</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">方向</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {ledger.length === 0 ? (
                  <tr>
                    <td colSpan={9} className="text-center py-12">
                      <p className="text-gray-500">暂无数据</p>
                    </td>
                  </tr>
                ) : ledger.map((e, idx) => (
                  <tr key={idx} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{e.date}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{e.voucher_no}</td>
                    <td className="px-6 py-4 text-sm text-gray-900">{e.summary}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{e.account_code}</td>
                    <td className="px-6 py-4 text-sm text-gray-900">{e.account_name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      {e.debit?.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      {e.credit?.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                      {e.balance?.toLocaleString('zh-CN', { minimumFractionDigits: 2 }) || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{e.direction || '-'}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Bookkeeping;
