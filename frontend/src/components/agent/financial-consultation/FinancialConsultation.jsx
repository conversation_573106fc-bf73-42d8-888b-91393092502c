import React, { useState, useCallback, useRef } from 'react';
import { MessageCircle, Lightbulb } from 'lucide-react';
import { MESSAGE_TYPES } from '../common/messageTypes';
import { cleanJsonFromText, cleanSpecialTags } from '../common/utils';
import api from '../../../api';
import { backendBase } from '../../../api';
import { getActiveService, AI_SERVICE_TYPES } from '../../../types/aiConfig';

const FinancialConsultation = ({
  messages,
  setMessages,
  input,
  setInput,
  isLoading,
  setIsLoading,
  isStreaming,
  setIsStreaming,
  sessionId,
  setSessionId,
  abortController,
  setAbortController,
  uploadedFiles,
  setUploadedFiles,
  aiConfig,
  addMessage,
  updateMessage,
  showToast,
  configureAgent,
  setContext,
  setAutoLooping,
  autoLoopRef
}) => {
  const streamingMessageRef = useRef(null);
  const reasoningMessageRef = useRef(null);

  // 财务咨询模式的 action 处理主函数
  const handleConsultationAgentAction = async (response, userInput) => {
    try {
      console.log('[DEBUG] handleConsultationAgentAction called', response);
      console.log('[DEBUG] action:', response.action);
      console.log('[DEBUG] consultation_conclusion:', response.consultation_conclusion);
      console.log('[DEBUG] needs_more_info:', response.needs_more_info);
      console.log('[DEBUG] required_info:', response.required_info);
      console.log('[DEBUG] analysis:', response.analysis);
      console.log('[DEBUG] recommendations:', response.recommendations);
      const { action, answer, data, is_finished, needs_more_info, required_info, consultation_conclusion, analysis, recommendations } = response;
      
      // 根据action类型处理不同的响应，避免重复显示
      switch (action) {
        case 'provide_advice':
          // 提供建议 - 添加咨询结果消息
          const adviceMessage = cleanSpecialTags(cleanJsonFromText(consultation_conclusion)) || '根据您的财务情况，我们提供以下专业建议。';
          let adviceData = { result: 'provide_advice' };
          
          // 如果有分析内容，也包含在结果数据中
          if (analysis && analysis.trim()) {
            adviceData.analysis = cleanSpecialTags(cleanJsonFromText(analysis));
          }
          
          // 如果有建议内容，也包含在结果数据中
          if (recommendations && Array.isArray(recommendations) && recommendations.length > 0) {
            adviceData.recommendations = recommendations;
          }
          
          addMessage(adviceMessage, MESSAGE_TYPES.CONSULTATION_RESULT, adviceData);
          showToast('财务咨询完成', 'success');
          
          // 如果有建议，单独显示建议消息
          if (recommendations && Array.isArray(recommendations) && recommendations.length > 0) {
            const recommendationsText = recommendations.map((rec, index) => `${index + 1}. ${rec}`).join('\n');
            addMessage(recommendationsText, MESSAGE_TYPES.CONSULTATION_RECOMMENDATIONS, { recommendations });
          }
          break;
          
        case 'request_more_info':
          // 需要更多信息 - 只显示信息请求消息，不重复显示结论
          if (needs_more_info && required_info && Array.isArray(required_info) && required_info.length > 0) {
            addMessage('', MESSAGE_TYPES.CONSULTATION_INFO_REQUEST, {
              required_info: required_info.map(info => cleanSpecialTags(cleanJsonFromText(info))),
              content: cleanSpecialTags(cleanJsonFromText(consultation_conclusion)) || '为了完成财务咨询，需要您提供以下补充信息：'
            });
            showToast('需要补充信息以完成咨询', 'info');
          } else {
            addMessage(cleanSpecialTags(cleanJsonFromText(consultation_conclusion)) || '需要更多信息以完成咨询', MESSAGE_TYPES.CONSULTATION_INFO_REQUEST, {
              content: cleanSpecialTags(cleanJsonFromText(consultation_conclusion)) || '需要更多信息以完成咨询'
            });
            showToast('需要补充信息以完成咨询', 'info');
          }
          break;
          
        case 'none':
        default:
          // 默认情况，可能是一般性分析
          if (consultation_conclusion && consultation_conclusion !== '分析完成') {
            addMessage(cleanSpecialTags(cleanJsonFromText(consultation_conclusion)), MESSAGE_TYPES.CONSULTATION_RESULT, { result: 'none' });
          }
          showToast('咨询分析完成');
          break;
      }
      
      if (!is_finished) {
        // 如果未完成且需要更多信息，可以在这里添加自动提示或其他处理
        if (needs_more_info) {
          console.log('Consultation requires more information from user');
        }
      }
    } catch (error) {
      console.error('handleConsultationAgentAction error:', error);
      addMessage(`处理响应时发生错误: ${error.message}`, MESSAGE_TYPES.ERROR);
      showToast('处理响应失败', 'error');
    }
  };

  // 封装财务咨询模式的 sendMessage
  const sendConsultationRequestWithContext = async (content, extraContext = {}) => {
    if (!content.trim() || isLoading) return;
    // 配置智能体
    const configured = await configureAgent();
    if (!configured) return;
    // 添加用户消息
    addMessage(content, MESSAGE_TYPES.USER);
    setInput('');
    setIsLoading(true);
    setIsStreaming(true);
    // 插入思考动画
    const thinkingMsg = addMessage('', MESSAGE_TYPES.THINKING);
    let thinkingMsgId = thinkingMsg.id;
    let analysisMsgId = null;
    let reasoningMsgId = null;
    let creatingCardsMsgId = null;
    let gotCards = false;
    let gotAnalysis = false;
    let gotReasoning = false;
    let lastAnalysis = '';
    let lastReasoning = '';
    let fullBuffer = '';
    let jsonCandidate = '';
    let firstContentReceived = false; // 标记是否已收到第一个内容
    const controller = new AbortController();
    setAbortController(controller);
    try {
      // 获取当前活动的AI服务
      const activeService = getActiveService(aiConfig);

      if (!activeService) {
        throw new Error('没有可用的AI服务配置');
      }

      // 根据服务类型构建请求头
      let headers = {
        'Content-Type': 'application/json'
      };

      if (activeService.type === AI_SERVICE_TYPES.OLLAMA) {
        headers['X-API-Key'] = '';
        headers['X-Base-URL'] = activeService.config.baseUrl;
        headers['X-Model'] = activeService.config.model;
        headers['X-Use-Ollama'] = 'true';
      } else {
        headers['X-API-Key'] = activeService.config.apiKey || '';
        headers['X-Base-URL'] = activeService.config.baseUrl;
        headers['X-Model'] = activeService.config.model;
        headers['X-Use-Ollama'] = 'false';
      }

      const response = await fetch(`${backendBase}/agent/v2/consultation/stream`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          content,
          session_id: sessionId,
          user_id: 'user',
          use_enhanced: true,
          files: extraContext.files || null,
          ...extraContext
        }),
        signal: controller.signal
      });
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let streamingBuffer = ''; // 用于存储流式文本内容
      let hasStructuredData = false; // 是否检测到结构化数据
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        fullBuffer += chunk;
        
        // 尝试从chunk中提取reasoning_content
        let reasoningContent = '';
        let regularContent = '';
        
        // 如果没有解析到结构化内容，将整个chunk作为普通内容
        if (!reasoningContent && !regularContent) {
          regularContent = chunk;
        }
        
        // 立即收集流式内容
        streamingBuffer += chunk;
        
        // 检查是否包含结构化标记
        if (chunk.includes('<|') && !hasStructuredData) {
          console.log('[DEBUG] 检测到结构化标记');
          hasStructuredData = true;
          
          // 添加智能体思考中的对话框
          if (!thinkingMsgId || firstContentReceived) {
            const thinkingMsg = addMessage('', MESSAGE_TYPES.THINKING);
            thinkingMsgId = thinkingMsg.id;
          }
        }
        
        // 立即显示流式内容，不等待完整响应
        if (streamingBuffer.trim()) {
          // 如果这是第一次收到内容，立即移除初始思考动画，但保留检测到结构化标记后添加的思考对话框
          if (!firstContentReceived) {
            firstContentReceived = true;
            // 只有当没有检测到结构化标记时，才移除思考动画
            if (!hasStructuredData) {
              setMessages(prev => prev.filter(msg => msg.id !== thinkingMsgId));
            }
          }
          
          // 如果检测到结构化标记，智能处理显示内容
          let displayContent = streamingBuffer;
          if (hasStructuredData) {
            // 检测到结构化标记后，不再更新分析内容，保持最后一次的有效内容
            // 这样可以确保在显示智能体思考中对话框时，之前的分析对话框不会继续输出
            if (lastAnalysis && lastAnalysis.trim()) {
              // 保持最后一次的有效分析内容，不再更新
              displayContent = lastAnalysis;
            } else {
              // 如果没有之前的分析内容，使用默认提示
              displayContent = '正在分析财务问题...';
            }
          } else {
            // 没有检测到结构化标记，正常处理显示内容
            // 移除所有特殊标记，只保留实际内容
            displayContent = streamingBuffer.replace(/<\|[^|]+\|>/g, '').replace(/<\|\/[^|]+\|>/g, '');
            
            // 如果清理后的内容为空，使用上一次的有效内容
            if (!displayContent.trim()) {
              displayContent = lastAnalysis || '正在分析财务问题...';
            }
          }
          
          // 只有当显示内容有变化时才更新UI，并且只有在没有检测到结构化标记时才更新
          if (displayContent !== lastAnalysis && displayContent.trim() && !hasStructuredData) {
            lastAnalysis = displayContent;
            gotAnalysis = true;
            
            if (!analysisMsgId) {
              // 创建分析消息
              const analysisMsg = addMessage(displayContent, MESSAGE_TYPES.CONSULTATION_ANALYSIS);
              analysisMsgId = analysisMsg.id;
            } else {
              // 实时更新分析内容
              updateMessage(analysisMsgId, { content: displayContent });
            }
          }
          
          // 处理推理内容
          if (reasoningContent && reasoningContent.trim()) {
            lastReasoning += reasoningContent;
            gotReasoning = true;
            
            if (!reasoningMsgId) {
              // 创建推理消息
              const reasoningMsg = addMessage(lastReasoning, MESSAGE_TYPES.REASONING);
              reasoningMsgId = reasoningMsg.id;
            } else {
              // 实时更新推理内容
              updateMessage(reasoningMsgId, { content: lastReasoning });
            }
          }
        }
      }
      
      // 流式结束后，尝试解析结构化数据
      console.log('[DEBUG] 流式结束，fullBuffer长度:', fullBuffer.length);
      console.log('[DEBUG] streamingBuffer长度:', streamingBuffer.length);
      
      // 从fullBuffer中提取结构化数据
      const structuredData = {};
      const tagPatterns = {
        'consultation_conclusion': /<\|consultation_conclusion\|>(.*?)<\|\/consultation_conclusion\|>/s,
        'action': /<\|action\|>(.*?)<\|\/action\|>/s,
        'needs_more_info': /<\|needs_more_info\|>(.*?)<\|\/needs_more_info\|>/s,
        'required_info': /<\|required_info\|>(.*?)<\|\/required_info\|>/s,
        'is_finished': /<\|is_finished\|>(.*?)<\|\/is_finished\|>/s,
        'recommendations': /<\|recommendations\|>(.*?)<\|\/recommendations\|>/s
      };
      
      for (const [key, pattern] of Object.entries(tagPatterns)) {
        const match = fullBuffer.match(pattern);
        if (match) {
          structuredData[key] = match[1].trim();
          console.log('[DEBUG] 提取到', key, ':', structuredData[key]);
        }
      }
      
      // 如果有结构化数据，构建响应对象
      if (Object.keys(structuredData).length > 0) {
        // 从fullBuffer中提取咨询分析部分（标记之前的内容）
        let analysisContent = fullBuffer;
        
        // 找到第一个标记的位置
        const firstTagMatch = fullBuffer.match(/<\|[^|]+\|>/);
        if (firstTagMatch) {
          analysisContent = fullBuffer.substring(0, firstTagMatch.index).trim();
        }
        
        // 清理分析内容
        analysisContent = cleanSpecialTags(cleanJsonFromText(analysisContent));
        
        console.log('[DEBUG] 提取的分析内容:', analysisContent);
        
        // 确保分析内容不为空，如果为空则使用consultation_conclusion作为分析内容
        if (!analysisContent.trim() && structuredData.consultation_conclusion) {
          analysisContent = cleanSpecialTags(cleanJsonFromText(structuredData.consultation_conclusion));
          console.log('[DEBUG] 使用consultation_conclusion作为分析内容:', analysisContent);
        }
        
        // 处理recommendations字段
        let recommendations = [];
        if (structuredData.recommendations) {
          try {
            recommendations = JSON.parse(structuredData.recommendations);
            if (!Array.isArray(recommendations)) {
              recommendations = [];
            }
          } catch (e) {
            console.warn('[DEBUG] 解析recommendations失败:', e);
            recommendations = [];
          }
        }
        
        const structuredResponse = {
          success: true,
          action: structuredData.action || 'none',
          consultation_conclusion: structuredData.consultation_conclusion || '',
          needs_more_info: structuredData.needs_more_info === 'true',
          required_info: structuredData.required_info ?
            structuredData.required_info.split(',').map(item => item.trim()).filter(item => item) : [],
          is_finished: structuredData.is_finished !== 'false',
          analysis: analysisContent || '',
          recommendations: recommendations
        };
        
        console.log('[DEBUG] 构建的结构化响应:', structuredResponse);
        // 处理结构化响应
        try {
          // 确保移除任何剩余的思考动画
          setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
          
          // 确保显示咨询分析内容
          if (structuredResponse.analysis && structuredResponse.analysis.trim()) {
            if (analysisMsgId) {
              // 更新现有的分析消息
              console.log('[DEBUG] 更新咨询分析内容:', structuredResponse.analysis);
              updateMessage(analysisMsgId, {
                content: structuredResponse.analysis,
                type: MESSAGE_TYPES.CONSULTATION_ANALYSIS
              });
            } else {
              // 创建新的分析消息（如果之前没有创建）
              console.log('[DEBUG] 创建新的咨询分析消息:', structuredResponse.analysis);
              addMessage(structuredResponse.analysis, MESSAGE_TYPES.CONSULTATION_ANALYSIS);
            }
          } else if (structuredResponse.consultation_conclusion && structuredResponse.consultation_conclusion.trim()) {
            // 如果没有分析内容但有咨询结论，使用咨询结论作为分析内容
            const conclusionContent = cleanSpecialTags(cleanJsonFromText(structuredResponse.consultation_conclusion));
            if (analysisMsgId) {
              console.log('[DEBUG] 使用咨询结论更新分析内容:', conclusionContent);
              updateMessage(analysisMsgId, {
                content: conclusionContent,
                type: MESSAGE_TYPES.CONSULTATION_ANALYSIS
              });
            } else {
              console.log('[DEBUG] 使用咨询结论创建分析消息:', conclusionContent);
              addMessage(conclusionContent, MESSAGE_TYPES.CONSULTATION_ANALYSIS);
            }
          } else {
            console.log('[DEBUG] 没有分析内容或咨询结论可显示');
          }
          
          // 处理结构化响应中的额外信息
          await handleConsultationAgentAction(structuredResponse, content);
        } catch (error) {
          console.error('handleConsultationAgentAction failed:', error);
          addMessage(`处理AI响应时发生错误: ${error.message}`, MESSAGE_TYPES.ERROR);
        }
      } else if (!streamingBuffer.trim()) {
        // 如果没有收到任何内容
        setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
        addMessage('❌ 没有收到响应内容', MESSAGE_TYPES.ERROR);
      } else {
        // 只有流式内容，没有结构化响应
        setMessages(prev => prev.filter(msg => msg.type !== MESSAGE_TYPES.THINKING));
        const cleanedContent = cleanSpecialTags(cleanJsonFromText(streamingBuffer));
        if (analysisMsgId) {
          // 更新现有消息的最终内容
          updateMessage(analysisMsgId, {
            content: cleanedContent,
            type: MESSAGE_TYPES.CONSULTATION_ANALYSIS
          });
        } else {
          // 如果没有分析消息ID，创建新消息
          addMessage(cleanedContent, MESSAGE_TYPES.CONSULTATION_ANALYSIS);
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        addMessage(`发送失败: ${error.message}`, MESSAGE_TYPES.ERROR);
        showToast('发送消息失败', 'error');
      } else {
        // 如果是用户主动停止，清除思考消息
        setMessages(prev => prev.filter(msg =>
          msg.type !== MESSAGE_TYPES.THINKING &&
          msg.type !== MESSAGE_TYPES.ANALYSIS &&
          msg.type !== MESSAGE_TYPES.REASONING &&
          msg.type !== MESSAGE_TYPES.CREATING_CARDS
        ));
      }
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
      setAbortController(null);
      streamingMessageRef.current = null;
      setAutoLooping(false);
      autoLoopRef.current = false;
    }
  };

  // 处理文件选择
  const handleFileSelect = (files) => {
    const fileArray = Array.from(files);
    console.log('handleFileSelect files:', fileArray);
    const newFiles = fileArray
      .filter(file => file && file.name)
      .map(file => ({
        id: Date.now() + Math.random(),
        file: file,
        name: file.name,
        size: file.size,
        type: file.type
          ? (file.type.startsWith('image/') ? 'image' : (file.type === 'application/pdf' ? 'pdf' : 'file'))
          : 'file'
      }));
    console.log('newFiles:', newFiles);
    setUploadedFiles(prev => {
      const result = [...prev, ...newFiles];
      console.log('setUploadedFiles result:', result);
      return result;
    });
  };

  // 删除文件
  const removeFile = (fileId) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // 财务咨询模式的 sendMessage
  const sendMessage = async (content) => {
    try {
      // 财务咨询功能
      // 1. 立即设置加载和流式状态
      setIsLoading(true);
      setIsStreaming(true);

      // 2. 保存当前文件列表并立即清空显示
      const currentFiles = [...uploadedFiles];
      setUploadedFiles([]);

      // 3. 立即插入文件消息，并记录消息ID用于后续更新
      const fileMsgIds = [];
      currentFiles.forEach(fileInfo => {
        const localUrl = URL.createObjectURL(fileInfo.file);
        const msg = addMessage('', MESSAGE_TYPES.FILE, {
          fileName: fileInfo.name,
          fileType: fileInfo.type,
          processing: true,
          fileUrl: localUrl,
        });
        fileMsgIds.push(msg.id);
      });

      // 4. 上传文件
      let fileInfos = [];
      for (let i = 0; i < currentFiles.length; i++) {
        const fileInfo = currentFiles[i];
        const formData = new FormData();
        formData.append('file', fileInfo.file);
        try {
          // 获取当前活动的AI服务
          const activeService = getActiveService(aiConfig);

          let uploadHeaders = {};
          if (activeService) {
            if (activeService.type === AI_SERVICE_TYPES.OLLAMA) {
              uploadHeaders['X-API-Key'] = '';
              uploadHeaders['X-Base-URL'] = activeService.config.baseUrl;
              uploadHeaders['X-Model'] = activeService.config.model;
            } else {
              uploadHeaders['X-API-Key'] = activeService.config.apiKey || '';
              uploadHeaders['X-Base-URL'] = activeService.config.baseUrl;
              uploadHeaders['X-Model'] = activeService.config.model;
            }
          }

          const response = await fetch(`${backendBase}/agent/v2/upload`, {
            method: 'POST',
            headers: uploadHeaders,
            body: formData
          });
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }
          const result = await response.json();
          if (result.success && result.file_info) {
            fileInfos.push({
              file_path: result.file_info.path, // 用本地路径给后端
              file_name: fileInfo.name,
              file_type: fileInfo.type
            });
            // 上传成功，更新消息 processing: false 和 fileUrl
            updateMessage(fileMsgIds[i], { processing: false, fileUrl: `${backendBase}${result.file_info.url}` });
          } else {
            updateMessage(fileMsgIds[i], { processing: false, error: true });
          }
        } catch (error) {
          console.error('文件上传失败:', error);
          showToast(`文件 ${fileInfo.name} 上传失败`, 'error');
          updateMessage(fileMsgIds[i], { processing: false, error: true });
        }
      }

      // 5. 发送财务咨询请求
      setContext({});
      setAutoLooping(true);
      autoLoopRef.current = true;
      await sendConsultationRequestWithContext(content, { files: fileInfos });
      setIsLoading(false);
      setIsStreaming(false);
    } catch (error) {
      console.error('DEBUG: Error in sendMessage:', error);
      setIsLoading(false);
      setIsStreaming(false);
    }
  };

  // 停止生成
  const stopGeneration = () => {
    if (abortController) {
      abortController.abort();
      // 清除思考和分析消息
      setMessages(prev => prev.filter(msg =>
        msg.type !== MESSAGE_TYPES.THINKING &&
        msg.type !== MESSAGE_TYPES.ANALYSIS &&
        msg.type !== MESSAGE_TYPES.REASONING &&
        msg.type !== MESSAGE_TYPES.CREATING_CARDS
      ));
      // 添加用户反馈消息
      addMessage('生成已停止', MESSAGE_TYPES.SYSTEM);
      showToast('生成已停止');
      // 重置状态
      setIsLoading(false);
      setIsStreaming(false);
      setAbortController(null);
    }
  };

  return {
    sendMessage,
    stopGeneration,
    handleFileSelect,
    removeFile,
    renderFunctionIndicator: () => (
      <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-center gap-2 text-green-700">
          <Lightbulb size={16} />
          <span className="text-sm font-medium">财务咨询模式</span>
          <span className="text-xs bg-green-100 px-2 py-1 rounded-full">已启用</span>
        </div>
        <p className="text-xs text-green-600 mt-1">提供专业的财务咨询和建议服务</p>
      </div>
    ),
    placeholder: "请描述您的财务问题..."
  };
};

export default FinancialConsultation;