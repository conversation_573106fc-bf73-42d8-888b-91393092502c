import React, { useState, useEffect, useRef } from 'react';
import { Plus, Search, Filter, Download, Upload, Edit, Trash2, Calculator, FileText, Calendar, TrendingUp, TrendingDown } from 'lucide-react';
import Watermark from './Watermark';

const Settlement = () => {
  const [settlements, setSettlements] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPeriod, setFilterPeriod] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingSettlement, setEditingSettlement] = useState(null);
  const fileInputRef = useRef();

  const [formData, setFormData] = useState({
    settlement_no: '',
    settlement_type: 'receivable', // receivable, payable
    counterparty_name: '',
    counterparty_type: 'customer', // customer, supplier
    amount: '',
    settlement_date: new Date().toISOString().split('T')[0],
    due_date: '',
    status: 'pending', // pending, settled, cancelled
    description: '',
    reference_no: '',
    attachments: []
  });

  // 结算类型选项
  const typeOptions = [
    { value: 'receivable', label: '应收结算', icon: TrendingUp, color: 'green' },
    { value: 'payable', label: '应付结算', icon: TrendingDown, color: 'red' }
  ];

  // 状态选项
  const statusOptions = [
    { value: 'pending', label: '待结算', color: 'yellow' },
    { value: 'settled', label: '已结算', color: 'green' },
    { value: 'cancelled', label: '已取消', color: 'gray' }
  ];

  // 获取结算记录
  const fetchSettlements = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/settlements');
      if (response.ok) {
        const data = await response.json();
        setSettlements(data.settlements || []);
      }
    } catch (error) {
      console.error('获取结算记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSettlements();
  }, []);

  // 过滤结算记录
  const filteredSettlements = settlements.filter(settlement => {
    const matchesSearch = settlement.settlement_no?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         settlement.counterparty_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         settlement.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || settlement.settlement_type === filterType;
    const matchesStatus = filterStatus === 'all' || settlement.status === filterStatus;
    const matchesPeriod = !filterPeriod || settlement.settlement_date?.includes(filterPeriod);
    return matchesSearch && matchesType && matchesStatus && matchesPeriod;
  });

  // 计算统计数据
  const stats = {
    totalReceivable: settlements.filter(s => s.settlement_type === 'receivable' && s.status !== 'cancelled')
                               .reduce((sum, s) => sum + (parseFloat(s.amount || 0)), 0),
    totalPayable: settlements.filter(s => s.settlement_type === 'payable' && s.status !== 'cancelled')
                             .reduce((sum, s) => sum + (parseFloat(s.amount || 0)), 0),
    pendingSettlements: settlements.filter(s => s.status === 'pending').length,
    settledSettlements: settlements.filter(s => s.status === 'settled').length
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      settlement_no: '',
      settlement_type: 'receivable',
      counterparty_name: '',
      counterparty_type: 'customer',
      amount: '',
      settlement_date: new Date().toISOString().split('T')[0],
      due_date: '',
      status: 'pending',
      description: '',
      reference_no: '',
      attachments: []
    });
  };

  // 打开添加模态框
  const handleAdd = () => {
    resetForm();
    setEditingSettlement(null);
    setShowAddModal(true);
  };

  // 打开编辑模态框
  const handleEdit = (settlement) => {
    setFormData(settlement);
    setEditingSettlement(settlement);
    setShowAddModal(true);
  };

  // 保存结算记录
  const handleSave = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const dataToSave = {
        ...formData,
        amount: parseFloat(formData.amount) || 0
      };

      const url = editingSettlement
        ? `http://localhost:8000/settlements/${editingSettlement.id}`
        : 'http://localhost:8000/settlements';
      const method = editingSettlement ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(dataToSave)
      });

      if (response.ok) {
        await fetchSettlements();
        setShowAddModal(false);
        resetForm();
      }
    } catch (error) {
      console.error('保存结算记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 删除结算记录
  const handleDelete = async (id) => {
    if (!window.confirm('确定要删除这条结算记录吗？')) return;
    
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:8000/settlements/${id}`, {
        method: 'DELETE'
      });
      if (response.ok) {
        await fetchSettlements();
      }
    } catch (error) {
      console.error('删除结算记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 导出结算记录
  const handleExport = () => {
    const params = new URLSearchParams();
    if (filterPeriod) params.append('period', filterPeriod);
    if (filterType !== 'all') params.append('type', filterType);
    window.open(`http://localhost:8000/settlements/export?${params.toString()}`, '_blank');
  };

  // 导入结算记录
  const handleImport = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    const formData = new FormData();
    formData.append('file', file);
    
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8000/settlements/import', {
        method: 'POST',
        body: formData
      });
      if (response.ok) {
        await fetchSettlements();
      }
    } catch (error) {
      console.error('导入结算记录失败:', error);
    } finally {
      setLoading(false);
      fileInputRef.current.value = '';
    }
  };

  // 获取类型信息
  const getTypeInfo = (type) => {
    return typeOptions.find(opt => opt.value === type) || typeOptions[0];
  };

  // 获取状态信息
  const getStatusInfo = (status) => {
    return statusOptions.find(opt => opt.value === status) || statusOptions[0];
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 水印 */}
      <Watermark text="结算管理功能暂时不开放" />
      
      {/* 原有功能已隐藏 */}
      <div className="hidden">
        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">结算管理</h1>
          <p className="text-gray-600">管理应收应付结算业务</p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">应收总额</p>
                <p className="text-2xl font-semibold text-gray-900">
                  ¥{stats.totalReceivable.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingDown className="h-8 w-8 text-red-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">应付总额</p>
                <p className="text-2xl font-semibold text-gray-900">
                  ¥{stats.totalPayable.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-yellow-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">待结算</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.pendingSettlements}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FileText className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">已结算</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.settledSettlements}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 工具栏 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
              {/* 搜索框 */}
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索结算号、对方名称或描述..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64"
                />
              </div>

              {/* 筛选器 */}
              <div className="flex gap-2">
                <input
                  type="month"
                  value={filterPeriod}
                  onChange={(e) => setFilterPeriod(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="结算期间"
                />

                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">所有类型</option>
                  {typeOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>

                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">所有状态</option>
                  {statusOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleImport}
                accept=".csv,.xlsx"
                className="hidden"
              />
              <button
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <Upload size={18} />
                导入
              </button>
              <button
                onClick={handleExport}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <Download size={18} />
                导出
              </button>
              <button
                onClick={handleAdd}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
              >
                <Plus size={18} />
                新增结算
              </button>
            </div>
          </div>
        </div>

        {/* 结算记录列表 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : filteredSettlements.length === 0 ? (
            <div className="text-center py-12">
              <Calculator size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500 mb-4">暂无结算记录</p>
              <button
                onClick={handleAdd}
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
              >
                <Plus size={18} />
                添加第一条结算记录
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结算号</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">对方名称</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结算日期</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">到期日期</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSettlements.map((settlement) => {
                    const typeInfo = getTypeInfo(settlement.settlement_type);
                    const statusInfo = getStatusInfo(settlement.status);
                    const TypeIcon = typeInfo.icon;
                    
                    return (
                      <tr key={settlement.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {settlement.settlement_no}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex items-center gap-2">
                            <TypeIcon size={16} className={`text-${typeInfo.color}-500`} />
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              typeInfo.color === 'green' ? 'bg-green-100 text-green-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {typeInfo.label}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {settlement.counterparty_name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <span className={`${
                            settlement.settlement_type === 'receivable' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {settlement.settlement_type === 'payable' ? '-' : '+'}¥{settlement.amount?.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {settlement.settlement_date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {settlement.due_date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            statusInfo.color === 'green' ? 'bg-green-100 text-green-800' :
                            statusInfo.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {statusInfo.label}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handleEdit(settlement)}
                              className="text-blue-600 hover:text-blue-900"
                              title="编辑"
                            >
                              <Edit size={16} />
                            </button>
                            <button
                              onClick={() => handleDelete(settlement.id)}
                              className="text-red-600 hover:text-red-900"
                              title="删除"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* 添加/编辑结算记录模态框 */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {editingSettlement ? '编辑结算记录' : '新增结算记录'}
                </h3>
                
                <form onSubmit={handleSave} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">结算号 *</label>
                      <input
                        type="text"
                        value={formData.settlement_no}
                        onChange={(e) => setFormData({...formData, settlement_no: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">结算类型 *</label>
                      <select
                        value={formData.settlement_type}
                        onChange={(e) => setFormData({...formData, settlement_type: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      >
                        {typeOptions.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">对方名称 *</label>
                      <input
                        type="text"
                        value={formData.counterparty_name}
                        onChange={(e) => setFormData({...formData, counterparty_name: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">对方类型 *</label>
                      <select
                        value={formData.counterparty_type}
                        onChange={(e) => setFormData({...formData, counterparty_type: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      >
                        <option value="customer">客户</option>
                        <option value="supplier">供应商</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">金额 *</label>
                      <input
                        type="number"
                        value={formData.amount}
                        onChange={(e) => setFormData({...formData, amount: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">结算日期 *</label>
                      <input
                        type="date"
                        value={formData.settlement_date}
                        onChange={(e) => setFormData({...formData, settlement_date: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">到期日期</label>
                      <input
                        type="date"
                        value={formData.due_date}
                        onChange={(e) => setFormData({...formData, due_date: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">状态 *</label>
                      <select
                        value={formData.status}
                        onChange={(e) => setFormData({...formData, status: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      >
                        {statusOptions.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={3}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">参考号</label>
                    <input
                      type="text"
                      value={formData.reference_no}
                      onChange={(e) => setFormData({...formData, reference_no: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowAddModal(false)}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                    >
                      取消
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
                    >
                      保存
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Settlement;
