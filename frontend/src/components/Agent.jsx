import React, { useState, useRef, useEffect, useCallback } from 'react';
import { MessageSquare, StopCircle } from 'lucide-react';
import SimpleChatInput from './SimpleChatInput';
import Audit from './agent/invoice-audit/InvoiceAudit';
import AccountingVoucher from './agent/accounting-voucher/AccountingVoucher';
import FinancialConsultation from './agent/financial-consultation/FinancialConsultation';
import Message from './agent/common/Message';
import Toast from './agent/common/Toast';
import api, { backendBase } from '../api';
import { getActiveService, AI_SERVICE_TYPES } from '../types/aiConfig';

const Agent = ({
  setVouchers, setSubjects, setAssets, setStaffs,
  aiConfig, inSidebar = false, session, updateSession
}) => {
  const [messages, setMessages] = useState(session?.messages || []);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState(session?.id || null);
  const [toast, setToast] = useState(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [abortController, setAbortController] = useState(null);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [selectedFunction, setSelectedFunction] = useState('单据审核');

  const fileInputRef = useRef(null);
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // 显示Toast
  const showToast = (message, type = 'success') => {
    setToast({ message, type });
  };

  // 处理卡片数据（只渲染到消息区，确认后才加入工作区）
  const processCards = (cards) => {
    console.log('[DEBUG] processCards called, cards:', cards);
    if (!cards) return;
    // 只渲染未 skip 的卡片（支持skip: true或op: 'skip'都跳过）
    const validCards = cards.filter(card => !card.skip && card.op !== 'skip');
    if (validCards.length === 0) return;
    // 新增一条CARD类型消息，内容为卡片数组
    addMessage('', 'card', {
      cards: validCards.map(card => ({
        ...card,
        // 标记未确认
        _pending: true
      }))
    });
  };

  // 卡片确认后加入工作区
  const handleCardConfirm = (card, messageId, cardIdx) => {
    // 加入对应工作区
    if (card.type === 'subject') setSubjects(prev => [...prev, card.data || card.props || card]);
    if (card.type === 'asset') setAssets(prev => [...prev, card.data || card.props || card]);
    if (card.type === 'staff') setStaffs(prev => [...prev, card.data || card.props || card]);
    if (card.type === 'voucher') setVouchers(prev => [...prev, card.data || card.props || card]);
    // 消息区该卡片标记为已确认
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId && msg.type === 'card' && msg.cards) {
        const newCards = msg.cards.map((c, idx) => idx === cardIdx ? { ...c, _pending: false } : c);
        return { ...msg, cards: newCards };
      }
      return msg;
    }));
  };

  // 添加消息
  const addMessage = (content, type = 'assistant', extra = {}) => {
    const message = {
      id: Date.now() + Math.random(),
      content,
      type,
      timestamp: new Date(),
      ...extra
    };
    setMessages(prev => [...prev, message]);
    return message;
  };

  // 更新消息
  const updateMessage = (id, updates) => {
    setMessages(prev => prev.map(msg =>
      msg.id === id ? { ...msg, ...updates } : msg
    ));
  };

  // 配置智能体
  const configureAgent = async () => {
    try {
      console.log('[DEBUG] configureAgent called with aiConfig:', aiConfig);

      // 获取当前活动的AI服务
      const activeService = getActiveService(aiConfig);
      console.log('[DEBUG] activeService:', activeService);

      if (!activeService) {
        throw new Error('没有可用的AI服务配置');
      }

      // 根据服务类型构建请求头
      let headers = {
        'Content-Type': 'application/json'
      };

      if (activeService.type === AI_SERVICE_TYPES.OLLAMA) {
        headers['X-API-Key'] = '';
        headers['X-Base-URL'] = activeService.config.baseUrl;
        headers['X-Model'] = activeService.config.model;
        headers['X-Use-Ollama'] = 'true';
      } else if (activeService.type === AI_SERVICE_TYPES.LMSTUDIO) {
        // LMStudio 作为本地服务，不需要 API key
        headers['X-API-Key'] = 'not-needed';  // LMStudio 推荐的占位符
        headers['X-Base-URL'] = activeService.config.baseUrl;
        headers['X-Model'] = activeService.config.model;
        headers['X-Use-Ollama'] = 'false';
        headers['X-Service-Type'] = 'lmstudio';  // 明确指定服务类型
      } else {
        // OpenAI 兼容或其他服务
        headers['X-API-Key'] = activeService.config.apiKey || '';
        headers['X-Base-URL'] = activeService.config.baseUrl;
        headers['X-Model'] = activeService.config.model;
        headers['X-Use-Ollama'] = 'false';
      }

      console.log('[DEBUG] Request headers:', headers);

      const response = await fetch(`${backendBase}/agent/v2/configure`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          session_id: sessionId,
          supports_vision: activeService.supportsMultimodal || false
        })
      });

      const result = await response.json();
      if (result.success) {
        setSessionId(result.session_id);
        return true;
      } else {
        throw new Error(result.error || '配置失败');
      }
    } catch (error) {
      console.error('配置智能体失败:', error);
      showToast(error.message, 'error');
      return false;
    }
  };

  // 新增：多轮自洽处理
  const [context, setContext] = useState({});
  const [autoLooping, setAutoLooping] = useState(false);
  const autoLoopRef = useRef(false);

  // action 处理主函数
  const handleAgentAction = async (response, userInput) => {
    try {
      console.log('[DEBUG] handleAgentAction called', response);
      const { action, cards, answer, data, is_finished } = response;
      // 新增：只要有 answer 就渲染
      if (answer) {
        addMessage(answer, 'assistant');
      }
      switch (action) {
        case 'create_subject':
          await api.createSubject(data);
          showToast('科目创建成功');
          break;
        case 'update_asset':
          await api.updateAsset(data);
          showToast('资产信息已更新');
          break;
        case 'delete_staff':
          await api.deleteStaff(data);
          showToast('员工已删除');
          break;
        case 'query_supplier':
          const supplier = await api.querySupplier(data);
          showToast('供应商信息已查询');
          break;
        case 'create_voucher':
        case 'create_card':
          if (cards && cards.length > 0) {
            processCards(cards);
          }
          break;
        case 'answer_question':
          // answer 已展示，无需重复
          break;
        case 'fetch_subjects':
          const subjects = await api.getSubjects();
          setContext(prev => ({ ...prev, subjects }));
          await sendMessageWithContext(userInput, { ...context, subjects });
          break;
        case 'none':
          showToast('操作已完成');
          break;
        default:
          showToast('暂不支持该操作');
      }
      if (!is_finished) {
        // 你可以自动继续下一轮，或等待用户输入
      }
    } catch (error) {
      console.error('handleAgentAction error:', error);
      // 清除思考消息
      setMessages(prev => prev.filter(msg =>
        msg.type !== 'thinking' &&
        msg.type !== 'analysis' &&
        msg.type !== 'creating_cards'
      ));
      addMessage(`处理响应时发生错误: ${error.message}`, 'error');
      showToast('处理响应失败', 'error');
    }
  };

  // 封装带上下文的 sendMessage
  const sendMessageWithContext = async (content, extraContext = {}) => {
    if ((!content.trim() && !(extraContext.files && extraContext.files.length > 0)) || isLoading) return;
    // 配置智能体
    const configured = await configureAgent();
    if (!configured) return;
    // 添加用户消息
    if (content && content.trim()) {
      addMessage(content, 'user');
    } else if (extraContext.files && extraContext.files.length > 0) {
      addMessage('用户上传了文件', 'user');
    }
    setInput('');
    setIsLoading(true);
    setIsStreaming(true);
    // 插入思考动画
    const thinkingMsg = addMessage('', 'thinking');
    let thinkingMsgId = thinkingMsg.id;
    let analysisMsgId = null;
    let creatingCardsMsgId = null;
    let gotCards = false;
    let gotAnalysis = false;
    let lastAnalysis = '';
    let fullBuffer = '';
    let jsonCandidate = '';
    const controller = new AbortController();
    setAbortController(controller);
    try {
      console.log('[DEBUG] sendMessageWithContext called with aiConfig:', aiConfig);

      // 获取当前活动的AI服务
      const activeService = getActiveService(aiConfig);
      console.log('[DEBUG] activeService:', activeService);

      // 构建请求头
      let headers = {
        'Content-Type': 'application/json'
      };

      if (activeService) {
        if (activeService.type === AI_SERVICE_TYPES.OLLAMA) {
          headers['X-API-Key'] = '';
          headers['X-Base-URL'] = activeService.config.baseUrl;
          headers['X-Model'] = activeService.config.model;
          headers['X-Use-Ollama'] = 'true';
        } else if (activeService.type === AI_SERVICE_TYPES.LMSTUDIO) {
          // LMStudio 作为本地服务，不需要 API key
          headers['X-API-Key'] = 'not-needed';  // LMStudio 推荐的占位符
          headers['X-Base-URL'] = activeService.config.baseUrl;
          headers['X-Model'] = activeService.config.model;
          headers['X-Use-Ollama'] = 'false';
          headers['X-Service-Type'] = 'lmstudio';  // 明确指定服务类型
        } else {
          // OpenAI 兼容或其他服务
          headers['X-API-Key'] = activeService.config.apiKey || '';
          headers['X-Base-URL'] = activeService.config.baseUrl;
          headers['X-Model'] = activeService.config.model;
          headers['X-Use-Ollama'] = 'false';
        }
      }

      console.log('[DEBUG] Request headers:', headers);

      const response = await fetch(`${backendBase}/agent/v2/stream`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          content,
          session_id: sessionId,
          user_id: 'user',
          use_enhanced: true,
          supports_vision: activeService?.supportsMultimodal || false,
          files: extraContext.files || null,
          ...extraContext
        }),
        signal: controller.signal
      });
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        fullBuffer += chunk;
        jsonCandidate += chunk;
        try {
          const matches = jsonCandidate.match(/\{[\s\S]*\}/g);
          if (matches && matches.length > 0) {
            const last = matches[matches.length - 1];
            const result = JSON.parse(last);
            if (result.analysis && result.analysis !== lastAnalysis) {
              lastAnalysis = result.analysis;
              gotAnalysis = true;
              if (!analysisMsgId) {
                setMessages(prev => prev.filter(msg => msg.id !== thinkingMsgId));
                const analysisMsg = addMessage(result.analysis, 'analysis');
                analysisMsgId = analysisMsg.id;
              } else {
                updateMessage(analysisMsgId, { content: lastAnalysis });
              }
            }
            // 修正：流式中间阶段不处理 cards 字段，不渲染卡片
          }
        } catch (e) {
          // 不是完整JSON，忽略
        }
      }
      if (gotAnalysis && !gotCards) {
        const newThinkingMsg = addMessage('', 'thinking');
        thinkingMsgId = newThinkingMsg.id;
      }
      if (!fullBuffer.trim()) {
        // 清除思考消息
        setMessages(prev => prev.filter(msg => msg.type !== 'thinking'));
        addMessage('❌ 没有收到响应内容', 'error');
      } else {
        try {
          const result = JSON.parse(fullBuffer);
          if (result.success === false) {
            // 清除思考消息
            setMessages(prev => prev.filter(msg => msg.type !== 'thinking'));
            addMessage(`❌ ${result.error}`, 'error');
          } else {
            // 移除所有思考动画
            setMessages(prev => prev.filter(msg => msg.type !== 'thinking'));
            // 只在流式结束后统一处理 answer/card/action
            try {
              await handleAgentAction(result, content); // 只在这里处理 cards
            } catch (error) {
              console.error('handleAgentAction failed:', error);
              // 清除思考消息
              setMessages(prev => prev.filter(msg =>
                msg.type !== 'thinking' &&
                msg.type !== 'analysis' &&
                msg.type !== 'creating_cards'
              ));
              addMessage(`处理AI响应时发生错误: ${error.message}`, 'error');
            }
          }
        } catch (e) {
          // 清除思考消息
          setMessages(prev => prev.filter(msg => msg.type !== 'thinking'));
          if (analysisMsgId) {
            updateMessage(analysisMsgId, { content: fullBuffer });
          }
        }
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        addMessage(`发送失败: ${error.message}`, 'error');
        showToast('发送消息失败', 'error');
      } else {
        // 如果是用户主动停止，清除思考消息
        setMessages(prev => prev.filter(msg =>
          msg.type !== 'thinking' &&
          msg.type !== 'analysis' &&
          msg.type !== 'creating_cards'
        ));
      }
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
      setAbortController(null);
      setAutoLooping(false);
      autoLoopRef.current = false;
    }
  };

  // 清空对话
  const clearConversation = async () => {
    if (sessionId) {
      try {
        await fetch(`${backendBase}/agent/v2/history/${sessionId}`, {
          method: 'DELETE'
        });
      } catch (error) {
        console.error('清空对话历史失败:', error);
      }
    }
    setMessages([]);
    showToast('对话已清空');
  };

  // 重试消息
  const retryMessage = (messageId) => {
    const message = messages.find(m => m.id === messageId);
    if (message && message.type === 'assistant') {
      // 找到对应的用户消息并重新发送
      const messageIndex = messages.findIndex(m => m.id === messageId);
      const userMessage = messages[messageIndex - 1];
      if (userMessage && userMessage.type === 'user') {
        // 根据当前选择的功能调用相应的sendMessage
        if (selectedFunction === '单据审核') {
          auditHook.sendMessage(userMessage.content);
        } else if (selectedFunction === '记账凭证') {
          accountingVoucherHook.sendMessage(userMessage.content);
        } else if (selectedFunction === '财务咨询') {
          financialConsultationHook.sendMessage(userMessage.content);
        }
      }
    }
  };

  // 拖拽上传
  const handleDragOver = (e) => {
    e.preventDefault();
  };
  const handleDrop = (e) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    // 根据当前选择的功能调用相应的handleFileSelect
    if (selectedFunction === '单据审核') {
      auditHook.handleFileSelect(files);
    } else if (selectedFunction === '记账凭证') {
      accountingVoucherHook.handleFileSelect(files);
    } else if (selectedFunction === '财务咨询') {
      financialConsultationHook.handleFileSelect(files);
    }
  };

  // 粘贴图片上传
  useEffect(() => {
    const handlePaste = (e) => {
      if (e.clipboardData && e.clipboardData.files.length > 0) {
        const files = Array.from(e.clipboardData.files);
        // 根据当前选择的功能调用相应的handleFileSelect
        if (selectedFunction === '单据审核') {
          auditHook.handleFileSelect(files);
        } else if (selectedFunction === '记账凭证') {
          accountingVoucherHook.handleFileSelect(files);
        } else if (selectedFunction === '财务咨询') {
          financialConsultationHook.handleFileSelect(files);
        }
      }
    };
    window.addEventListener('paste', handlePaste);
    return () => window.removeEventListener('paste', handlePaste);
  }, [selectedFunction]);

  // 初始化功能模块
  const auditHook = Audit({
    messages,
    setMessages,
    input,
    setInput,
    isLoading,
    setIsLoading,
    isStreaming,
    setIsStreaming,
    sessionId,
    setSessionId,
    abortController,
    setAbortController,
    uploadedFiles,
    setUploadedFiles,
    aiConfig,
    addMessage,
    updateMessage,
    showToast,
    configureAgent,
    setContext,
    setAutoLooping,
    autoLoopRef
  });

  const accountingVoucherHook = AccountingVoucher({
    messages,
    setMessages,
    input,
    setInput,
    isLoading,
    setIsLoading,
    isStreaming,
    setIsStreaming,
    sessionId,
    setSessionId,
    abortController,
    setAbortController,
    uploadedFiles,
    setUploadedFiles,
    aiConfig,
    addMessage,
    updateMessage,
    showToast,
    configureAgent,
    setContext,
    setAutoLooping,
    autoLoopRef,
    sendMessageWithContext,
    processCards
  });

  const financialConsultationHook = FinancialConsultation({
    messages,
    setMessages,
    input,
    setInput,
    isLoading,
    setIsLoading,
    isStreaming,
    setIsStreaming,
    sessionId,
    setSessionId,
    abortController,
    setAbortController,
    uploadedFiles,
    setUploadedFiles,
    aiConfig,
    addMessage,
    updateMessage,
    showToast,
    configureAgent,
    setContext,
    setAutoLooping,
    autoLoopRef
  });

  // 根据当前选择的功能获取相应的hook
  const getCurrentHook = () => {
    switch (selectedFunction) {
      case '单据审核':
        return auditHook;
      case '记账凭证':
        return accountingVoucherHook;
      case '财务咨询':
        return financialConsultationHook;
      default:
        return auditHook;
    }
  };

  const currentHook = getCurrentHook();

  return (
    <div className="flex flex-col h-full bg-white"
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {/* 头部 - 只在非侧边栏模式下显示 */}
      {!inSidebar && (
        <div className="flex items-center justify-between p-4 border-b bg-gray-50">
          <div className="flex items-center gap-2">
            <MessageSquare className="text-blue-600" size={20} />
            <h2 className="font-semibold">智能助手</h2>
            {sessionId && typeof sessionId === 'string' && (
              <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
                {sessionId.slice(0, 8)}...
              </span>
            )}
          </div>
        </div>
      )}

      {/* 消息列表 */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-6 space-y-6"
      >
        {messages.length === 0 && (
          <div className="flex-1 flex items-center justify-center min-h-[60vh]">
            <div className="text-center opacity-60 select-none pointer-events-none">
              <div className="mb-8">
                <MessageSquare size={80} className="text-gray-400 mx-auto mb-6" strokeWidth={1.5} />
              </div>
              <h3 className="text-2xl font-medium text-gray-500 mb-4 tracking-wide">
                开始与智能助手对话
              </h3>
              <p className="text-gray-400 text-lg font-normal leading-relaxed max-w-md mx-auto">
                发送消息或上传文档<br />
                获得专业的会计服务支持
              </p>
            </div>
          </div>
        )}

        {messages.map((message, index) => (
          <div
            key={message.id}
          >
            <Message
              message={message}
              onRetry={retryMessage}
              handleCardConfirm={handleCardConfirm}
              sendMessageWithContext={sendMessageWithContext}
            />
          </div>
        ))}

        <div ref={messagesEndRef} />
      </div>

      {/* 现代化输入区域 */}
      <div className="p-6 border-t border-white/10 bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-sm">
        {/* 功能提示 */}
        {currentHook.renderFunctionIndicator()}

        <SimpleChatInput
          value={input}
          onChange={setInput}
          onSend={() => currentHook.sendMessage(input)}
          onFileSelect={currentHook.handleFileSelect}
          uploadedFiles={uploadedFiles}
          onRemoveFile={(index) => setUploadedFiles(prev => prev.filter((_, i) => i !== index))}
          isLoading={isLoading}
          isStreaming={isStreaming}
          onStop={currentHook.stopGeneration}
          placeholder={currentHook.placeholder}
          disabled={false}
          selectedFunction={selectedFunction}
          onFunctionChange={setSelectedFunction}
        />
      </div>

      {/* Toast */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export default Agent;
