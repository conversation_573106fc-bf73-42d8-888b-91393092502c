import React, { useState, useEffect } from 'react';
import { Bot, MessageSquare, Plus, Search, Settings, User, Home, FileText, BarChart3, Calculator, DollarSign, Receipt, Briefcase, Users, Building, Cog, Sparkles, Minus, Square, X, Database, Server, Wrench, ChevronDown, Cloud, HardDrive, LayoutGrid, Lock } from 'lucide-react';
import StatusBar from './StatusBar';
import SettingsManager from '../settings/SettingsManager';
import RAGManagement from '../settings/RAGManagement';
import MCPManagement from '../settings/MCPManagement';
import WorkflowManagement from '../settings/WorkflowManagement';
import { getActiveService, getEnabledServices, AI_SERVICE_TYPES } from '../../types/aiConfig';

const ElectronMainLayout = ({ children, activeFeature, onFeatureSelect, features, sessions, activeSessionId, onSessionSelect, onNewSession, aiConfig, setAiConfig, backendBase, isWorkspaceVisible }) => {
  const isElectron = typeof window !== 'undefined' && window.electronAPI;
  const [activeSetting, setActiveSetting] = useState('settings');
  const [showAIServiceSelector, setShowAIServiceSelector] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [isLocked, setIsLocked] = useState(false);
  const [activeTab, setActiveTab] = useState('assistant');

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showAIServiceSelector && !event.target.closest('.ai-service-selector')) {
        setShowAIServiceSelector(false);
      }
      if (showUserMenu && !event.target.closest('.user-menu-container')) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showAIServiceSelector, showUserMenu]);

  // 监听从RAG管理跳转到微工作流的事件
  useEffect(() => {
    const handleNavigateToWorkflow = (event) => {
      console.log('ElectronMainLayout navigate-to-workflow event received:', event.detail);
      // 切换到设置功能
      console.log('Switching to settings feature');
      onFeatureSelect('settings');
      // 设置活动设置为内部工具（微工作流）
      console.log('Setting active setting to internal-tools');
      setActiveSetting('internal-tools');
      // 清除左侧tab页的focus
      console.log('Clearing active tab');
      setActiveTab('');
    };

    window.addEventListener('navigate-to-workflow', handleNavigateToWorkflow);
    
    return () => {
      window.removeEventListener('navigate-to-workflow', handleNavigateToWorkflow);
    };
  }, [onFeatureSelect, setActiveSetting, setActiveTab]);

  // 获取当前活动的AI服务
  const getActiveAIService = () => {
    return getActiveService(aiConfig);
  };

  // 获取所有启用的AI服务
  const getEnabledAIServices = () => {
    return getEnabledServices(aiConfig);
  };

  // 切换AI服务
  const handleAIServiceSwitch = (serviceId) => {
    console.log('[DEBUG] handleAIServiceSwitch called with serviceId:', serviceId);
    console.log('[DEBUG] current aiConfig:', aiConfig);

    const service = aiConfig.services?.find(s => s.id === serviceId);
    if (!service || !service.enabled) {
      console.log('[DEBUG] Service not found or not enabled:', serviceId);
      return;
    }

    const updatedConfig = {
      ...aiConfig,
      activeServiceId: serviceId
    };
    console.log('[DEBUG] updatedConfig:', updatedConfig);

    setAiConfig(updatedConfig);

    // 保存到localStorage
    localStorage.setItem('aiConfig', JSON.stringify(updatedConfig));
    console.log('[DEBUG] saved to localStorage');

    // 关闭选择器
    setShowAIServiceSelector(false);
  };

  // 获取当前AI服务名称
  const getCurrentAIServiceName = () => {
    const activeService = getActiveAIService();
    if (activeService) {
      return activeService.name;
    }
    return '未配置';
  };

  // 获取服务类型图标
  const getServiceTypeIcon = (type) => {
    switch (type) {
      case AI_SERVICE_TYPES.OLLAMA:
        return <HardDrive size={16} />;
      case AI_SERVICE_TYPES.OPENAI_COMPATIBLE:
        return <Cloud size={16} />;
      case AI_SERVICE_TYPES.LMSTUDIO:
        return <Database size={16} />;
      default:
        return <Sparkles size={16} />;
    }
  };

  // 窗口控制函数
  const handleMinimize = () => {
    if (isElectron) {
      window.electronAPI.minimize();
    }
  };

  const handleMaximize = () => {
    if (isElectron) {
      window.electronAPI.maximize();
    }
  };

  const handleClose = () => {
    if (isElectron) {
      window.electronAPI.close();
    }
  };
  const [assistants, setAssistants] = useState([
    { id: 1, name: 'Default Assistant', avatar: '😊', active: true, lastMessage: '你好，我是智能助手，你可以向我提问任何问题' },
    { id: 2, name: 'Default Assistant', avatar: '😊', active: false, lastMessage: '' }
  ]);

  const [searchQuery, setSearchQuery] = useState('');

  const addAssistant = () => {
    const newAssistant = {
      id: Date.now(),
      name: 'Default Assistant',
      avatar: '😊',
      active: false,
      lastMessage: ''
    };
    setAssistants(prev => [...prev, newAssistant]);
  };

  const setActiveAssistant = (id) => {
    setAssistants(prev => prev.map(assistant => ({
      ...assistant,
      active: assistant.id === id
    })));
  };

  // 功能图标映射
  const featureIcons = {
    agent: Bot,
    approval: FileText,
    voucher: Receipt,
    bookkeeping: FileText,
    report: BarChart3,
    settlement: Calculator,
    asset: Briefcase,
    invoice: Receipt,
    cashier: DollarSign,
    salary: Users,
    tax: Calculator,
    subject: FileText,
    roleStaff: Users,
    company: Building,
    settings: Cog
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      <div className="flex flex-1 overflow-hidden">
        {/* 左侧边栏 - Cherry Studio 风格 */}
        <div className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
          {/* 顶部标签页 */}
          <div className="h-14 flex border-b border-gray-200 bg-white">
            <button
              onClick={() => {
                setActiveTab('assistant');
                onFeatureSelect('agent'); // 激活智能体功能
                // 如果有会话，默认选中第一个会话
                if (sessions && sessions.length > 0 && onSessionSelect) {
                  onSessionSelect(sessions[0].id);
                }
              }}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'assistant' && activeFeature !== 'settings'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50/50'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              助手
            </button>
            <button
              onClick={() => {
                setActiveTab('functions');
                // 默认选中第一个功能选项
                const firstFeature = Object.entries(features)
                  .filter(([key]) => key !== 'settings' && key !== 'agent')[0];
                if (firstFeature) {
                  onFeatureSelect(firstFeature[0]);
                }
              }}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'functions' && activeFeature !== 'settings'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50/50'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              功能
            </button>
          </div>

          {/* 搜索框 - 与右侧状态栏对齐 */}
          <div className="h-14 px-4 bg-white border-b border-gray-200 flex items-center">
            <div className="relative w-full">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={
                  activeTab === 'functions' ? '搜索功能...' :
                  activeTab === 'assistant' ? '搜索助手...' :
                  '搜索设置...'
                }
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50"
              />
            </div>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 overflow-y-auto bg-gray-50/50">
            {/* 当设置功能激活时显示设置选项列表 */}
            {activeFeature === 'settings' && (
              <div className="p-2">
                <div
                  onClick={() => {
                    setActiveSetting('settings');
                    onFeatureSelect('settings');
                  }}
                  className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                    activeFeature === 'settings' && activeSetting === 'settings'
                      ? 'bg-blue-50 border border-blue-200 text-blue-700'
                      : 'hover:bg-white hover:shadow-sm text-gray-700'
                  }`}
                >
                  <Cog size={18} className="flex-shrink-0" />
                  <span className="text-sm font-medium">AI服务器</span>
                </div>
                <div
                  onClick={() => {
                    setActiveSetting('rag');
                    onFeatureSelect('settings');
                  }}
                  className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                    activeFeature === 'settings' && activeSetting === 'rag'
                      ? 'bg-blue-50 border border-blue-200 text-blue-700'
                      : 'hover:bg-white hover:shadow-sm text-gray-700'
                  }`}
                >
                  <Database size={18} className="flex-shrink-0" />
                  <span className="text-sm font-medium">RAG管理</span>
                </div>
                <div
                  onClick={() => {
                    setActiveSetting('internal-tools');
                    onFeatureSelect('settings');
                  }}
                  className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                    activeFeature === 'settings' && activeSetting === 'internal-tools'
                      ? 'bg-blue-50 border border-blue-200 text-blue-700'
                      : 'hover:bg-white hover:shadow-sm text-gray-700'
                  }`}
                >
                  <Wrench size={18} className="flex-shrink-0" />
                  <span className="text-sm font-medium">微工作流</span>
                </div>
                <div
                  onClick={() => {
                    setActiveSetting('mcp');
                    onFeatureSelect('settings');
                  }}
                  className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                    activeFeature === 'settings' && activeSetting === 'mcp'
                      ? 'bg-blue-50 border border-blue-200 text-blue-700'
                      : 'hover:bg-white hover:shadow-sm text-gray-700'
                  }`}
                >
                  <Server size={18} className="flex-shrink-0" />
                  <span className="text-sm font-medium">MCP服务</span>
                </div>
              </div>
            )}

            {activeTab === 'assistant' && (
              <div className="p-2">
                {/* 始终显示会话列表 */}
                {sessions && sessions.map((session) => {
                  const lastMessage = session.messages.length > 0
                    ? session.messages[session.messages.length - 1].content
                    : '新会话';
                  
                  return (
                    <div
                      key={session.id}
                      onClick={() => onSessionSelect && onSessionSelect(session.id)}
                      className={`flex items-start space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                        session.id === activeSessionId
                          ? 'bg-blue-50 border border-blue-200'
                          : 'hover:bg-white hover:shadow-sm'
                      }`}
                    >
                      <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center text-lg flex-shrink-0">
                        😊
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {session.name}
                        </div>
                        <div className="text-xs text-gray-400 mt-1 line-clamp-2">
                          {lastMessage}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {activeTab === 'functions' && (
              <div className="p-2">
                {Object.entries(features)
                  .filter(([key]) => key !== 'settings' && key !== 'agent') // 过滤掉设置功能和智能体
                  .map(([key, feature]) => {
                  const IconComponent = featureIcons[key] || FileText;
                  return (
                    <div
                      key={key}
                      onClick={() => {
                        onFeatureSelect(key);
                        setActiveTab('functions'); // 确保点击功能选项时，功能tab保持激活状态
                      }}
                      className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                        activeFeature === key
                          ? 'bg-blue-50 border border-blue-200 text-blue-700'
                          : 'hover:bg-white hover:shadow-sm text-gray-700'
                      }`}
                    >
                      <IconComponent size={18} className="flex-shrink-0" />
                      <span className="text-sm font-medium">{feature.name}</span>
                    </div>
                  );
                })}
              </div>
            )}

          </div>

          {/* 底部添加按钮 */}
          {activeTab === 'assistant' && (
            <div className="p-4 border-t border-gray-200 bg-white">
              <button
                onClick={onNewSession}
                className="w-full flex items-center justify-center space-x-2 px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-50 rounded-lg transition-colors border border-gray-200"
              >
                <Plus size={16} />
                <span>新建对话</span>
              </button>
            </div>
          )}
        </div>

        {/* 主内容区域 */}
        <div className="flex-1 flex flex-col overflow-hidden bg-white">
          {/* 顶部状态栏 - 类似 Cherry Studio */}
          <div className="h-14 border-b border-gray-200 flex items-center justify-between px-6 bg-white/95 backdrop-blur-sm drag-region relative z-[50]">
            <div className="flex items-center space-x-4">
              <div className="relative ai-service-selector z-[99999]">
                <div
                  className="flex items-center space-x-3 px-3 py-1.5 bg-gray-100 rounded-lg no-drag cursor-pointer hover:bg-gray-200 transition-colors"
                  onClick={() => setShowAIServiceSelector(!showAIServiceSelector)}
                >
                  <Sparkles size={14} className="text-blue-500" />
                  <div className="w-1.5 h-1. bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-700 font-medium">智能助手</span>
                  <span className="text-xs text-gray-400">|</span>
                  <span className="text-xs text-gray-500">{getCurrentAIServiceName()}</span>
                  <ChevronDown size={14} className="text-gray-500" />
                </div>
                
                {/* AI服务选择器下拉菜单 */}
                {showAIServiceSelector && (
                  <div className="absolute top-full left-0 mt-2 w-96 bg-white rounded-xl shadow-lg border border-gray-200 z-[999999] no-drag">
                    <div className="p-2">
                      <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 py-1">
                        选择AI服务
                      </div>

                      {/* 显示所有启用的AI服务 */}
                      {getEnabledServices(aiConfig).map((service) => {
                        const isActive = service.id === aiConfig.activeServiceId;
                        const serviceIcon = getServiceTypeIcon(service.type);

                        return (
                          <div
                            key={service.id}
                            className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors mb-1 ${
                              isActive
                                ? 'bg-blue-50 border border-blue-200'
                                : 'hover:bg-gray-50'
                            }`}
                            onClick={() => handleAIServiceSwitch(service.id)}
                          >
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              isActive ? 'bg-blue-100' : 'bg-gray-200'
                            }`}>
                              <div className={isActive ? 'text-blue-600' : 'text-gray-500'}>
                                {serviceIcon}
                              </div>
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <div className="text-sm font-medium text-gray-900">{service.name}</div>
                                {service.supportsMultimodal && (
                                  <span className="px-1.5 py-0.5 text-xs bg-purple-100 text-purple-700 rounded">
                                    多模态
                                  </span>
                                )}
                              </div>
                              <div className="text-xs text-gray-500">{service.config.model}</div>
                            </div>
                            {isActive && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            )}
                          </div>
                        );
                      })}

                      {/* 当没有启用的服务时显示提示 */}
                      {getEnabledServices(aiConfig).length === 0 && (
                        <div className="p-3 text-center">
                          <div className="text-sm text-gray-500 mb-2">暂无可用的AI服务</div>
                          <div className="text-xs text-gray-400">请先在设置中启用AI服务</div>
                        </div>
                      )}
                      
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <button
                          className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                          onClick={() => {
                            setActiveSetting('settings');
                            setActiveTab('');
                            onFeatureSelect('settings');
                            setShowAIServiceSelector(false);
                          }}
                        >
                          <Settings size={14} />
                          <span>AI服务设置</span>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-1 no-drag">
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                <Search size={18} />
              </button>
              <button
                onClick={() => {
                  // 触发工作区显示/隐藏事件
                  if (typeof window !== 'undefined') {
                    const event = new CustomEvent('toggle-workspace-visibility');
                    window.dispatchEvent(event);
                  }
                }}
                className={`p-2 rounded-lg transition-colors ${
                  isWorkspaceVisible
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                }`}
                title={isWorkspaceVisible ? "隐藏工作区" : "显示工作区"}
              >
                <LayoutGrid size={18} />
              </button>
              <button
                onClick={() => {
                  setActiveSetting('settings'); // 默认选中AI服务器设置
                  setActiveTab(''); // 清除左侧tab页的focus
                  onFeatureSelect('settings'); // 激活设置功能
                }}
                className={`p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors ${
                  activeFeature === 'settings' ? 'text-blue-600 bg-blue-50' : ''
                }`}
              >
                <Settings size={18} />
              </button>
              
              {/* 用户信息按钮 - Switch 开关样式 */}
              <div className="relative user-menu-container ml-2">
                {/* 左右滑动开关 */}
                <div
                  className={`relative w-14 h-7 flex items-center rounded-full p-1 cursor-pointer transition-colors duration-150 ${
                    isLocked ? 'bg-red-400' : 'bg-green-400'
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    const rect = e.currentTarget.getBoundingClientRect();
                    const clickX = e.clientX - rect.left;
                    const isLeftSide = clickX < rect.width / 2;
                    
                    if (isLocked && isLeftSide) {
                      // 锁定状态下点击左侧 - 解锁
                      setIsLocked(false);
                    } else if (!isLocked && !isLeftSide) {
                      // 未锁定状态下点击右侧 - 锁定
                      setIsLocked(true);
                    } else if (!isLocked && isLeftSide) {
                      // 未锁定状态下点击左侧 - 显示用户菜单
                      setShowUserMenu(!showUserMenu);
                    }
                  }}
                  title={isLocked ? "点击左侧解锁" : "点击左侧查看用户信息，点击右侧锁定"}
                >
                  <div
                    className={`bg-white w-5 h-5 rounded-full shadow-md transform transition-transform duration-150 flex items-center justify-center ${
                      isLocked ? 'translate-x-7' : 'translate-x-0'
                    }`}
                  >
                    {isLocked ? (
                      <Lock size={12} className="text-red-500" />
                    ) : (
                      <User size={12} className="text-green-500" />
                    )}
                  </div>
                </div>
                
                {/* 用户信息下拉菜单 - 只在未锁定状态下显示 */}
                {showUserMenu && !isLocked && (
                  <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                    <div className="p-4 border-b border-gray-200">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <User size={20} className="text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">用户名称</p>
                          <p className="text-sm text-gray-500"><EMAIL></p>
                        </div>
                      </div>
                    </div>
                    <div className="py-1">
                      <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                        <User size={16} />
                        <span>个人资料</span>
                      </button>
                      <button className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                        <Cog size={16} />
                        <span>账户设置</span>
                      </button>
                      <div className="border-t border-gray-200 my-1"></div>
                      <button className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                        <span>注销</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
              
              {/* 窗口控制按钮 */}
              {isElectron && (
                <>
                  <div className="w-px h-6 bg-gray-300 mx-2"></div>
                  <button
                    onClick={handleMinimize}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="最小化"
                  >
                    <Minus size={16} />
                  </button>
                  <button
                    onClick={handleMaximize}
                    className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                    title="最大化"
                  >
                    <Square size={16} />
                  </button>
                  <button
                    onClick={handleClose}
                    className="p-2 text-gray-400 hover:text-white hover:bg-red-500 rounded-lg transition-colors"
                    title="关闭"
                  >
                    <X size={16} />
                  </button>
                </>
              )}
            </div>
          </div>

          {/* 主内容 */}
          <div className="flex-1 overflow-hidden">
            {activeFeature === 'settings' ? (
              <div className="h-full">
                {activeSetting === 'settings' && (
                  <SettingsManager
                    activeSetting={activeSetting}
                    aiConfig={aiConfig}
                    setAiConfig={setAiConfig}
                    backendBase={backendBase}
                  />
                )}
                {activeSetting === 'rag' && (
                  <div className="p-8 h-full overflow-hidden">
                    <RAGManagement
                      backendBase={backendBase}
                      aiConfig={aiConfig}
                    />
                  </div>
                )}
                {activeSetting === 'internal-tools' && (
                  <div className="p-8 h-full">
                    <WorkflowManagement
                      backendBase={backendBase}
                      aiConfig={aiConfig}
                    />
                  </div>
                )}
                {activeSetting === 'mcp' && (
                  <div className="p-8 h-full">
                    <MCPManagement />
                  </div>
                )}
              </div>
            ) : (
              children
            )}
          </div>
        </div>
      </div>
      
      {/* 底部状态栏 - 全页面宽度 */}
      <StatusBar />
    </div>
  );
};

export default ElectronMainLayout;