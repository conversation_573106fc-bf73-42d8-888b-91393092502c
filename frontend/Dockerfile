# 使用 Node.js 20 作为基础镜像
FROM node:20-alpine

# 设置工作目录
WORKDIR /app

# 配置使用中国国内源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装必要的系统依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    linux-headers \
    udev

# 配置 npm 使用官方镜像源
RUN npm config set registry https://registry.npmjs.org

# 使用 apk 安装 yarn
RUN apk add --no-cache yarn

# 配置yarn使用官方源
RUN yarn config set registry https://registry.yarnpkg.com

# 复制 package.json 和 yarn.lock
COPY package.json yarn.lock ./

# 安装所有依赖（包括开发依赖）
RUN yarn install

# 暴露端口
EXPOSE 5173

# 启动开发服务器
CMD ["yarn", "dev", "--", "--host", "0.0.0.0"]